#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	# Use the embedded font.
};
bugreport:
{
	btn_continue = "Cerca una soluzione online e chiudi il gioco.";
	btn_exit = "Chiudi il gioco.";
	btn_hidedetails = "Nascondi dettagli";
	btn_showdetails = "Visualizza dettagli";
	chk_sendreport = "Invia segnalazione errore";
	error_code = "Codice errore:";
	lbl_body1 = "Si è verificato un errore nell'avvio del gioco";
	lbl_body2 = "Aiutaci segnalando il problema.";
	lbl_body3 = "Easy Anti-Cheat può cercare una soluzione online e aiutarti a risolvere il problema.";
	lbl_header = "Impossibile avviare il gioco";
	title = "Errore avvio";
};
game_error:
{
	error_catalogue_corrupted = "Catalogo di hash di Easy Anti-Cheat danneggiato";
	error_catalogue_not_found = "Indice EAC non trovato";
	error_certificate_revoked = "Certificato revocato indice EAC";
	error_corrupted_memory = "Memoria danneggiata";
	error_corrupted_network = "Pacchetto di comunicazione danneggiato";
	error_file_forbidden = "File di gioco sconosciuto";
	error_file_not_found = "File necessario assente";
	error_file_version = "Versione file sconosciuta";
	error_module_forbidden = "Modulo vietato";
	error_system_configuration = "Configurazione di sistema proibita";
	error_system_version = "File di sistema non affidabile";
	error_tool_forbidden = "Strumento vietato";
	error_violation = "Errore anti-cheat interno.";
	error_virtual = "Impossibile eseguire con macchina virtuale.";
	peer_client_banned = "Anti-cheat peer escluso.";
	peer_heartbeat_rejected = "Anti-cheat peer rifiutato.";
	peer_validated = "Verifica anti-cheat peer completata.";
	peer_validation_failed = "Verifica anti-cheat peer non riuscita.";
	executable_not_hashed = "Impossibile trovare l'eseguibile del gioco nel catalogo.";
};
launcher:
{
	btn_cancel = "Annulla";
	btn_exit = "Esci";
	error_cancel = "Avvio annullato";
	error_filenotfound = "File non trovato";
	error_init = "Errore di inizializzazione";
	error_install = "Errore installazione";
	error_launch = "Errore avvio";
	error_nolib = "Impossibile caricare la libreria Easy Anti-Cheat";
	loading = "CARICAMENTO IN CORSO";
	wait = "Attendere";
	initializing = "INIZIALIZZAZIONE IN CORSO";
	success_waiting_for_game = "IN ATTESA DEL GIOCO";
	success_closing = "Successo";
	network_error = "Errore di rete";
	error_no_settings_file = "{0} non trovato";
	error_invalid_settings_format = "{0} non ha un formato JSON valido";
	error_missing_required_field = "Campo obbligatorio ({1}) mancante in {0}";
	error_invalid_eos_identifier = "{0} contiene un identificatore EOS non valido: ({1})";
	download_progress = "Progresso del download: {0}";
};
launcher_error:
{
	error_already_running = "Un'applicazione che usa Easy Anti-Cheat è già in esecuzione! {0}";
	error_application = "Il client del gioco ha rilevato un errore di applicazione. Codice errore: {0}";
	error_bad_exe_format = "Sistema operativo a 64 bit necessario";
	error_bitset_32 = "Usare la versione 32-bit del gioco";
	error_bitset_64 = "Usare la versione 64-bit del gioco";
	error_cancelled = "Operazione annullata dall'utente";
	error_certificate_validation = "Errore di convalida del certificato di firma del codice di Easy Anti-Cheat";
	error_connection = "Connessione alla rete di distribuzione contenuti non riuscita!";
	error_debugger = "Debugger individuato. Rimuoverlo e riprovare.";
	error_disk_space = "Spazio insufficiente sul disco fisso.";
	error_dns = "Tentativo di risoluzione DNS per la rete di distribuzione contenuti non riuscito!";
	error_dotlocal = "Reindirizzamento del DLL DotLocal rilevato.";
	error_dotlocal_instructions = "Eliminare il file seguente";
	error_file_not_found = "File non trovato:";
	error_forbidden_tool = "Chiudere {0} prima di avviare il gioco";
	error_forbidden_driver = "Scarica {0} prima di avviare il gioco";
	error_generic = "Errore imprevisto.";
	error_kernel_debug = "Easy Anti-Cheat non può essere avviato se viene attivato il debugging del kernel";
	error_kernel_dse = "Easy Anti-Cheat non può essere eseguito se l'imposizione firma driver è stata disattivata";
	error_kernel_modified = "Modifica kernel di Windows rilevata";
	error_library_load = "Impossibile caricare la libreria Easy Anti-Cheat";
	error_memory = "Memoria non sufficiente per avviare il gioco";
	error_module_load = "Impossibile caricare il modulo anti-cheat";
	error_patched = "Programma di caricamento patch di Windows rilevato";
	error_process = "Impossibile creare la procedura";
	error_process_crash = "Il processo è terminato improvvisamente";
	error_safe_mode = "Easy Anti-Cheat non può essere eseguito nella modalità provvisoria di Windows";
	error_socket = "Qualcosa impedisce all'applicazione di accedere a Internet!";
	error_ssl = "Errore di connessione SSL con il servizio CDN!";
	error_start = "Impossibile avviare il gioco";
	error_uncpath_forbidden = "Impossibile eseguire il gioco attraverso una condivisione di rete. (percorso UNC).";
	error_connection_failed = "Connessione fallita: ";
	error_missing_game_id = "ID del gioco non trovato";
	error_dns_resolve_failed = "Risoluzione DNS per proxy non riuscita";
	error_dns_connection_failed = "Connessione alla rete di distribuzione dei contenuti non riuscita! Codice curl: {0}!";
	error_http_response = "Codice di risposta HTTP: {0} Codice curl: {1}";
	error_driver_handle = "Errore inaspettato (impossibile aprire l'handle del driver)";
	error_incompatible_service = "Un servizio di Easy Anti-Cheat non compatibile è già in esecuzione. Chiudi gli altri giochi aperti o esegui il riavvio.";
	error_incompatible_driver_version = "Una versione del driver di Easy Anti-Cheat non compatibile è già in esecuzione. Chiudi gli altri giochi aperti o esegui il riavvio.";
	error_another_launcher = "Errore inaspettato (un altro launcher è già in esecuzione)";
	error_game_running = "Errore inaspettato (gioco già in esecuzione)";
	error_patched_boot_loader = "Rilevato caricatore di avvio di Windows con patch (protezione patch kernel disabilitata)";
	error_unknown_process = "Client del gioco non riconosciuto. Impossibile continuare.";
	error_unknown_game = "Gioco non configurato. Impossibile continuare.";
	error_win7_required = "È necessario Windows 7 o successivo.";
	success_initialized = "Easy Anti-Cheat inizializzato";
	success_loaded = "Easy Anti-Cheat caricato in gioco";
	error_create_process = "Impossibile creare il processo: {0}";
	error_create_thread = "Impossibile creare il thread in background!";
	error_disallowed_cdn_path = "Errore imprevisto. (URL CDN errato)";
	error_empty_executable_field = "Non è stato fornito il percorso del binario del gioco.";
	error_failed_path_query = "Impossibile trovare il percorso";
	error_failed_to_execute = "Impossibile avviare il processo.";
	error_game_binary_is_directory = "L'eseguibile di destinazione è una cartella!";
	error_game_binary_is_dot_app = "L'eseguibile di destinazione è una cartella, scegli il binario all'interno del file .app!";
	error_game_binary_not_found = "Binario del gioco non trovato: '{0}'";
	error_game_binary_not_found_wine = "Impossibile trovare il binario del gioco (Wine)";
	error_game_security_violation = "Violazione della sicurezza {0}";
	error_generic_ex = "Errore imprevisto. {0}";
	error_instance_count_limit = "Numero massimo di istanze di gioco simultanee raggiunto!";
	error_internal = "Errore interno!";
	error_invalid_executable_path = "Percorso dell'eseguibile non valido!";
	error_memory_ex = "Memoria insufficiente per iniziare il gioco {0}";
	error_missing_binary_path = "Percorso dell'eseguibile non trovato.";
	error_missing_directory_path = "Percorso della cartella di lavoro non trovato.";
	error_module_initialize = "Inizializzazione del modulo non riuscita con {0}";
	error_module_loading = "Impossibile caricare il modulo anti-cheat.";
	error_set_environment_variables = "Impossibile impostare variabili d'ambiente per il processo.";
	error_unrecognized_blacklisted_driver = "N/A è stato rilevato. Rimuovilo e prova di nuovo.";
	error_unsupported_machine_arch = "Architettura dell'host non supportata. ({0})";
	error_working_directory_not_found = "La cartella di lavoro non esiste.";
	error_x8664_required = "SO non supportato. È richiesta la versione Windows a 64 bit (x86-64).";
	warn_module_download_size = "Dimensione di risposta HTTP: {0}. Avvio in modalità client nulla.";
	warn_vista_deprecation = "Easy Anti-Cheat deve terminare il supporto per Windows Vista a ottobre 2020 poiché non è più possibile creare firme di codice compatibili. Visita https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Impossibile convalidare la configurazione dell'anti-cheat.";
	warn_win7_update_required = "Esegui gli aggiornamenti di Windows, il tuo sistema non dispone del supporto fondamentale per la firma del codice SHA-2 richiesto entro ottobre 2020. Visita https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Aggiornamento del servizio Easy Anti-Cheat scaduto."
	error_launch_ex = "Errore avvio {0}";
};
setup:
{
	btn_finish = "Finisci";
	btn_install = "Installa ora";
	btn_repair = "Riparazione";
	btn_uninstall = "Disinstalla";
	epic_link = "© Epic Games, Inc";
	install_progress = "Installazione in corso...";
	install_success = "Installato";
	licenses_link = "Licenze";
	privacy_link = "Privacy";
	repair_progress = "Riparazione in corso...";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "Disinstallazione in corso...";
	uninstall_success = "Disinstallato";
};
setup_error:
{
	error_cancelled = "Operazione annullata dall'utente";
	error_encrypted = "La cartella di installazione di Easy Anti-Cheat è stata crittografata";
	error_intro = "Impostazione Easy Anti-Cheat non riuscita";
	error_not_installed = "Easy Anti-Cheat non installato.";
	error_registry = "Copia dei file eseguibili del servizio non riuscita";
	error_rights = "Privilegi non sufficienti";
	error_service = "Impossibile creare servizio";
	error_service_ex = "Impossibile creare servizio {0}";
	error_system = "Accesso a System32 negato";
};