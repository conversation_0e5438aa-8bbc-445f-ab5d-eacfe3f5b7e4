__request   =   {
   __select   =   {
      __select   =   {
         __where   =   {
            __type   =   "^(mr_node_anim_with_events|mr_node_ecs_anim_with_events)$"
         }
         __groupByTag   =   "Container"
         __type   =   "modelRecursive"
      }
      __showOnlyChildren   =   True
      __convert   =   {
         __type   =   "string"
         __family   =   ".mr"
      }
      __type   =   "brand"
   }
   __showOnlyChildren   =   True
   __format   =   "{value}"
   __tag   =   "neoNetworkName"
   __type   =   "modelTag"
}
__type   =   "linkBrowser"

