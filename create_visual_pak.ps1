# Space Marine 2 - Create Visual Effects PAK
# This creates a proper PAK file for the visual modifications

Write-Host "Creating Visual Effects PAK..." -ForegroundColor Green

# Create temporary directory structure
$tempDir = "temp_visual_mod"
$pakStructure = @(
    "$tempDir\ssl\weapons",
    "$tempDir\effects\weapons",
    "$tempDir\audio\weapons"
)

# Clean and create directories
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}

foreach ($dir in $pakStructure) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

# Copy SSL files
Copy-Item "client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso" "$tempDir\ssl\weapons\" -Force
Copy-Item "client_pc\root\local\ssl\weapons\lightning_sword_complete.sso" "$tempDir\ssl\weapons\" -Force

# Create effect definition files
$flameEffects = @"
// Flamethrower Effects
fx_flamethrower_muzzle_burst = {
    type = "particle_system"
    texture = "flame_burst.dds"
    lifetime = 0.5
    color = [1.0, 0.6, 0.2, 1.0]
}

fx_flame_stream_continuous = {
    type = "particle_stream"
    texture = "flame_stream.dds"
    lifetime = 0.6
    velocity = 30.0
}
"@

$lightningEffects = @"
// Lightning Effects  
fx_lightning_arc_trail = {
    type = "particle_system"
    texture = "lightning_arc.dds"
    lifetime = 0.3
    color = [0.2, 0.6, 1.0, 1.0]
}

fx_electric_explosion = {
    type = "particle_burst"
    texture = "electric_burst.dds"
    lifetime = 0.4
    radius = 3.0
}
"@

$flameEffects | Out-File "$tempDir\effects\weapons\flamethrower_effects.fx" -Encoding UTF8
$lightningEffects | Out-File "$tempDir\effects\weapons\lightning_effects.fx" -Encoding UTF8

# Create audio definition files
$flameAudio = @"
// Flamethrower Audio
sfx_flamethrower_continuous_roar = {
    file = "flamethrower_fire.wav"
    volume = 1.2
    loop = true
}
"@

$lightningAudio = @"
// Lightning Audio
sfx_lightning_crack_sharp = {
    file = "lightning_crack.wav"
    volume = 1.3
    loop = false
}
"@

$flameAudio | Out-File "$tempDir\audio\weapons\flamethrower_audio.sfx" -Encoding UTF8
$lightningAudio | Out-File "$tempDir\audio\weapons\lightning_audio.sfx" -Encoding UTF8

# Create PAK file (as ZIP)
$pakFile = "client_pc\root\mods\visual_weapons.pak"

# Ensure mods directory exists
if (-not (Test-Path "client_pc\root\mods")) {
    New-Item -ItemType Directory -Path "client_pc\root\mods" -Force | Out-Null
}

# Remove existing pak
if (Test-Path $pakFile) {
    Remove-Item $pakFile -Force
}

# Create PAK file
try {
    Compress-Archive -Path "$tempDir\*" -DestinationPath $pakFile -Force
    Write-Host "Created PAK file: $pakFile" -ForegroundColor Green
} catch {
    Write-Warning "Could not create PAK file: $_"
}

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force

# Create mod configuration
$modConfig = @"
# Visual Weapons Mod Configuration
visual_weapons.pak:
  enabled: true
  priority: 100
  description: "Flamethrower pistol and lightning sword with full visual effects"
"@

$modConfig | Out-File "client_pc\root\mods\mod_config.yaml" -Encoding UTF8

Write-Host ""
Write-Host "Visual Effects PAK created successfully!" -ForegroundColor Green
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- $pakFile" -ForegroundColor White
Write-Host "- client_pc\root\mods\mod_config.yaml" -ForegroundColor White
Write-Host ""
Write-Host "Ready to test in Space Marine 2!" -ForegroundColor Yellow
