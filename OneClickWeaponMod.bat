@echo off
title Space Marine 2 - One-Click Weapon Mod
color 0C

echo.
echo  ███████╗██████╗  █████╗  ██████╗███████╗    ███╗   ███╗ █████╗ ██████╗ ██╗███╗   ██╗███████╗
echo  ██╔════╝██╔══██╗██╔══██╗██╔════╝██╔════╝    ████╗ ████║██╔══██╗██╔══██╗██║████╗  ██║██╔════╝
echo  ███████╗██████╔╝███████║██║     █████╗      ██╔████╔██║███████║██████╔╝██║██╔██╗ ██║█████╗  
echo  ╚════██║██╔═══╝ ██╔══██║██║     ██╔══╝      ██║╚██╔╝██║██╔══██║██╔══██╗██║██║╚██╗██║██╔══╝  
echo  ███████║██║     ██║  ██║╚██████╗███████╗    ██║ ╚═╝ ██║██║  ██║██║  ██║██║██║ ╚████║███████╗
echo  ╚══════╝╚═╝     ╚═╝  ╚═╝ ╚═════╝╚══════╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚══════╝
echo.
echo                            🔥 AUTOMATED WEAPON MOD 🔥
echo                           ===========================
echo.
echo  This tool automatically creates overpowered weapons:
echo  ✓ 999 damage flamethrower pistol
echo  ✓ 1500 damage lightning sword  
echo  ✓ One-shot kills on most enemies
echo  ✓ No manual work required!
echo.
echo  INSTRUCTIONS:
echo  1. Launch Space Marine 2 first
echo  2. Get to a mission with enemies
echo  3. Press any key to start the automated mod...
echo.
pause

echo.
echo 🚀 Starting automated weapon modification...
echo.

REM Run the PowerShell script with elevated privileges
powershell -ExecutionPolicy Bypass -Command "Start-Process powershell -ArgumentList '-ExecutionPolicy Bypass -File \"%~dp0AutoWeaponMod.ps1\" -AutoRun' -Verb RunAs"

echo.
echo ✅ Modification process started!
echo Check the PowerShell window for progress...
echo.
pause
