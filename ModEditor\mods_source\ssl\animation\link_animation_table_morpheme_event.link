__request   =   {
   __select   =   {
      __select   =   {
         __id   =   "arg"
         __select   =   {
            __path   =   "<project_dir>/resources/morpheme/{arg}/mr_link_browser_data.json"
            __root   =   "{arg}.events"
            __type   =   "jsonLink"
         }
         __path   =   "morphemeBundle"
         __type   =   "model"
      }
      __path   =   "actorCategories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "AnimationTable"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"


