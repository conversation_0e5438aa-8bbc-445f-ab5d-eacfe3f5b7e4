__request   =   {
   __select   =   {
      __id   =   ""
      __select   =   {
         __id   =   "mesh"
         __select   =   {
            __id   =   "cut"
            __select   =   {
               __id   =   "gib"
               __format   =   "{value}"
               __showValue   =   True
               __path   =   "gibNames/*"
               __type   =   "model"
            }
            __path   =   "cutRegions/*"
            __type   =   "model"
         }
         __path   =   "/*"
         __type   =   "model"
      }
      __path   =   "meshes/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "GoreMeshLibrary*"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{mesh}/{cut}/{gib}"
   __type   =   "rf_text"
}
__addEmpty   =   {
   __value   =   True
}
__type   =   "linkBrowser"

