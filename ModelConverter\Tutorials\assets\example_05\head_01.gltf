{"asset": {"generator": "Khronos glTF Blender I/O v4.3.47", "version": "2.0"}, "scene": 0, "scenes": [{"extras": {"vs": {"export_list": [{"name": "glTF_not_exported.dmx", "collection": {"name": "glTF_not_exported", "type": "Collection"}, "ob_type": "COLLECTION", "icon": "GROUP"}, {"name": "ARM1L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "ARM1L_cdt", "type": "Object"}}, {"name": "ARM1R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "ARM1R_cdt", "type": "Object"}}, {"name": "ARM2L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "ARM2L_cdt", "type": "Object"}}, {"name": "ARM2R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "ARM2R_cdt", "type": "Object"}}, {"name": "BACKdown_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "BACKdown_cdt", "type": "Object"}}, {"name": "backpack.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack", "type": "Object"}}, {"name": "backpack_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack_LOD1", "type": "Object"}}, {"name": "backpack_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack_LOD2", "type": "Object"}}, {"name": "backpack_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack_LOD3", "type": "Object"}}, {"name": "backpack_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack_LOD4", "type": "Object"}}, {"name": "backpack_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "backpack_LOD5", "type": "Object"}}, {"name": "BACKup_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "BACKup_cdt", "type": "Object"}}, {"name": "CENTRE_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "CENTRE_cdt", "type": "Object"}}, {"name": "cloth_cdt_1_body.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_1_body", "type": "Object"}}, {"name": "cloth_cdt_1_cap_bottom.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_1_cap_bottom", "type": "Object"}}, {"name": "cloth_cdt_1_cap_top.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_1_cap_top", "type": "Object"}}, {"name": "cloth_cdt_2_body.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_2_body", "type": "Object"}}, {"name": "cloth_cdt_2_cap_bottom.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_2_cap_bottom", "type": "Object"}}, {"name": "cloth_cdt_2_cap_top.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_cdt_2_cap_top", "type": "Object"}}, {"name": "cloth_stamp_leg.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg", "type": "Object"}}, {"name": "cloth_stamp_leg_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_LOD1", "type": "Object"}}, {"name": "cloth_stamp_leg_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_LOD2", "type": "Object"}}, {"name": "cloth_stamp_leg_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_LOD3", "type": "Object"}}, {"name": "cloth_stamp_leg_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_LOD4", "type": "Object"}}, {"name": "cloth_stamp_leg_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_LOD5", "type": "Object"}}, {"name": "cloth_stamp_leg_SIM.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "cloth_stamp_leg_SIM", "type": "Object"}}, {"name": "FOOT1L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "FOOT1L_cdt", "type": "Object"}}, {"name": "FOOT1R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "FOOT1R_cdt", "type": "Object"}}, {"name": "head_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "head_01", "type": "Object"}}, {"name": "head_01.002.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "head_01.002", "type": "Object"}}, {"name": "HEAD_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "HEAD_cdt", "type": "Object"}}, {"name": "helmet_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01", "type": "Object"}}, {"name": "helmet_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01_LOD1", "type": "Object"}}, {"name": "helmet_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01_LOD2", "type": "Object"}}, {"name": "helmet_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01_LOD3", "type": "Object"}}, {"name": "helmet_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01_LOD4", "type": "Object"}}, {"name": "helmet_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "helmet_01_LOD5", "type": "Object"}}, {"name": "LEG1L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "LEG1L_cdt", "type": "Object"}}, {"name": "LEG1R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "LEG1R_cdt", "type": "Object"}}, {"name": "LEG2L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "LEG2L_cdt", "type": "Object"}}, {"name": "LEG2R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "LEG2R_cdt", "type": "Object"}}, {"name": "powerplant_backpack_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "powerplant_backpack_cdt", "type": "Object"}}, {"name": "pre_order_shoulder_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01", "type": "Object"}}, {"name": "pre_order_shoulder_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01_LOD1", "type": "Object"}}, {"name": "pre_order_shoulder_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01_LOD2", "type": "Object"}}, {"name": "pre_order_shoulder_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01_LOD3", "type": "Object"}}, {"name": "pre_order_shoulder_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01_LOD4", "type": "Object"}}, {"name": "pre_order_shoulder_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "pre_order_shoulder_01_LOD5", "type": "Object"}}, {"name": "shoulder_L_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "shoulder_L_cdt", "type": "Object"}}, {"name": "shoulder_R_cdt.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "shoulder_R_cdt", "type": "Object"}}, {"name": "stamp_R_01_REND.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND", "type": "Object"}}, {"name": "stamp_R_01_REND_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND_LOD1", "type": "Object"}}, {"name": "stamp_R_01_REND_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND_LOD2", "type": "Object"}}, {"name": "stamp_R_01_REND_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND_LOD3", "type": "Object"}}, {"name": "stamp_R_01_REND_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND_LOD4", "type": "Object"}}, {"name": "stamp_R_01_REND_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_REND_LOD5", "type": "Object"}}, {"name": "stamp_R_01_SIM.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_01_SIM", "type": "Object"}}, {"name": "stamp_R_02_REND.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND", "type": "Object"}}, {"name": "stamp_R_02_REND_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND_LOD1", "type": "Object"}}, {"name": "stamp_R_02_REND_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND_LOD2", "type": "Object"}}, {"name": "stamp_R_02_REND_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND_LOD3", "type": "Object"}}, {"name": "stamp_R_02_REND_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND_LOD4", "type": "Object"}}, {"name": "stamp_R_02_REND_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_REND_LOD5", "type": "Object"}}, {"name": "stamp_R_02_SIM.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_02_SIM", "type": "Object"}}, {"name": "stamp_R_03_REND.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND", "type": "Object"}}, {"name": "stamp_R_03_REND_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND_LOD1", "type": "Object"}}, {"name": "stamp_R_03_REND_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND_LOD2", "type": "Object"}}, {"name": "stamp_R_03_REND_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND_LOD3", "type": "Object"}}, {"name": "stamp_R_03_REND_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND_LOD4", "type": "Object"}}, {"name": "stamp_R_03_REND_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_REND_LOD5", "type": "Object"}}, {"name": "stamp_R_03_SIM.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "stamp_R_03_SIM", "type": "Object"}}, {"name": "tbrd_01_bck_REND.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND", "type": "Object"}}, {"name": "tbrd_01_bck_REND_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND_LOD1", "type": "Object"}}, {"name": "tbrd_01_bck_REND_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND_LOD2", "type": "Object"}}, {"name": "tbrd_01_bck_REND_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND_LOD3", "type": "Object"}}, {"name": "tbrd_01_bck_REND_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND_LOD4", "type": "Object"}}, {"name": "tbrd_01_bck_REND_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_REND_LOD5", "type": "Object"}}, {"name": "tbrd_01_bck_SIM_02.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_bck_SIM_02", "type": "Object"}}, {"name": "tbrd_01_frnt_REND.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND", "type": "Object"}}, {"name": "tbrd_01_frnt_REND_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND_LOD1", "type": "Object"}}, {"name": "tbrd_01_frnt_REND_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND_LOD2", "type": "Object"}}, {"name": "tbrd_01_frnt_REND_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND_LOD3", "type": "Object"}}, {"name": "tbrd_01_frnt_REND_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND_LOD4", "type": "Object"}}, {"name": "tbrd_01_frnt_REND_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_REND_LOD5", "type": "Object"}}, {"name": "tbrd_01_frnt_SIM_02.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "tbrd_01_frnt_SIM_02", "type": "Object"}}, {"name": "titus_hand_L_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01", "type": "Object"}}, {"name": "titus_hand_L_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01_LOD1", "type": "Object"}}, {"name": "titus_hand_L_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01_LOD2", "type": "Object"}}, {"name": "titus_hand_L_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01_LOD3", "type": "Object"}}, {"name": "titus_hand_L_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01_LOD4", "type": "Object"}}, {"name": "titus_hand_L_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_L_01_LOD5", "type": "Object"}}, {"name": "titus_hand_R_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01", "type": "Object"}}, {"name": "titus_hand_R_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01_LOD1", "type": "Object"}}, {"name": "titus_hand_R_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01_LOD2", "type": "Object"}}, {"name": "titus_hand_R_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01_LOD3", "type": "Object"}}, {"name": "titus_hand_R_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01_LOD4", "type": "Object"}}, {"name": "titus_hand_R_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_hand_R_01_LOD5", "type": "Object"}}, {"name": "titus_legs_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01", "type": "Object"}}, {"name": "titus_legs_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01_LOD1", "type": "Object"}}, {"name": "titus_legs_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01_LOD2", "type": "Object"}}, {"name": "titus_legs_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01_LOD3", "type": "Object"}}, {"name": "titus_legs_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01_LOD4", "type": "Object"}}, {"name": "titus_legs_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_legs_01_LOD5", "type": "Object"}}, {"name": "titus_sholder_armor_L_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01", "type": "Object"}}, {"name": "titus_sholder_armor_L_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01_LOD1", "type": "Object"}}, {"name": "titus_sholder_armor_L_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01_LOD2", "type": "Object"}}, {"name": "titus_sholder_armor_L_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01_LOD3", "type": "Object"}}, {"name": "titus_sholder_armor_L_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01_LOD4", "type": "Object"}}, {"name": "titus_sholder_armor_L_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_L_01_LOD5", "type": "Object"}}, {"name": "titus_sholder_armor_R_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01", "type": "Object"}}, {"name": "titus_sholder_armor_R_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01_LOD1", "type": "Object"}}, {"name": "titus_sholder_armor_R_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01_LOD2", "type": "Object"}}, {"name": "titus_sholder_armor_R_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01_LOD3", "type": "Object"}}, {"name": "titus_sholder_armor_R_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01_LOD4", "type": "Object"}}, {"name": "titus_sholder_armor_R_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_sholder_armor_R_01_LOD5", "type": "Object"}}, {"name": "titus_torso_01.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01", "type": "Object"}}, {"name": "titus_torso_01_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01_LOD1", "type": "Object"}}, {"name": "titus_torso_01_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01_LOD2", "type": "Object"}}, {"name": "titus_torso_01_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01_LOD3", "type": "Object"}}, {"name": "titus_torso_01_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01_LOD4", "type": "Object"}}, {"name": "titus_torso_01_LOD5.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "titus_torso_01_LOD5", "type": "Object"}}]}, "name": "", "type": "tpl", "ps": "\noptions    =    {\n    geom    =    {\n        optimize_pc    =    TRUE\n        share_geometry    =    TRUE\n        build_skin_compound    =    TRUE\n        weld_vtx    =    0.001000\n        weld_uv    =    0.001000\n        export_decals    =    FALSE\n        subdivide_vis    =    FALSE\n        subdivide_edge    =    FALSE\n        subdivide_merge    =    FALSE\n        subdivide_merge_count    =    200\n        disable_tpl_edge    =    TRUE\n        dyn_cont    =    FALSE\n    }\n\tadvanced    =    {\n        disable_bulldozer    =    TRUE\n        disable_export    =    FALSE\n        disable_resource_manager    =    FALSE\n        disable_shadow_map_converter    =    FALSE\n        memory_file_size    =    96\n        optim_tpl    =    TRUE\n        dont_run_lsagen    =    TRUE\n        show_lsagen_wnd    =    FALSE\n        disable_scene_optimization    =    FALSE\n        disable_scene_group_optimization    =    FALSE\n        remove_degenerated_faces_havok    =    TRUE\n        export_mtl_in_text_format    =    FALSE\n        optimize_indices_for_tl_cache    =    TRUE\n        export_tpl_data    =    TRUE\n    }\n    compression    =    {\n        compress_verts_tpl    =    TRUE\n        compress_stat_verts    =    {\n            precision    =    0.002000\n            __type_id    =    \"yes\"\n        }\n        compress_normals    =    TRUE\n        compress_norm_to_4verts    =    TRUE\n        compress_texture    =    TRUE\n    }\n    sync_folder    =    FALSE\n}\n"}, "name": "Scene", "nodes": [0]}], "nodes": [{"extras": {"vs": {}}, "mesh": 0, "name": "head_01.002"}], "materials": [{"doubleSided": true, "name": "ch_titus_body_02_mat0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}, {"doubleSided": true, "name": "ch_titus_undersuit_02_mat0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}, {"doubleSided": true, "name": "ch_lego", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_OPTIONAL", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"soft\"\n      textureName = \"ch_titus_hair\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   },\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 0.650000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_hair_ao\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 3\n      uvSetIdx = 1\n   }\n]\nmaterial = {\n   name = \"ch_titus_hair_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 1\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   },\n   {\n      isUsedInLayersOnly = 1\n      name = \"ao\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_eye\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_eye_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_body_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_body_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"soft_cool\"\n      textureName = \"ch_eyelashes_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_eyelashes_02_mat1\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_undersuit_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_undersuit_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"metall\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat1\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_OPTIONAL", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"hard\"\n      textureName = \"ch_titus_hair\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   },\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 0.650000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_titus_hair_ao\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 3\n      uvSetIdx = 1\n   }\n]\nmaterial = {\n   name = \"ch_titus_hair_mat1\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 1\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   },\n   {\n      isUsedInLayersOnly = 1\n      name = \"ao\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"hair\"\n      textureName = \"ch_titus_head\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_titus_head_mat3\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 0\n      name = \"map1\"\n   }\n]"}}, {"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n   {\n      colorChannelIdx = 0\n      isUsedInLayersOnly = 0\n   }\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"ch_eye_shadow_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"ch_eye_shadow_01_mat1\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = 0\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "DUAL_QUATERNION"}, "name": "head_01.001", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2, "TEXCOORD_1": 3, "TEXCOORD_2": 4, "TEXCOORD_3": 5, "COLOR_0": 6, "COLOR_1": 7, "COLOR_2": 8}, "indices": 9, "material": 0}, {"attributes": {"POSITION": 10, "NORMAL": 11, "TEXCOORD_0": 12, "TEXCOORD_1": 13, "TEXCOORD_2": 14, "TEXCOORD_3": 15, "COLOR_0": 16, "COLOR_1": 17, "COLOR_2": 18}, "indices": 19, "material": 1}, {"attributes": {"POSITION": 20, "NORMAL": 21, "TEXCOORD_0": 22, "TEXCOORD_1": 23, "TEXCOORD_2": 24, "TEXCOORD_3": 25, "COLOR_0": 26, "COLOR_1": 27, "COLOR_2": 28}, "indices": 29, "material": 2}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 2411, "max": [0.16750800609588623, 2.332688093185425, 0.09795299917459488], "min": [-0.16750800609588623, 2.202907085418701, -0.1990250051021576], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 2411, "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 2411, "type": "VEC2"}, {"bufferView": 3, "componentType": 5126, "count": 2411, "type": "VEC2"}, {"bufferView": 4, "componentType": 5126, "count": 2411, "type": "VEC2"}, {"bufferView": 5, "componentType": 5126, "count": 2411, "type": "VEC2"}, {"bufferView": 6, "componentType": 5121, "count": 2411, "normalized": true, "type": "VEC4"}, {"bufferView": 7, "componentType": 5123, "count": 2411, "normalized": true, "type": "VEC4"}, {"bufferView": 8, "componentType": 5123, "count": 2411, "normalized": true, "type": "VEC4"}, {"bufferView": 9, "componentType": 5123, "count": 3498, "type": "SCALAR"}, {"bufferView": 10, "componentType": 5126, "count": 2838, "max": [0.13625499606132507, 2.3923256397247314, 0.09245819598436356], "min": [-0.13625499606132507, 2.221029758453369, -0.16992399096488953], "type": "VEC3"}, {"bufferView": 11, "componentType": 5126, "count": 2838, "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 2838, "type": "VEC2"}, {"bufferView": 13, "componentType": 5126, "count": 2838, "type": "VEC2"}, {"bufferView": 14, "componentType": 5126, "count": 2838, "type": "VEC2"}, {"bufferView": 15, "componentType": 5126, "count": 2838, "type": "VEC2"}, {"bufferView": 16, "componentType": 5121, "count": 2838, "normalized": true, "type": "VEC4"}, {"bufferView": 17, "componentType": 5123, "count": 2838, "normalized": true, "type": "VEC4"}, {"bufferView": 18, "componentType": 5123, "count": 2838, "normalized": true, "type": "VEC4"}, {"bufferView": 19, "componentType": 5123, "count": 3900, "type": "SCALAR"}, {"bufferView": 20, "componentType": 5126, "count": 1116, "max": [0.10004693269729614, 2.534452438354492, 0.08421964943408966], "min": [-0.10004688799381256, 2.2798209190368652, -0.11587421596050262], "type": "VEC3"}, {"bufferView": 21, "componentType": 5126, "count": 1116, "type": "VEC3"}, {"bufferView": 22, "componentType": 5126, "count": 1116, "type": "VEC2"}, {"bufferView": 23, "componentType": 5126, "count": 1116, "type": "VEC2"}, {"bufferView": 24, "componentType": 5126, "count": 1116, "type": "VEC2"}, {"bufferView": 25, "componentType": 5126, "count": 1116, "type": "VEC2"}, {"bufferView": 26, "componentType": 5121, "count": 1116, "normalized": true, "type": "VEC4"}, {"bufferView": 27, "componentType": 5123, "count": 1116, "normalized": true, "type": "VEC4"}, {"bufferView": 28, "componentType": 5123, "count": 1116, "normalized": true, "type": "VEC4"}, {"bufferView": 29, "componentType": 5123, "count": 1908, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 28932, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 28932, "byteOffset": 28932, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 57864, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 77152, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 96440, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 115728, "target": 34962}, {"buffer": 0, "byteLength": 9644, "byteOffset": 135016, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 144660, "target": 34962}, {"buffer": 0, "byteLength": 19288, "byteOffset": 163948, "target": 34962}, {"buffer": 0, "byteLength": 6996, "byteOffset": 183236, "target": 34963}, {"buffer": 0, "byteLength": 34056, "byteOffset": 190232, "target": 34962}, {"buffer": 0, "byteLength": 34056, "byteOffset": 224288, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 258344, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 281048, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 303752, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 326456, "target": 34962}, {"buffer": 0, "byteLength": 11352, "byteOffset": 349160, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 360512, "target": 34962}, {"buffer": 0, "byteLength": 22704, "byteOffset": 383216, "target": 34962}, {"buffer": 0, "byteLength": 7800, "byteOffset": 405920, "target": 34963}, {"buffer": 0, "byteLength": 13392, "byteOffset": 413720, "target": 34962}, {"buffer": 0, "byteLength": 13392, "byteOffset": 427112, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 440504, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 449432, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 458360, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 467288, "target": 34962}, {"buffer": 0, "byteLength": 4464, "byteOffset": 476216, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 480680, "target": 34962}, {"buffer": 0, "byteLength": 8928, "byteOffset": 489608, "target": 34962}, {"buffer": 0, "byteLength": 3816, "byteOffset": 498536, "target": 34963}], "buffers": [{"byteLength": 502352, "uri": "head_01.bin"}]}