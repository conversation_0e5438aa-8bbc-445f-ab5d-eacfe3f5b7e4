{"asset": {"generator": "Khronos glTF Blender I/O v4.3.47", "version": "2.0"}, "scene": 0, "scenes": [{"extras": {"vs": {"export_list": [{"name": "knife_geom.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "knife_geom", "type": "Object"}}, {"name": "knife_geom_LOD1.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "knife_geom_LOD1", "type": "Object"}}, {"name": "knife_geom_LOD2.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "knife_geom_LOD2", "type": "Object"}}, {"name": "knife_geom_LOD3.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "knife_geom_LOD3", "type": "Object"}}, {"name": "knife_geom_LOD4.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "knife_geom_LOD4", "type": "Object"}}, {"name": "rb_wpn.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "rb_wpn", "type": "Object"}}]}, "name": "", "type": "tpl", "ps": "options = {\n   geom = {\n      optimize_pc = true\n      share_geometry = true\n      build_skin_compound = true\n      weld_vtx = 0.001000\n      weld_uv = 0.001000\n      export_decals = false\n      subdivide_vis = false\n      subdivide_edge = false\n      subdivide_merge = false\n      subdivide_merge_count = 200\n      disable_tpl_edge = true\n      dyn_cont = true\n   }\n   advanced = {\n      disable_bulldozer1 = true\n      disable_export = false\n      disable_resource_manager = false\n      disable_shadow_map_converter = false\n      memory_file_size = 96\n      optim_tpl = true\n      dont_run_lsagen = true\n      show_lsagen_wnd = false\n      disable_scene_optimization = false\n      disable_scene_group_optimization = false\n      remove_degenerated_faces_havok = true\n      export_mtl_in_text_format = true\n      optimize_indices_for_tl_cache = true\n   }\n   compression = {\n      compress_verts_tpl = true\n      compress_stat_verts = {\n         precision = 0.002000\n         __type_id = \"yes\"\n      }\n      compress_normals = true\n      compress_norm_to_4verts = true\n      compress_texture = true\n   }\n   src_filename = \"weapons\\\\wpn_combat_knife_01.mb\"\n   src_in_common = false\n   src_dir_key = \"database-src-dir\"\n   sync_folder = true\n}\n"}, "name": "Scene", "nodes": [18]}], "nodes": [{"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\n\n", "mayaNodeId": "locator"}, "name": "floorplace", "translation": [0.03777007386088371, 0, 0]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\ncdt_high_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 0, "name": "knife_geom_LOD1"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\ncdt_high_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 1, "name": "knife_geom_LOD2"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\ncdt_high_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 2, "name": "knife_geom_LOD3"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\ncdt_high_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 3, "name": "knife_geom_LOD4"}, {"children": [1, 2, 3, 4], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nexport_no_normal_compression\n\ncdt_high_res_geometry\n\nlod_system_root\ndistances\n10.0 20.0 30.0 40.0 50.0\nis_shared\nFalse\nhide_at_last_distance\nTrue\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 4, "name": "knife_geom", "translation": [0.14589563012123108, 0, 0.4623018503189087]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\ncdt_low_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 5, "name": "rb_wpn"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\n\n", "mayaNodeId": "locator", "animation": {"initialTranslate": {"x": 0, "y": -0.078053005, "z": -0.4922704}, "initialRotation": {"x": 6.123234e-17, "y": 0, "z": -1, "w": 0}, "initialScale": {"x": 1, "y": 1, "z": 1}, "initialVisibility": 1, "splTranslate": {"splType": "M3D_SPL_LINEAR1D", "splState": 0, "splValueDim": 0, "splValueDataDim": 0, "splNkp": 0, "splDataSize": 0, "splData": null}, "splRotation": {"splType": "M3D_SPL_QUAT", "splState": 0, "splValueDim": 4, "splValueDataDim": 4, "splNkp": 261, "splDataSize": 5220, "splData": "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"}, "splScale": {"splType": "M3D_SPL_LINEAR1D", "splState": 0, "splValueDim": 0, "splValueDataDim": 0, "splNkp": 0, "splDataSize": 0, "splData": null}, "splVisibility": {"splType": "M3D_SPL_LINEAR1D", "splState": 0, "splValueDim": 0, "splValueDataDim": 0, "splNkp": 0, "splDataSize": 0, "splData": null}}}, "name": "sfx_blood_pt", "rotation": [0, 1, 0, 6.123234262925839e-17], "translation": [0, -0.07805300503969193, -0.49227041006088257]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nvisibility_hidden\npreserveMaterial\n0\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_trail_blood_01", "rotation": [0, 1, 0, 6.123234262925839e-17], "translation": [0, -0.004356068558990955, -0.9604142904281616]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nvisibility_hidden\npreserveMaterial\n0\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_trail_blood_02", "rotation": [0, 1, 0, 6.123234262925839e-17], "translation": [0, -0.004356068558990955, -0.7722910046577454]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nvisibility_hidden\npreserveMaterial\n0\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_trail_blood_03", "rotation": [0, 1, 0, 6.123234262925839e-17], "translation": [0, -0.004356068558990955, -0.430395245552063]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_parry_loc", "translation": [0, -0.11451762914657593, -0.5839412212371826]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_trail2", "translation": [0, 0.058721091598272324, -1.0340090990066528]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\n\n", "mayaNodeId": "locator"}, "name": "sfx_trail3", "translation": [0, 0.058721091598272324, -0.17883586883544922]}, {"children": [11, 12, 13], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": ""}, "name": "vfx"}, {"children": [0, 5, 6, 7, 8, 9, 10, 14], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\n\n", "mayaNodeId": ""}, "name": "body"}, {"children": [15], "extras": {"pses": {"version": 258, "ps": {"str": "tpl_name = \"weapons\\\\wpn_combat_knife_01.tpl\";tpl_obj = \"\";__type = \"iactor\";ExportOptions = {generateCollisionData = true;buildSkinCompound = true;};"}}, "affixes": "", "mayaNodeId": "saberActor", "actor": {"actorTypeString": "tpl_desc", "actorType": "USF_ACTORTYPE_EXTERNAL", "tplName": "weapons\\wpn_combat_knife_01.tpl", "animations": [], "sourceUsfUids": [], "refName": "", "isNested": false, "isRTC": false, "clsName": "", "isSubNested": false}}, "name": "combat_knife"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "saberDxNode"}, "name": "s3dSaberDxTechnicalNode"}, {"children": [16, 17], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": ""}, "name": ".root."}], "materials": [{"doubleSided": true, "name": "wpn_combat_knife_01_mat0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}, {"doubleSided": true, "name": "Material_0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}], "meshes": [{"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"wpn_combat_knife_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"wpn_combat_knife_01_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "knife_geom_LOD1", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2, "TEXCOORD_1": 3, "TEXCOORD_2": 4, "TEXCOORD_3": 5, "COLOR_0": 6, "COLOR_1": 7, "COLOR_2": 8}, "indices": 9, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"wpn_combat_knife_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"wpn_combat_knife_01_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "knife_geom_LOD2", "primitives": [{"attributes": {"POSITION": 10, "NORMAL": 11, "TEXCOORD_0": 12, "TEXCOORD_1": 13, "TEXCOORD_2": 14, "TEXCOORD_3": 15, "COLOR_0": 16, "COLOR_1": 17, "COLOR_2": 18}, "indices": 19, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"wpn_combat_knife_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"wpn_combat_knife_01_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "knife_geom_LOD3", "primitives": [{"attributes": {"POSITION": 20, "NORMAL": 21, "TEXCOORD_0": 22, "TEXCOORD_1": 23, "TEXCOORD_2": 24, "TEXCOORD_3": 25, "COLOR_0": 26, "COLOR_1": 27, "COLOR_2": 28}, "indices": 29, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"wpn_combat_knife_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"wpn_combat_knife_01_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "knife_geom_LOD4", "primitives": [{"attributes": {"POSITION": 30, "NORMAL": 31, "TEXCOORD_0": 32, "TEXCOORD_1": 33, "TEXCOORD_2": 34, "TEXCOORD_3": 35, "COLOR_0": 36, "COLOR_1": 37, "COLOR_2": 38}, "indices": 39, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"wpn_combat_knife_01\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"wpn_combat_knife_01_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "knife_geom", "primitives": [{"attributes": {"POSITION": 40, "NORMAL": 41, "TEXCOORD_0": 42, "TEXCOORD_1": 43, "TEXCOORD_2": 44, "TEXCOORD_3": 45, "COLOR_0": 46, "COLOR_1": 47, "COLOR_2": 48}, "indices": 49, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "material = {\n   version = 2\n   type = 0\n}\nlayers = [\n\n]\nuvSets = [\n\n]\ncolorSets = [\n\n]\n"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "rb_wpn", "primitives": [{"attributes": {"POSITION": 50, "NORMAL": 51, "TEXCOORD_0": 52, "TEXCOORD_1": 53, "TEXCOORD_2": 54, "TEXCOORD_3": 55, "COLOR_0": 56, "COLOR_1": 57, "COLOR_2": 58}, "indices": 59, "material": 1}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 2166, "max": [0.059214770793914795, 0.08561208844184875, -0.26913678646087646], "min": [-0.4010314643383026, -0.13234540820121765, -1.3690779209136963], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 2166, "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 2166, "type": "VEC2"}, {"bufferView": 3, "componentType": 5126, "count": 2166, "type": "VEC2"}, {"bufferView": 4, "componentType": 5126, "count": 2166, "type": "VEC2"}, {"bufferView": 5, "componentType": 5126, "count": 2166, "type": "VEC2"}, {"bufferView": 6, "componentType": 5121, "count": 2166, "normalized": true, "type": "VEC4"}, {"bufferView": 7, "componentType": 5123, "count": 2166, "normalized": true, "type": "VEC4"}, {"bufferView": 8, "componentType": 5123, "count": 2166, "normalized": true, "type": "VEC4"}, {"bufferView": 9, "componentType": 5123, "count": 4398, "type": "SCALAR"}, {"bufferView": 10, "componentType": 5126, "count": 1622, "max": [0.05758027732372284, 0.08426632732152939, -0.26844340562820435], "min": [-0.40124741196632385, -0.12986469268798828, -1.3691861629486084], "type": "VEC3"}, {"bufferView": 11, "componentType": 5126, "count": 1622, "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 1622, "type": "VEC2"}, {"bufferView": 13, "componentType": 5126, "count": 1622, "type": "VEC2"}, {"bufferView": 14, "componentType": 5126, "count": 1622, "type": "VEC2"}, {"bufferView": 15, "componentType": 5126, "count": 1622, "type": "VEC2"}, {"bufferView": 16, "componentType": 5121, "count": 1622, "normalized": true, "type": "VEC4"}, {"bufferView": 17, "componentType": 5123, "count": 1622, "normalized": true, "type": "VEC4"}, {"bufferView": 18, "componentType": 5123, "count": 1622, "normalized": true, "type": "VEC4"}, {"bufferView": 19, "componentType": 5123, "count": 2196, "type": "SCALAR"}, {"bufferView": 20, "componentType": 5126, "count": 915, "max": [0.05568540096282959, 0.08925296366214752, -0.2684401869773865], "min": [-0.40128159523010254, -0.13096977770328522, -1.369187593460083], "type": "VEC3"}, {"bufferView": 21, "componentType": 5126, "count": 915, "type": "VEC3"}, {"bufferView": 22, "componentType": 5126, "count": 915, "type": "VEC2"}, {"bufferView": 23, "componentType": 5126, "count": 915, "type": "VEC2"}, {"bufferView": 24, "componentType": 5126, "count": 915, "type": "VEC2"}, {"bufferView": 25, "componentType": 5126, "count": 915, "type": "VEC2"}, {"bufferView": 26, "componentType": 5121, "count": 915, "normalized": true, "type": "VEC4"}, {"bufferView": 27, "componentType": 5123, "count": 915, "normalized": true, "type": "VEC4"}, {"bufferView": 28, "componentType": 5123, "count": 915, "normalized": true, "type": "VEC4"}, {"bufferView": 29, "componentType": 5123, "count": 1098, "type": "SCALAR"}, {"bufferView": 30, "componentType": 5126, "count": 484, "max": [0.05553324520587921, 0.08884537220001221, -0.26193028688430786], "min": [-0.40108734369277954, -0.13235192000865936, -1.3691446781158447], "type": "VEC3"}, {"bufferView": 31, "componentType": 5126, "count": 484, "type": "VEC3"}, {"bufferView": 32, "componentType": 5126, "count": 484, "type": "VEC2"}, {"bufferView": 33, "componentType": 5126, "count": 484, "type": "VEC2"}, {"bufferView": 34, "componentType": 5126, "count": 484, "type": "VEC2"}, {"bufferView": 35, "componentType": 5126, "count": 484, "type": "VEC2"}, {"bufferView": 36, "componentType": 5121, "count": 484, "normalized": true, "type": "VEC4"}, {"bufferView": 37, "componentType": 5123, "count": 484, "normalized": true, "type": "VEC4"}, {"bufferView": 38, "componentType": 5123, "count": 484, "normalized": true, "type": "VEC4"}, {"bufferView": 39, "componentType": 5123, "count": 546, "type": "SCALAR"}, {"bufferView": 40, "componentType": 5126, "count": 1849, "max": [0.05921494960784912, 0.08698559552431107, -0.2693480849266052], "min": [-0.4010314643383026, -0.1321897655725479, -1.3690779209136963], "type": "VEC3"}, {"bufferView": 41, "componentType": 5126, "count": 1849, "type": "VEC3"}, {"bufferView": 42, "componentType": 5126, "count": 1849, "type": "VEC2"}, {"bufferView": 43, "componentType": 5126, "count": 1849, "type": "VEC2"}, {"bufferView": 44, "componentType": 5126, "count": 1849, "type": "VEC2"}, {"bufferView": 45, "componentType": 5126, "count": 1849, "type": "VEC2"}, {"bufferView": 46, "componentType": 5121, "count": 1849, "normalized": true, "type": "VEC4"}, {"bufferView": 47, "componentType": 5123, "count": 1849, "normalized": true, "type": "VEC4"}, {"bufferView": 48, "componentType": 5123, "count": 1849, "normalized": true, "type": "VEC4"}, {"bufferView": 49, "componentType": 5123, "count": 8802, "type": "SCALAR"}, {"bufferView": 50, "componentType": 5126, "count": 24, "max": [0.044003844261169434, 0.07707545161247253, 0.18941208720207214], "min": [-0.04233500361442566, -0.11392974853515625, -0.9727201461791992], "type": "VEC3"}, {"bufferView": 51, "componentType": 5126, "count": 24, "type": "VEC3"}, {"bufferView": 52, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 53, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 54, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 55, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 56, "componentType": 5121, "count": 24, "normalized": true, "type": "VEC4"}, {"bufferView": 57, "componentType": 5123, "count": 24, "normalized": true, "type": "VEC4"}, {"bufferView": 58, "componentType": 5123, "count": 24, "normalized": true, "type": "VEC4"}, {"bufferView": 59, "componentType": 5123, "count": 36, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 25992, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 25992, "byteOffset": 25992, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 51984, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 69312, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 86640, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 103968, "target": 34962}, {"buffer": 0, "byteLength": 8664, "byteOffset": 121296, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 129960, "target": 34962}, {"buffer": 0, "byteLength": 17328, "byteOffset": 147288, "target": 34962}, {"buffer": 0, "byteLength": 8796, "byteOffset": 164616, "target": 34963}, {"buffer": 0, "byteLength": 19464, "byteOffset": 173412, "target": 34962}, {"buffer": 0, "byteLength": 19464, "byteOffset": 192876, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 212340, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 225316, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 238292, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 251268, "target": 34962}, {"buffer": 0, "byteLength": 6488, "byteOffset": 264244, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 270732, "target": 34962}, {"buffer": 0, "byteLength": 12976, "byteOffset": 283708, "target": 34962}, {"buffer": 0, "byteLength": 4392, "byteOffset": 296684, "target": 34963}, {"buffer": 0, "byteLength": 10980, "byteOffset": 301076, "target": 34962}, {"buffer": 0, "byteLength": 10980, "byteOffset": 312056, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 323036, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 330356, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 337676, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 344996, "target": 34962}, {"buffer": 0, "byteLength": 3660, "byteOffset": 352316, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 355976, "target": 34962}, {"buffer": 0, "byteLength": 7320, "byteOffset": 363296, "target": 34962}, {"buffer": 0, "byteLength": 2196, "byteOffset": 370616, "target": 34963}, {"buffer": 0, "byteLength": 5808, "byteOffset": 372812, "target": 34962}, {"buffer": 0, "byteLength": 5808, "byteOffset": 378620, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 384428, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 388300, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 392172, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 396044, "target": 34962}, {"buffer": 0, "byteLength": 1936, "byteOffset": 399916, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 401852, "target": 34962}, {"buffer": 0, "byteLength": 3872, "byteOffset": 405724, "target": 34962}, {"buffer": 0, "byteLength": 1092, "byteOffset": 409596, "target": 34963}, {"buffer": 0, "byteLength": 22188, "byteOffset": 410688, "target": 34962}, {"buffer": 0, "byteLength": 22188, "byteOffset": 432876, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 455064, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 469856, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 484648, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 499440, "target": 34962}, {"buffer": 0, "byteLength": 7396, "byteOffset": 514232, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 521628, "target": 34962}, {"buffer": 0, "byteLength": 14792, "byteOffset": 536420, "target": 34962}, {"buffer": 0, "byteLength": 17604, "byteOffset": 551212, "target": 34963}, {"buffer": 0, "byteLength": 288, "byteOffset": 568816, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 569104, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 569392, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 569584, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 569776, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 569968, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 570160, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 570256, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 570448, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 570640, "target": 34963}], "buffers": [{"byteLength": 570712, "uri": "wpn_combat_knife_01.bin"}]}