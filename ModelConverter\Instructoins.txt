ModelConverter: 
	Converts USF<->GLTF. Just drag and drop it. Input\Output files should already have final name, e.g. cc_titus and not my_cool_model, since mdoel name gets baked into the model. 
	You can use --no-skin to extract only rigid geometry and locators from USF file
	You can use --experimental to use more convenient, but experimental format of your GLTF file
CrashReporter: Open existing SSLDMP file and view callstack and SSL frame memory. You may replace stock Crash Reporter with it too.
UsfExporter: Converts USF into TPL and TGA into PCT files and also generates resources for them.
SampleUsfFiles: Sample USFs to be converted and edited in Blender with most common data (bones, locators, etc)
PremadeTemplates: PvE constructor sample + few samples in experimental format