#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	light = "Msjhl.ttc";
	regular = "Msjh.ttc";
	bold = "Msjhbd.ttc";
};
bugreport:
{
	btn_continue = "線上檢查解決方案並關閉遊戲。";
	btn_exit = "關閉遊戲。";
	btn_hidedetails = "隱藏詳情";
	btn_showdetails = "顯示詳情";
	chk_sendreport = "送出錯誤報告";
	error_code = "錯誤代碼：";
	lbl_body1 = "很抱歉，在啟動你的遊戲時遇到了問題。";
	lbl_body2 = "請透過報告問題幫助我們解決。";
	lbl_body3 = "Easy Anti-Cheat 可以線上檢查有此問題的解決方案，並試著協助解決。";
	lbl_header = "無法啟動遊戲";
	title = "啟動錯誤";
};
game_error:
{
	error_catalogue_corrupted = "Easy Anti-Cheat 雜湊分類損壞";
	error_catalogue_not_found = "未找到 EAC 索引";
	error_certificate_revoked = "EAC 索引證書已撤銷";
	error_corrupted_memory = "記憶體損壞";
	error_corrupted_network = "資料包流損壞";
	error_file_forbidden = "未知的遊戲檔";
	error_file_not_found = "缺少必需的文件";
	error_file_version = "未知的檔案版本";
	error_module_forbidden = "禁止的模組";
	error_system_configuration = "禁止的系統組態";
	error_system_version = "不受信任的系統檔";
	error_tool_forbidden = "禁止的工具";
	error_violation = "內部反作弊錯誤";
	error_virtual = "無法在虛擬機器下運行。";
	peer_client_banned = "反作弊同伴已禁止。";
	peer_heartbeat_rejected = "反作弊同伴已拒絕。";
	peer_validated = "反作弊同伴核驗已完成。";
	peer_validation_failed = "反作弊同伴核驗已失敗。";
	executable_not_hashed = "在本目錄中找不到遊戲執行檔。";
};
launcher:
{
	btn_cancel = "取消";
	btn_exit = "退出";
	error_cancel = "啟動取消";
	error_filenotfound = "檔案未找到";
	error_init = "初始化錯誤";
	error_install = "安裝錯誤";
	error_launch = "啟動錯誤";
	error_nolib = "無法載入 Easy Anti-Cheat 庫";
	loading = "載入中";
	wait = "請稍候";
	initializing = "正在初始化";
	success_waiting_for_game = "等待遊戲中";
	success_closing = "成功";
	network_error = "網路錯誤";
	error_no_settings_file = "找不到 {0}";
	error_invalid_settings_format = "{0} 沒有有效的 JSON 格式";
	error_missing_required_field = "必要檔案（{1}）中缺少 {0}";
	error_invalid_eos_identifier = "{0} 包含無效的 EOS 識別碼：({1})";
	download_progress = "下載進度： {0}";
};
launcher_error:
{
	error_already_running = "使用 Easy Anti-Cheat 的應用程式已在運行中！{0}";
	error_application = "遊戲使用者端遇到應用程式錯誤。錯誤代碼： {0}";
	error_bad_exe_format = "需要 64 位元作業系統";
	error_bitset_32 = "請使用遊戲的32位版本";
	error_bitset_64 = "請使用遊戲的64位版本";
	error_cancelled = "操作已由使用者取消";
	error_certificate_validation = "驗證 Easy Anti-Cheat 代碼簽章憑證時出錯";
	error_connection = "連線內容分發網路失敗！";
	error_debugger = "檢測到偵錯工具。請取消載入，然後重試";
	error_disk_space = "磁碟空間不足。";
	error_dns = "DNS 解析到內容分發網路失敗！";
	error_dotlocal = "檢測到 DotLocal DLL 重定向。";
	error_dotlocal_instructions = "請刪除以下檔案";
	error_file_not_found = "檔案未找到：";
	error_forbidden_tool = "請在開始遊戲前關閉{0}";
	error_forbidden_driver = "請在啟動遊戲前卸載 {0}";
	error_generic = "意外錯誤。";
	error_kernel_debug = "若啟用了內核調試，Easy Anti-Cheat 將無法運行";
	error_kernel_dse = "若停用了驅動程式強制簽名，Easy Anti-Cheat 將無法運行";
	error_kernel_modified = "檢測到禁止的 Windows 內核修改";
	error_library_load = "無法載入 Easy Anti-Cheat 庫";
	error_memory = "記憶體不足，無法啟動遊戲";
	error_module_load = "無法載入反作弊模組";
	error_patched = "檢測到經過修補的 Windows 開機載入程式";
	error_process = "無法建立進程";
	error_process_crash = "進程突然終止";
	error_safe_mode = "Easy Anti-Cheat 無法在 Windows 安全模式下運行";
	error_socket = "有什麼東西阻止應用程式存取網際網路！";
	error_ssl = "與 CDN 服務建立 SSL 連線時出錯！";
	error_start = "無法啟動遊戲";
	error_uncpath_forbidden = "無法透過網路共享執行遊戲。（UNC 路徑）";
	error_connection_failed = "連線失敗: ";
	error_missing_game_id = "找不到遊戲 id";
	error_dns_resolve_failed = "DNS 解析為代理失敗";
	error_dns_connection_failed = "連結至內容傳遞網路失敗！Curl 碼：{0}!";
	error_http_response = "HTTP 回應碼：{0} Curl 碼：{1}";
	error_driver_handle = "發生非預期的錯誤。（開啟 driver handle 失敗）";
	error_incompatible_service = "有版本不相容的 Easy Anti-Cheat 服務已在執行中，請退出其他正在執行的遊戲或重新啟動。";
	error_incompatible_driver_version = "有版本不相容的 Easy Anti-Cheat 驅動程式已在執行中，請退出其他正在執行的遊戲或重新啟動。";
	error_another_launcher = "發生非預期的錯誤。（另一個啟動器已在執行）";
	error_game_running = "發生非預期的錯誤。（遊戲已在執行）";
	error_patched_boot_loader = "偵測到 Patched Windows 啟動程式。（Kernel Patch Protection 已停用）";
	error_unknown_process = "無法識別的遊戲使用者端。無法繼續。";
	error_unknown_game = "遊戲未設定，無法繼續。";
	error_win7_required = "需要 Windows 7 或更高版本。";
	success_initialized = "Easy Anti-Cheat 成功初始化";
	success_loaded = "Easy Anti-Cheat 在遊戲中成功載入";
	error_create_process = "遊戲程序建立失敗：{0}";
	error_create_thread = "背景執行緒建立失敗！";
	error_disallowed_cdn_path = "預期外的錯誤。（不正確的 CDN URL）";
	error_empty_executable_field = "未提供遊戲二進位檔路徑。";
	error_failed_path_query = "無法取得程序路徑";
	error_failed_to_execute = "遊戲程序執行失敗。";
	error_game_binary_is_directory = "目標執行檔是目錄！";
	error_game_binary_is_dot_app = "目標執行檔是目錄，請以 .app 內的二進位檔為目標！";
	error_game_binary_not_found = "找不到遊戲二進位檔「{0}」";
	error_game_binary_not_found_wine = "找不到遊戲二進位檔 (Wine)";
	error_game_security_violation = "違反遊戲安全性 {0}";
	error_generic_ex = "預期外的錯誤。{0}";
	error_instance_count_limit = "同時執行的遊戲執行個體數量已達上限！";
	error_internal = "發生內部錯誤！";
	error_invalid_executable_path = "遊戲執行檔路徑無效！";
	error_memory_ex = "記憶體不足，無法啟動遊戲 {0}";
	error_missing_binary_path = "缺少遊戲執行檔路徑。";
	error_missing_directory_path = "缺少工作目錄路徑。";
	error_module_initialize = "模組初始化失敗，{0}";
	error_module_loading = "反作弊模組載入失敗。";
	error_set_environment_variables = "遊戲程序環境變數設定失敗。";
	error_unrecognized_blacklisted_driver = "偵測到 N/A。請解除安裝後再重試。";
	error_unsupported_machine_arch = "不支援的主機電腦架構。({0})";
	error_working_directory_not_found = "工作目錄不存在。";
	error_x8664_required = "不支援的作業系統。需要 Windows 64 位元 (x86-64) 版本。";
	warn_module_download_size = "HTTP 回應容量：{0}。在無用戶端模式下啟動。";
	warn_vista_deprecation = "Easy Anti-Cheat 不會再建立相容性程式碼簽章，因此必須在 2020 年 10 月結束支援 Windows Vista。請參閱 https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "無法驗證防作弊設定。";
	warn_win7_update_required = "您的系統缺少所需的關鍵 SHA-2 程式碼簽章支援，因此請在 2020 年 10 月之前更新 Windows。請參閱 https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Easy Anti-Cheat 服務更新已逾時。"
	error_launch_ex = "啟動錯誤: {0}";
};
setup:
{
	btn_finish = "完成";
	btn_install = "立即安裝";
	btn_repair = "修復服務";
	btn_uninstall = "卸載";
	epic_link = "© Epic Games, Inc";
	install_progress = "安裝中……";
	install_success = "成功安裝";
	licenses_link = "授權協議";
	privacy_link = "私隱";
	repair_progress = "修復中……";
	title = "Easy Anti-Cheat服務安裝";
	uninstall_progress = "卸載中……";
	uninstall_success = "成功卸載";
};
setup_error:
{
	error_cancelled = "操作已由使用者取消";
	error_encrypted = "「Easy Anti-Cheat」安裝資料夾遭到鎖碼";
	error_intro = "Easy Anti-Cheat 設定失敗";
	error_not_installed = "未安裝 Easy Anti-Cheat。";
	error_registry = "存取註冊表被拒絕";
	error_rights = "許可權不足";
	error_service = "無法建立服務";
	error_service_ex = "無法建立服務 {0}";
	error_system = "存取 System32 被拒絕";
};