"""
Usage:
To convert texture to pct:
    1) Create and paint tga texture
    2) Create td file with corresponding name in project/assets/td directory
    3) Setup convert params and materials in td file
    3) Run convert_pct.py <src_path>
To convert pct to texture:
    1) Run convert_pct.py <src_path> <dst_path>
"""

import os
import sys
import shutil
from dataclasses import dataclass
import subprocess


CWD = os.path.dirname(os.path.realpath(__file__))

PROJECT_DIR = os.path.join(CWD, 'project')
PCT_DIR = os.path.join(PROJECT_DIR, 'resources', 'pct')
TD_DIR = os.path.join(PROJECT_DIR, 'assets', 'td')

BINARIES_DIR = os.path.join(PROJECT_DIR, 'bin')
TEXTURE_CONVERTER_EXE = os.path.join(BINARIES_DIR, 'TextureConverter.exe')
PCT_RES_GEN_EXE = os.path.join(BINARIES_DIR, 'pct_resource_gen.exe')


def _execute_subprocess(cmd_args):
    startupinfo = subprocess.STARTUPINFO()
    # this bit is needed if process creates window
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE

    print(' '.join(cmd_args))

    cmd_process = subprocess.Popen(
        cmd_args,
        startupinfo=startupinfo,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd=PROJECT_DIR)
        
    stdout, stderr = cmd_process.communicate()
    print(stdout.decode())
    if stderr:
        print(stderr.decode())
    
    return cmd_process.wait()


@dataclass(frozen=True)
class TextureConverterContext:
    src: str
    dst: str
    options: str


def convert_texture(ctx: TextureConverterContext):
    cmd = [
        TEXTURE_CONVERTER_EXE,
        ctx.src,
        ctx.dst,
    ]

    if ctx.options:
        cmd.extend(['--cfg', ctx.options])

    return _execute_subprocess(cmd)


def generate_texture_resources(ctx: TextureConverterContext):
    cmd = [
        PCT_RES_GEN_EXE,
        '--project', PROJECT_DIR,
        '--pct-path', ctx.dst,
        '--source', ctx.src,
        '--silent'
    ]

    return _execute_subprocess(cmd)


def convert_main(ctx: TextureConverterContext):
    result = convert_texture(ctx)
    if result != 0:
        print(f'Error: texture conversion failed with code {result}')
        return result
    result = generate_texture_resources(ctx)
    if result != 0:
        print(f'Error: texture resource generation failed with code {result}')
        return result
    return 0

def main(args):
    if len(args) < 1:
        print('Error: missing source path')
        return 1
    tex_path = args[0]
    if not os.path.exists(tex_path):
        print(f'Error: file {tex_path} not found')
        return 1
        
    tex_name, _ = os.path.splitext(os.path.basename(tex_path))
    if len(args) == 1:
        if not os.path.exists(os.path.join(TD_DIR, tex_name + '.td')):
            print(f'Error: td file for texture {tex_name} not found')
            return 1

        return convert_main(TextureConverterContext(
            src=tex_path,
            dst=os.path.join(PCT_DIR, tex_name + '.pct'),
            options=os.path.join(TD_DIR, tex_name + '.td')
        ))
    
    dst_path = args[1]
    return convert_texture(
        TextureConverterContext(
            src=tex_path,
            dst=dst_path,
            options=None
        )
    )


def test_convert(texture_name: str):
    tex_path = os.path.join(CWD, 'examples', 'textures', texture_name + '.tga')
    td_path = os.path.join(CWD, 'examples', 'textures', texture_name + '.td')

    os.makedirs(TD_DIR, exist_ok=True)
    try:
        shutil.copy(td_path, TD_DIR)
    except shutil.Error as e:
        print(e)

    main([tex_path])
    
    tex_name, ext = os.path.splitext(os.path.basename(tex_path))
    dst=os.path.join(PCT_DIR, tex_name + '.pct')
    convert_back = dst.replace('.pct', '.tga')
    main([dst, convert_back])


if __name__ == '__main__':
    result = main(sys.argv[1:])
    #result = test_convert('ch_titus_body')
    sys.exit(result)
