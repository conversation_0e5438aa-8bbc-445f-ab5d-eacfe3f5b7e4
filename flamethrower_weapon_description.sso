// Space Marine 2 - Flamethrower Weapon Description
// This file defines the basic properties of our custom flamethrower

flamethrower_heavy = {
    // Basic weapon identification
    weaponUid = "flamethrower_heavy"
    weaponName = "Heavy Flamethrower"
    weaponCategory = "heavy"
    
    // Weapon stats
    damage = {
        baseDamage = 45.0          // High damage per tick
        damageType = "fire"        // Fire damage type
        armorPenetration = 0.3     // Low armor penetration
        criticalMultiplier = 1.5   // Moderate crit damage
    }
    
    // Fire characteristics
    fireMode = {
        type = "continuous"        // Continuous fire like a beam
        fireRate = 20.0           // 20 damage ticks per second
        burstLength = -1          // Infinite burst (continuous)
        spinUpTime = 0.5          // 0.5 second spin-up time
        cooldownTime = 2.0        // 2 second cooldown after overheating
    }
    
    // Ammo and capacity
    ammoSettings = {
        maxAmmo = 200             // 200 fuel units
        reloadTime = 4.0          // 4 second reload
        ammoPerShot = 2           // 2 fuel per damage tick
        clipSize = 200            // Full tank capacity
    }
    
    // Range and accuracy
    rangeSettings = {
        effectiveRange = 15.0     // 15 meter effective range
        maxRange = 20.0           // 20 meter maximum range
        falloffStart = 10.0       // Damage falloff starts at 10m
        falloffEnd = 20.0         // No damage beyond 20m
    }
    
    // Projectile behavior
    projectileSettings = {
        projectileType = "flame_stream"
        speed = 25.0              // Relatively slow projectile
        gravity = 0.5             // Slight gravity effect
        lifetime = 0.8            // Projectile lives for 0.8 seconds
        penetration = 0           // Cannot penetrate targets
        areaOfEffect = 3.0        // 3 meter AoE radius
    }
    
    // Status effects
    statusEffects = {
        burning = {
            enabled = true
            duration = 5.0        // 5 second burn duration
            damagePerSecond = 10.0 // 10 damage per second while burning
            stackable = true      // Multiple burns can stack
            maxStacks = 3         // Maximum 3 burn stacks
        }
    }
    
    // Visual effects
    visualEffects = {
        muzzleFlash = "sfx_flamethrower_muzzle"
        projectileTrail = "sfx_flame_stream"
        impactEffect = "sfx_flame_impact"
        scorchMark = "scr_flamethrower"
        
        // Flame colors (RGB values)
        flameColor = [255, 100, 0]    // Orange flame
        flameIntensity = 2.0          // Bright flames
        smokeEffect = "sfx_flame_smoke"
    }
    
    // Audio effects
    audioEffects = {
        fireSound = "wpn_flamethrower_fire"
        reloadSound = "wpn_flamethrower_reload"
        emptySound = "wpn_flamethrower_empty"
        spinUpSound = "wpn_flamethrower_spinup"
        cooldownSound = "wpn_flamethrower_cooldown"
    }
    
    // Weapon handling
    handling = {
        weight = 8.5              // Heavy weapon
        movementSpeedMultiplier = 0.7  // 30% movement speed reduction
        aimDownSightTime = 1.2    // Slow ADS
        hipFireAccuracy = 0.9     // Good hip fire accuracy (close range weapon)
        recoil = {
            vertical = 0.2        // Low vertical recoil
            horizontal = 0.1      // Very low horizontal recoil
            pattern = "flame"     // Custom recoil pattern
        }
    }
    
    // Special mechanics
    specialMechanics = {
        overheating = {
            enabled = true
            maxHeat = 100.0       // Maximum heat level
            heatPerShot = 1.5     // Heat generated per damage tick
            coolingRate = 15.0    // Heat dissipation per second
            overheatPenalty = 3.0 // 3 second forced cooldown when overheated
        }
        
        environmentalEffects = {
            igniteEnvironment = true    // Can ignite flammable objects
            clearVegetation = true      // Burns through vegetation
            meltsIce = true            // Melts ice/snow
        }
    }
    
    // UI and progression
    uiSettings = {
        weaponIcon = "ui_flamethrower_icon"
        weaponDescription = "Heavy flamethrower that deals continuous fire damage and applies burning effects to enemies."
        unlockLevel = 15          // Unlocked at level 15
        masteryProgression = {
            tier1 = "increased_range"
            tier2 = "reduced_fuel_consumption"
            tier3 = "enhanced_burning_effect"
            tier4 = "faster_cooldown"
            tier5 = "explosive_finale"
        }
    }
    
    __type = "FirearmDescription"
}
