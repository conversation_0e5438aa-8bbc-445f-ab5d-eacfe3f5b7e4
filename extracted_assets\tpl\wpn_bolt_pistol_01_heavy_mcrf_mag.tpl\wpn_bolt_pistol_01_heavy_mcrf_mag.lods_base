lodInfo = {
   maxLodDist = [

   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      rb_magazine = {
         collision = {
            abilityProjectile = false
            light = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            soundOcclusion = true
            soundObstruction = true
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      magazine_01_dropped = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      magazine_01_dropped_LOD1 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      magazine_01_dropped_LOD2 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      magazine_01_dropped_LOD3 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      magazine_01_dropped_LOD4 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
