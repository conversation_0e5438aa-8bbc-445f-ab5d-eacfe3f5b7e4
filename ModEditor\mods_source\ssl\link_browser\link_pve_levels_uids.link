__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __id   =   ""
            __showOnlyChildren   =   False
            __path   =   "levelUid"
            __format   =   "{value}"
            __showValue   =   True
            __where   =   {
               __name   =   ""
               __type   =   ""
               __value   =   ""
            }
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __path   =   "chapters/*"
         __format   =   "{index}"
         __type   =   "model"
      }
      __path   =   "episodes/*"
      __format   =   "{name}"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "PveEpisodesLibrary"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

