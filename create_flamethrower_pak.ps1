# Space Marine 2 - Create Flamethrower .pak Mod
# This creates a proper .pak file for the flamethrower mod

Write-Host "Creating Flamethrower .pak Mod..." -ForegroundColor Green

# Create temporary directory for pak contents
$TempDir = "temp_flamethrower_mod"
$PakDir = "$TempDir\ssl\weapons"

if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}

New-Item -ItemType Directory -Path $PakDir -Force | Out-Null

# Create the flamethrower mod file
$ModContent = @"
// Flamethrower Pistol Mod for Space Marine 2
// Replaces bolt pistol with overpowered flamethrower

FirearmLibraryPve = {
    firearms = {
        bolt_pistol = {
            uid = "bolt_pistol"
            name = "Bolt Pistol"
            
            damage = {
                base = 500.0
                armorPenetration = 0.1
                criticalMultiplier = 2.0
                falloffStart = 8.0
                falloffEnd = 15.0
            }
            
            fireRate = {
                roundsPerMinute = 2000
                burstLength = 30
                spinUpTime = 0.05
                cooldownTime = 0.3
            }
            
            ammo = {
                maxAmmo = 999
                clipSize = 150
                reloadTime = 1.0
                ammoPerShot = 1
            }
            
            range = {
                effective = 12.0
                maximum = 18.0
                optimal = 6.0
            }
            
            accuracy = {
                hipFire = 1.0
                aimDownSight = 1.0
                movementPenalty = 0.0
                recoil = {
                    vertical = 0.0
                    horizontal = 0.0
                    pattern = "none"
                }
            }
            
            handling = {
                weight = 1.0
                aimDownSightTime = 0.1
                movementSpeedMultiplier = 1.0
                swapTime = 0.3
            }
            
            projectile = {
                type = "explosive"
                speed = 60.0
                gravity = 0.05
                lifetime = 0.3
                penetration = 10
                areaOfEffect = 4.0
                maxTargets = 15
            }
            
            specialMechanics = {
                areaDamage = {
                    enabled = true
                    radius = 4.0
                    damageMultiplier = 1.0
                    falloffType = "linear"
                    friendlyFire = false
                }
                
                statusEffects = {
                    burning = {
                        enabled = true
                        applyChance = 1.0
                        duration = 8.0
                        damagePerSecond = 50.0
                        stackable = true
                        maxStacks = 10
                    }
                }
            }
            
            ui = {
                icon = "ui_bolt_pistol_icon"
                description = "Overpowered flamethrower disguised as a pistol."
                weaponClass = "Pistol"
                unlockLevel = 1
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
"@

# Write the mod file
$ModContent | Out-File "$PakDir\flamethrower_pistol.sso" -Encoding UTF8

Write-Host "Created mod file: $PakDir\flamethrower_pistol.sso" -ForegroundColor Yellow

# Try to create a simple zip file as .pak (many games use zip format for pak files)
$PakFile = "client_pc\root\mods\flamethrower_pistol.pak"

# Remove existing pak file
if (Test-Path $PakFile) {
    Remove-Item $PakFile -Force
}

# Create pak file (as zip)
try {
    Compress-Archive -Path "$TempDir\*" -DestinationPath $PakFile -Force
    Write-Host "Created pak file: $PakFile" -ForegroundColor Green
} catch {
    Write-Warning "Could not create pak file automatically. Manual creation needed."
}

# Clean up temp directory
Remove-Item $TempDir -Recurse -Force

# Update pak config
$ConfigFile = "client_pc\root\mods\pak_config.yaml"
$ConfigContent = @"
# Flamethrower Pistol Mod Configuration
- pak: flamethrower_pistol.pak
"@

$ConfigContent | Out-File $ConfigFile -Encoding UTF8

Write-Host ""
Write-Host "FLAMETHROWER PAK MOD CREATED!" -ForegroundColor Red
Write-Host "=============================" -ForegroundColor Red
Write-Host ""
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- $PakFile" -ForegroundColor White
Write-Host "- $ConfigFile" -ForegroundColor White
Write-Host ""
Write-Host "Now:" -ForegroundColor Yellow
Write-Host "1. Close Space Marine 2 completely" -ForegroundColor White
Write-Host "2. Launch Space Marine 2" -ForegroundColor White
Write-Host "3. Test your pistol - should be overpowered!" -ForegroundColor White
Write-Host ""
Write-Host "Pistol Stats:" -ForegroundColor Green
Write-Host "- 500 damage per shot" -ForegroundColor White
Write-Host "- 4m area damage" -ForegroundColor White
Write-Host "- 999 ammo" -ForegroundColor White
Write-Host "- Perfect accuracy" -ForegroundColor White
Write-Host "- Burning effects" -ForegroundColor White
Write-Host ""
Write-Host "Ready to burn heretics!" -ForegroundColor Red
