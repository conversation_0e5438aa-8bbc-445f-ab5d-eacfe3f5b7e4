__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __tag   =   "net_sync"
               __type   =   "modelParentWithTag"
            }
            __showOnlyChildren   =   True
            __path   =   "variables/*"
            __type   =   "model"
         }
         __format   =   "{name}"
         __path   =   "blueprints/*"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __where   =   {
         __parentType   =   "prop_blueprints"
      }
      __path   =   "/Content/properties/*"
      __type   =   "model"
   }
   __type   =   "document"
}
__type   =   "linkBrowser"


