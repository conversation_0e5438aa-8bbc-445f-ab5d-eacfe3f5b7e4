__request   =   {
   __select   =   {
      __select   =   {
         __id   =   ""
         __showOnlyChildren   =   False
         __path   =   "knowledgeModules/AiKnowledgeModuleCachedQuery/querySettings/*"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __where   =   {
         __parentType   =   "prop_ai_knowledge"
      }
      __path   =   "properties/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   False
   __family   =   ".cls"
   __groupByParents   =   False
   __format   =   "{name}"
   __where   =   {
      __name   =   "npc_"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

