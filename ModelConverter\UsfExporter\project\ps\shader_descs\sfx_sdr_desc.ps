sfx {
   __caption = "sfx"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   blendMode {
      __type = "enum"
      __list = ["blend", "add", "premul_a"]
   }
   
   isUseForPortals {
      __type = "bool"
   }
   
   alphaKillSmooth {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      smoothness {
         __type = float
      }
      
   }
   
   animationFrameBlend {
      __type = "bool"
   }
   
   overrideBloom {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      tint {
         __type = "color"
      }
      
   }
   
   emissive {
      __type = "section"
      intensity {
         __type = float
      }
      
      adaptiveIntensity {
         __type = "bool"
      }
      
      bloomIntensity {
         __type = float
      }
      
   }
   
   lighting {
      __type = "section"
      technique {
         __type = "enum"
         __list = ["none", "vertex", "pixel"]
      }
      
      normalmaps {
         __type = "section"
         normalmap = {
            __type = "texture"
            __callback = "nm"
         }
         
         normalmapScaleX {
            __type = float
         }
         
         normalmapScaleY {
            __type = float
         }
         
         normalmapSpeedScaleX {
            __type = float
         }
         
         normalmapSpeedScaleY {
            __type = float
         }
         
         detailNormalmap = {
            __type = "texture"
            __callback = "nm"
         }
         
         detailNormalmapScaleX {
            __type = float
         }
         
         detailNormalmapScaleY {
            __type = float
         }
         
         detailNormalmapSpeedScaleX {
            __type = float
         }
         
         detailNormalmapSpeedScaleY {
            __type = float
         }
         
         detailNormalmapRotation {
            __type = float
         }
         
         detailNormalmapBumpiness {
            __type = float
         }
         
         normalBumpiness {
            __type = float
         }
         
         normalSSRMultiplier {
            __type = float
         }
         
      }
      
      reflectance {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      reflection_intensity {
         __type = float
      }
      
      sfx_color_intensity {
         __type = float
      }
      
      roughness {
         __type = float
      }
      
      roughness_overwrite_multiplier {
         __type = float
      }
      
      metalness {
         __type = float
      }
      
   }
   
   tint {
      __type = "color"
   }
   
   disableTint {
      __type = "bool"
   }
   
   lerpBaseColorWithTint {
      __type = "bool"
   }
   
   tintLerpTex = {
      __type = "texture"
      __callback = ""
   }
   
   hueShift {
      __type = float
   }
   
   alphaLevelStart {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   alphaLevelEnd {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   overrideAffixScroll {
      __type = "section"
      override {
         __type = "bool"
      }
      
      speedX {
         __type = float
      }
      
      speedY {
         __type = float
      }
      
   }
   
   gradientScroll {
      __type = "section"
      tex = {
         __type = "texture"
         __callback = ""
      }
      
      speed {
         __type = float
      }
      
   }
   
   gradientBlink {
      __type = "section"
      tex = {
         __type = "texture"
         __callback = ""
      }
      
      speed {
         __type = float
      }
      
   }
   
   softZ {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      inverted {
         __type = "bool"
      }
      
      range {
         __type = float
      }
      
      nearEnabled {
         __type = "bool"
      }
      
      nearRangeStart {
         __type = float
      }
      
      nearRange {
         __type = float
      }
      
      power {
         __type = float
      }
      
   }
   
   softFreshnel {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      power {
         __type = float
      }
      
   }
   
   edgeHighlight {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      intensity {
         __type = float
      }
      
      power {
         __type = float
      }
      
      blendMode {
         __type = "enum"
         __list = ["add", "mul"]
      }
      
   }
   
   fakeLight {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      color {
         __type = "color"
      }
      
      intensity {
         __type = float
      }
      
      sharpness {
         __type = float
      }
      
      rotation {
         __type = "section"
         angle_x {
            __type = float
         }
         
         angle_y {
            __type = float
         }
         
         angle_z {
            __type = float
         }
         
      }
      
      useCamDir {
         __type = "bool"
      }
      
   }
   
   animPeriod {
      __type = float
   }
   
   scrollSpeedScaleX {
      __type = float
   }
   
   scrollSpeedScaleY {
      __type = float
   }
   
   clipping {
      __type = "section"
      level {
         __type = float
      }
      
      smoothness {
         __type = float
      }
      
   }
   
   flowmap {
      __type = "section"
      flowTex = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   tex = {
      __type = "texture"
      __callback = ""
   }
   
   layerFirst {
      __type = "section"
      textureScaleX {
         __type = float
      }
      
      textureScaleY {
         __type = float
      }
      
      intensity {
         __type = float
      }
      
      disable {
         __type = "bool"
      }
      
      flowmapPeriod {
         __type = float
      }
      
      flowmapStrength {
         __type = float
      }
      
      tint {
         __type = "color"
      }
      
      lerpTint {
         __type = "section"
         enable {
            __type = "bool"
         }
         
         invert {
            __type = "bool"
         }
         
         tint {
            __type = "color"
         }
         
      }
      
      saturation {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      brightness {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      contrast {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      gradientScroll {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         speed {
            __type = float
         }
         
      }
      
      softFreshnel {
         __type = "section"
         power {
            __type = float
         }
         
      }
      
      edgeHighlight {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         intensity {
            __type = float
         }
         
         power {
            __type = float
         }
         
         blendMode {
            __type = "enum"
            __list = ["add", "mul"]
         }
         
      }
      
      offsetUV {
         __type = "section"
         offsetU {
            __type = float
         }
         
         offsetV {
            __type = float
         }
         
      }
      
   }
   
   distort {
      __type = "section"
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      distortTexture = {
         __type = "texture"
         __callback = "nm"
      }
      
      distortTextureScaleX {
         __type = float
      }
      
      distortTextureScaleY {
         __type = float
      }
      
      distortTextureRotation {
         __type = float
      }
      
      distortionScale {
         __type = float
      }
      
      offsetUV {
         __type = "section"
         offsetU {
            __type = float
         }
         
         offsetV {
            __type = float
         }
         
      }
      
   }
   
   layerSecond {
      __type = "section"
      UVProjection {
         __type = "enum"
         __list = ["none", "XY", "XZ", "ZY"]
      }
      
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      texture = {
         __type = "texture"
         __callback = ""
      }
      
      textureScaleX {
         __type = float
      }
      
      textureScaleY {
         __type = float
      }
      
      textureRotation {
         __type = float
      }
      
      blendMode {
         __type = "enum"
         __list = ["mul", "add", "sub", "min", "max", "normalmask"]
      }
      
      alphaSource {
         __type = "enum"
         __list = ["base_layer_inverse", "one"]
      }
      
      intensity {
         __type = float
      }
      
      disable {
         __type = "bool"
      }
      
      flowmapPeriod {
         __type = float
      }
      
      flowmapStrength {
         __type = float
      }
      
      tint {
         __type = "color"
      }
      
      lerpTint {
         __type = "section"
         enable {
            __type = "bool"
         }
         
         invert {
            __type = "bool"
         }
         
         tint {
            __type = "color"
         }
         
      }
      
      saturation {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      brightness {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      contrast {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      gradientScroll {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         speed {
            __type = float
         }
         
      }
      
      softFreshnel {
         __type = "section"
         power {
            __type = float
         }
         
      }
      
      edgeHighlight {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         intensity {
            __type = float
         }
         
         power {
            __type = float
         }
         
         blendMode {
            __type = "enum"
            __list = ["add", "mul"]
         }
         
      }
      
      offsetUV {
         __type = "section"
         offsetU {
            __type = float
         }
         
         offsetV {
            __type = float
         }
         
      }
      
   }
   
   distortSecondLayer {
      __type = "section"
      UVProjection {
         __type = "enum"
         __list = ["none", "XY", "XZ", "ZY"]
      }
      
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      distortTexture = {
         __type = "texture"
         __callback = "nm"
      }
      
      distortTextureScaleX {
         __type = float
      }
      
      distortTextureScaleY {
         __type = float
      }
      
      distortTextureRotation {
         __type = float
      }
      
      distortionScale {
         __type = float
      }
      
   }
   
   layerThird {
      __type = "section"
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      texture = {
         __type = "texture"
         __callback = ""
      }
      
      textureScaleX {
         __type = float
      }
      
      textureScaleY {
         __type = float
      }
      
      textureRotation {
         __type = float
      }
      
      blendMode {
         __type = "enum"
         __list = ["mul", "add", "sub", "min", "max"]
      }
      
      alphaSource {
         __type = "enum"
         __list = ["base_layer_inverse", "one"]
      }
      
      intensity {
         __type = float
      }
      
      disable {
         __type = "bool"
      }
      
      flowmapPeriod {
         __type = float
      }
      
      flowmapStrength {
         __type = float
      }
      
      tint {
         __type = "color"
      }
      
      lerpTint {
         __type = "section"
         enable {
            __type = "bool"
         }
         
         invert {
            __type = "bool"
         }
         
         tint {
            __type = "color"
         }
         
      }
      
      saturation {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      brightness {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      contrast {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      gradientScroll {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         speed {
            __type = float
         }
         
      }
      
      softFreshnel {
         __type = "section"
         power {
            __type = float
         }
         
      }
      
      edgeHighlight {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         intensity {
            __type = float
         }
         
         power {
            __type = float
         }
         
         blendMode {
            __type = "enum"
            __list = ["add", "mul"]
         }
         
      }
      
      offsetUV {
         __type = "section"
         offsetU {
            __type = float
         }
         
         offsetV {
            __type = float
         }
         
      }
      
   }
   
   distortThirdLayer {
      __type = "section"
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      distortTexture = {
         __type = "texture"
         __callback = "nm"
      }
      
      distortTextureScaleX {
         __type = float
      }
      
      distortTextureScaleY {
         __type = float
      }
      
      distortTextureRotation {
         __type = float
      }
      
      distortionScale {
         __type = float
      }
      
   }
   
   distortBackground {
      __type = "section"
      affectedBySelfDistortion {
         __type = "bool"
      }
      
      strength {
         __type = float
      }
      
      texture = {
         __type = "texture"
         __callback = "nm"
      }
      
      textureScaleX {
         __type = float
      }
      
      textureScaleY {
         __type = float
      }
      
      textureRotation {
         __type = float
      }
      
      speedScaleX {
         __type = float
      }
      
      speedScaleY {
         __type = float
      }
      
      flowmapPeriod {
         __type = float
      }
      
      flowmapStrength {
         __type = float
      }
      
      useWCS {
         __type = "bool"
      }
      
   }
   
   dynamicAlphaMask {
      __type = "section"
      mask = {
         __type = "texture"
         __callback = ""
      }
      
      doNotUseDynamic {
         __type = "bool"
      }
      
      useAlphaFromLayerFirst {
         __type = "bool"
      }
      
      maskScrollX {
         __type = float
      }
      
      maskScrollY {
         __type = float
      }
      
      maskScaleX {
         __type = float
      }
      
      maskScaleY {
         __type = float
      }
      
      alphaVal_min {
         __type = float
      }
      
      alphaVal_max {
         __type = float
      }
      
      softBorder {
         __type = float
      }
      
      generateSoftBorderNormal {
         __type = "bool"
      }
      
      softBorderNormalMultiplier {
         __type = float
      }
      
   }
   
   writeDepth {
      __type = "bool"
   }
   
   zTest {
      __type = "bool"
   }
   
   noCull {
      __type = "bool"
   }
   
   particle {
      __type = "bool"
   }
   
   disable_fog {
      __type = "bool"
   }
   
   noVertexColor {
      __type = "bool"
   }
   
   dissolvable {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      invert {
         __type = "bool"
      }
      
      noiseTexture = {
         __type = "texture"
         __callback = ""
      }
      
      patternTexture = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   extrude {
      __type = float
   }
   
   rotationMap = {
      __type = "texture"
      __callback = "dir"
   }
   
   animatedTextures {
      __type = "section"
      animationTimerate {
         __type = float
      }
      
      normalmap_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
      normalmap_detail_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
      layer0Texture_arr = {
         __type = "texture"
         __callback = ""
      }
      
      layer0Distort_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
      layer1Texture_arr = {
         __type = "texture"
         __callback = ""
      }
      
      layer1Distort_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
      layer2Texture_arr = {
         __type = "texture"
         __callback = ""
      }
      
      layer2Distort_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
      distortBgTex_arr = {
         __type = "texture"
         __callback = "nm"
      }
      
   }
   
   temporalAA {
      __type = "section"
      affectTransparencyMask {
         __type = "bool"
      }
      
      affectReactiveMask {
         __type = "bool"
      }
      
      transparencyMaskScale {
         __type = float
         __min = 0.000000
      }
      
      reactiveMaskScale {
         __type = float
         __min = 0.000000
      }
      
   }
   
   disableOcclusionTest {
      __type = "bool"
   }
   
   finalAlphaMask {
      __type = "section"
      texture = {
         __type = "texture"
         __callback = ""
      }
      
      coordinates {
         __type = "enum"
         __list = ["UV", "XY", "XZ", "ZY"]
      }
      
   }
   
   finalColor {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      tint {
         __type = "color"
      }
      
      saturation {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      brightness {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      contrast {
         __type = "slider"
         __min = -1.000000
         __max = 1.000000
         __step = 0.010000
      }
      
   }
   
   vertex_pulsation {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      source_texture = {
         __type = "texture"
         __callback = ""
      }
      
      texture_uv_params {
         __type = "section"
         speedScaleX {
            __type = float
         }
         
         speedScaleY {
            __type = float
         }
         
         texScaleX {
            __type = float
         }
         
         texScaleY {
            __type = float
         }
         
      }
      
      bending {
         __type = "section"
         amplitude {
            __type = float
         }
         
         speed {
            __type = float
         }
         
      }
      
      noise {
         __type = "section"
         amplitude {
            __type = float
         }
         
         speed {
            __type = float
         }
         
      }
      
   }
   
}