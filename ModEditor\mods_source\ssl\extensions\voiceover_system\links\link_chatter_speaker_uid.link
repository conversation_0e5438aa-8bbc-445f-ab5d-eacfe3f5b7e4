__request   =   {
   __select   =   {
      __select   =   {
         __id   =   "speaker"
         __onlyChanged   =   False
         __where   =   {
            __parentType   =   "^ChatSettingsSpeaker$"
         }
         __type   =   "modelRecursive"
      }
      __path   =   "chatSettings/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "Chatter"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{speaker}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"


