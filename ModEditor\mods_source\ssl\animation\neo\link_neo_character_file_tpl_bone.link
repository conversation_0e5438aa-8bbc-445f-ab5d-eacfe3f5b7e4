__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __format   =   "{value}"
                  __showValue   =   True
                  __path   =   "objectId/name"
                  __type   =   "model"
               }
               __showOnlyChildren   =   True
               __type   =   "modelParent"
            }
            __showOnlyChildren   =   True
            __where   =   {
               __name   =   "animationRole"
               __value   =   "(RIG_INCLUDE|RIG_CHARACTER_ROOT|RIG_TRAJECTORY)"
            }
            __type   =   "modelRecursive"
         }
         __showOnlyChildren   =   True
         __type   =   "tplMarkup"
      }
      __showValue   =   True
      __path   =   "rig/source/templateName"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __type   =   "document"
}
__type   =   "linkBrowser"

