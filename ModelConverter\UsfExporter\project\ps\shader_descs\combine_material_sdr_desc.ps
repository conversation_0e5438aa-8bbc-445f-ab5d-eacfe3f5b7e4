combine_material {
   __caption = "combine_material"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   no_backface_culling {
      __type = "bool"
   }
   
   disableSsao {
      __type = "bool"
   }
   
   disableSSR {
      __type = "bool"
   }
   
   unlit {
      __type = "bool"
   }
   
   reflectance {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   porosity {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   occlusionMaskScale {
      __type = float
      __min = 0.000000
      __max = 8.000000
   }
   
   blendMode {
      __type = "enum"
      __list = ["none", "blend", "add"]
   }
   
   opticalSightType {
      __type = "enum"
      __list = ["none", "telescopic", "collimator"]
   }
   
   vertex_anim_disable_blending {
      __type = "bool"
   }
   
   vertex_anim_use_double_precise {
      __type = "bool"
   }
   
   shaderType {
      __type = "enum"
      __list = ["shader0", "shader1", "shader2", "shader3", "shader4", "shader5", "shader6", "shader7", "shader_far_rain", "shader_blood_splash", "shader_wipers", "shader_eye_occlusion", "shader_eye_lacrimal"]
   }
   
   buffer_params {
      __type = "section"
      colorParam0 {
         __type = "color"
      }
      
      colorParam1 {
         __type = "color"
      }
      
      colorParam2 {
         __type = "color"
      }
      
      colorParam3 {
         __type = "color"
      }
      
      colorParam4 {
         __type = "color"
      }
      
      floatParam0 {
         __type = float
      }
      
      floatParam1 {
         __type = float
      }
      
      floatParam2 {
         __type = float
      }
      
      floatParam3 {
         __type = float
      }
      
      floatParam4 {
         __type = float
      }
      
      floatParam5 {
         __type = float
      }
      
      floatParam6 {
         __type = float
      }
      
      floatParam7 {
         __type = float
      }
      
      floatParam8 {
         __type = float
      }
      
      floatParam9 {
         __type = float
      }
      
      floatParam10 {
         __type = float
      }
      
      floatParam11 {
         __type = float
      }
      
      floatParam12 {
         __type = float
      }
      
      floatParam13 {
         __type = float
      }
      
      floatParam14 {
         __type = float
      }
      
      floatParam15 {
         __type = float
      }
      
      floatParam16 {
         __type = float
      }
      
      floatParam17 {
         __type = float
      }
      
      floatParam18 {
         __type = float
      }
      
      floatParam19 {
         __type = float
      }
      
      floatParam20 {
         __type = float
      }
      
      floatParam21 {
         __type = float
      }
      
      floatParam22 {
         __type = float
      }
      
      floatParam23 {
         __type = float
      }
      
      floatParam24 {
         __type = float
      }
      
      floatParam25 {
         __type = float
      }
      
      floatParam26 {
         __type = float
      }
      
      floatParam27 {
         __type = float
      }
      
      floatParam28 {
         __type = float
      }
      
      floatParam29 {
         __type = float
      }
      
      floatParam30 {
         __type = float
      }
      
      floatParam31 {
         __type = float
      }
      
      floatParam32 {
         __type = float
      }
      
      floatParam33 {
         __type = float
      }
      
      floatParam34 {
         __type = float
      }
      
      floatParam35 {
         __type = float
      }
      
      floatParam36 {
         __type = float
      }
      
      floatParam37 {
         __type = float
      }
      
      floatParam38 {
         __type = float
      }
      
      floatParam39 {
         __type = float
      }
      
   }
   
   far_rain_params {
      __type = "section"
      finalAlphaK {
         __type = float
      }
      
      reflectionPower {
         __type = float
      }
      
      reflectionMultiplier {
         __type = float
      }
      
      prescaleAlpha {
         __type = float
      }
      
      transparencyChannel {
         __type = "color"
      }
      
      finalColorMultiplier {
         __type = float
      }
      
      occlThreshold {
         __type = float
      }
      
      reflectionColor {
         __type = "color"
      }
      
      upperSmoothRange {
         __type = float
      }
      
      upperSmoothPower {
         __type = float
      }
      
      debugMode {
         __type = "bool"
      }
      
      debugShowOccluded {
         __type = "bool"
      }
      
   }
   
   external_textures {
      __type = "section"
      tex0 {
         __type = "section"
         albedo = {
            __type = "texture"
            __callback = ""
         }
         
         NM = {
            __type = "texture"
            __callback = "nm"
         }
         
         spec = {
            __type = "texture"
            __callback = "spec"
         }
         
         heightDetMask = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         emissiveTex = {
            __type = "texture"
            __callback = "em"
         }
         
         addInfo = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      tex1 {
         __type = "section"
         albedo = {
            __type = "texture"
            __callback = ""
         }
         
         NM = {
            __type = "texture"
            __callback = "nm"
         }
         
         spec = {
            __type = "texture"
            __callback = "spec"
         }
         
         heightDetMask = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         emissiveTex = {
            __type = "texture"
            __callback = "em"
         }
         
         addInfo = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      tex2 {
         __type = "section"
         albedo = {
            __type = "texture"
            __callback = ""
         }
         
         NM = {
            __type = "texture"
            __callback = "nm"
         }
         
         spec = {
            __type = "texture"
            __callback = "spec"
         }
         
         heightDetMask = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         emissiveTex = {
            __type = "texture"
            __callback = "em"
         }
         
         addInfo = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      tex3 {
         __type = "section"
         albedo = {
            __type = "texture"
            __callback = ""
         }
         
         NM = {
            __type = "texture"
            __callback = "nm"
         }
         
         spec = {
            __type = "texture"
            __callback = "spec"
         }
         
         heightDetMask = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         emissiveTex = {
            __type = "texture"
            __callback = "em"
         }
         
         addInfo = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      tex4 {
         __type = "section"
         albedo = {
            __type = "texture"
            __callback = ""
         }
         
         NM = {
            __type = "texture"
            __callback = "nm"
         }
         
         spec = {
            __type = "texture"
            __callback = "spec"
         }
         
         heightDetMask = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         emissiveTex = {
            __type = "texture"
            __callback = "em"
         }
         
         addInfo = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      texCube = {
         __type = "texture"
         __callback = "cube"
      }
      
      texArray = {
         __type = "texture"
         __callback = ""
      }
      
      texVolume = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   covering {
      __type = "section"
      coveringMask = {
         __type = "texture"
         __callback = "hdetm"
      }
      
   }
   
}