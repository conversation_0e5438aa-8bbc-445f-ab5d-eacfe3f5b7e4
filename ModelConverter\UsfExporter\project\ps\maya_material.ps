auxiliaryTextures = {
   mask = {
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = -1
   }
}
colorSets = [
]
dynamicParameters = [
]
extraUVData = {
   uvSetIdx = -1
}
extraVertexColorData = {
   colorA = {
      colorSetIdx = -1
   }
   colorB = {
      colorSetIdx = -1
   }
   colorG = {
      colorSetIdx = -1
   }
   colorR = {
      colorSetIdx = -1
   }
}
layers = [
]
material = {
   name = ""
   parent = ""
   subMaterial = ""
   type = 0
   version = 2
}
occlusion = {
   occlusionTexture = {
      colorSetIdx = -1
      isVisible = 1
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = -1
   }
}
parallax = {
   baseLayerParallax = {
      colorSetIdx = -1
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = 0
   }
   parallaxSettings = {
      enableSecondParallaxLayer = 0
      flatten = {
         colorSetIdx = -1
      }
      overrideSecondParallaxTexture = 0
   }
   secondLayerParallax = {
      colorSetIdx = -1
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = 0
   }
}
reliefNormalmaps = {
   macro = {
      end = 0.0
      falloff = 5.0
      isVisible = 1
      scale = 1.0
      start = 0.0
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = -1
   }
   micro1 = {
      end = 0.0
      falloff = 5.0
      isVisible = 1
      scale = 1.0
      start = 0.0
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = -1
   }
   micro2 = {
      end = 0.0
      falloff = 5.0
      isVisible = 1
      scale = 1.0
      start = 0.0
      textureName = ""
      tilingU = 1.0
      tilingV = 1.0
      uvSetIdx = -1
   }
}
transparency = {
   colorSetIdx = -1
   dirtModifyAlpha = 0
   enabled = 0
   multiplier = 1.0
   sources = 0
   useBlendMaskAlpha = 0
}
uvSets = [
]