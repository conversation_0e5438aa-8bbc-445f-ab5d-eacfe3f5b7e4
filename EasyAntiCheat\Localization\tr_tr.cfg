#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	light = "Segoeuil.ttf";
	regular = "Segoeui.ttf";
	bold = "Segoeuib.ttf";
};
bugreport:
{
	btn_continue = "Çözüm için internete bağlanın ve oyunu kapatın.";
	btn_exit = "Oyunu kapat.";
	btn_hidedetails = "Detayları gizle";
	btn_showdetails = "Detayları göster";
	chk_sendreport = "Hata raporu gönder";
	error_code = "Hata Kodu:";
	lbl_body1 = "Üzgünüz, oyun başlatılırken hata oluştu";
	lbl_body2 = "Bu hatayı raporlayarak çözmemize yardımcı olun.";
	lbl_body3 = "Easy Anti-Cheat çözüm için internete bağlanabilir.";
	lbl_header = "Oyun başlatılamadı";
	title = "Başlatma Hatası";
};
game_error:
{
	error_catalogue_corrupted = "Bozuk Easy Anti-Cheat Karma Kataloğu";
	error_catalogue_not_found = "EAC dizini bulunamadı";
	error_certificate_revoked = "EAC dizini sertifikası geçersiz";
	error_corrupted_memory = "Bozuk hafıza";
	error_corrupted_network = "Bozuk paket akışı";
	error_file_forbidden = "Bilinmeyen oyun dosyası";
	error_file_not_found = "Önemli bir dosya eksik";
	error_file_version = "Bilinmiyen dosya sürümü";
	error_module_forbidden = "Yasak modül";
	error_system_configuration = "Yasaklanmış sistem ayarı";
	error_system_version = "Güvenli olmayan sistem dosyası";
	error_tool_forbidden = "Yasaklanmış araç";
	error_violation = "İçsel anti hile hatası";
	error_virtual = "Sanal makinede çalıştırılamaz.";
	peer_client_banned = "Anti-hile eşdüzey doğrulama yasaklandı.";
	peer_heartbeat_rejected = "Anti-hile eşdüzey doğrulama reddedildi.";
	peer_validated = "Anti-hile eşdüzey doğrulama tamamlandı.";
	peer_validation_failed = "Anti-hile eşdüzey doğrulama başarısız oldu.";
	executable_not_hashed = "Katalogda yürütülebilir oyun girdisi bulunamadı.";
};
launcher:
{
	btn_cancel = "İptal";
	btn_exit = "Çıkış";
	error_cancel = "Başlatma İptal";
	error_filenotfound = "Dosya Bulunamadı";
	error_init = "Başlatma Hatası";
	error_install = "Yükleme Hatası";
	error_launch = "Başlatma Hatası";
	error_nolib = "Easy Anti-Cheat kütüphanesi yüklenemedi";
	loading = "YÜKLENIYOR";
	wait = "Lütfen bekleyin";
	initializing = "BAŞLATILIYOR";
	success_waiting_for_game = "OYUN İÇİN BEKLENİYOR";
	success_closing = "Başarılı";
	network_error = "Ağ hatası";
	error_no_settings_file = "{0} bulunamadı";
	error_invalid_settings_format = "{0} geçerli bir JSON formatına sahip değil";
	error_missing_required_field = "{0} üzerinde gerekli bir alan eksik ({1})";
	error_invalid_eos_identifier = "{0} geçersiz bir EOS tanımlayıcısı içeriyor: ({1})";
	download_progress = "İndirme İlerlemesi: {0}";
};
launcher_error:
{
	error_already_running = "Easy Anti-Cheat kullanan başka bir uygulama  çalışıyor! {0}";
	error_application = "Oyun istemcisi bir uygulama hatasıyla karşılaştı. Hata Kodu: {0}";
	error_bad_exe_format = "64 Bit İşletim Sistemi gereklidir";
	error_bitset_32 = "Oyunun 32-bit sürümünü kullanın";
	error_bitset_64 = "Oyunun 64-bit sürümünü kullanın";
	error_cancelled = "İşlem kullanıcı tarafından iptal edildi";
	error_certificate_validation = "Easy Anti-Cheat kod imza sertifikası doğrulama hatası";
	error_connection = "İçerik Dağıtım Ağı'na bağlanılamadı!";
	error_debugger = "Debugger saptandı. Lütfen onu silip tekrar deneyin";
	error_disk_space = "Yetersiz disk alanı.";
	error_dns = "İçerik Dağıtım Ağı için DNS çözümlemesi başarısız";
	error_dotlocal = "DotLocal DLL yönlendirme saptandı.";
	error_dotlocal_instructions = "Lütfen şu dosyayı silin";
	error_file_not_found = "Dosya bulunamadı:";
	error_forbidden_tool = "Oyunu başlatmadan önce {0} uygulamasını kapatın";
	error_forbidden_driver = "Lütfen oyunu başlatmadan önce {0} değerini kaldır";
	error_generic = "Beklenmeyen hata.";
	error_kernel_debug = "Kernel Debugger açıkken Easy Anti-Cheat çalıştırılamaz";
	error_kernel_dse = "Sürücü İmza Zorunluluğu devre dışıysa Easy Anti-Cheat çalıştırılamaz";
	error_kernel_modified = "Yasaklanmış Windows kernel modifikasyonu saptandı";
	error_library_load = "Easy Anti-Cheat kütüphanesi yüklenemedi";
	error_memory = "Oyunu başlatmak için hafıza yetersiz";
	error_module_load = "Anti hile modülü yüklemesi başarısız";
	error_patched = "Yamalı Windows yükleyici saptandı";
	error_process = "İşlem oluşturulamadı";
	error_process_crash = "İşlem sonlandırıldı";
	error_safe_mode = "Easy Anti-Cheat, Windows Güvenli Kipte çalıştırılamaz";
	error_socket = "Uygulamanın internete erişimini engelleyen bir şeyler var!";
	error_ssl = "CDN hizmetiyle SSL bağlantısı kurulamadı";
	error_start = "Oyunu başlatma başarısız";
	error_uncpath_forbidden = "Oyun ağ paylaşımı aracılığıyla başlatılamaz. (UNC yolu)";
	error_connection_failed = "Bağlantı başarısız: ";
	error_missing_game_id = "Oyun kimliği eksik";
	error_dns_resolve_failed = "DNS proxy çözümlemesi başarısız";
	error_dns_connection_failed = "İçerik Dağıtım Ağına bağlantı başarısız! Döngü Kodu: {0}!";
	error_http_response = "HTTP Yanıt Kodu: {0} Döngü Kodu: {1}";
	error_driver_handle = "Beklenmedik hata. (Sürücü tanıtıcısı açılamadı)";
	error_incompatible_service = "Uyumsuz bir Easy Anti-Cheat hizmeti zaten çalışıyor. Lütfen çalışan diğer tüm oyunları kapat veya yeniden başlat";
	error_incompatible_driver_version = "Uyumsuz bir Easy Anti-Cheat sürücü sürümü zaten çalışıyor. Lütfen çalışan diğer tüm oyunları kapat veya yeniden başlat";
	error_another_launcher = "Beklenmedik hata. (Zaten başka bir başlatıcı çalışıyor)";
	error_game_running = "Beklenmedik hata. (Oyun zaten çalışıyor)";
	error_patched_boot_loader = "Yamalı Windows önyükleme yöneticisi tespit edildi. (Kernel Yama Koruması devre dışı)";
	error_unknown_process = "Tanınmayan oyun istemcisi. Devam edilemiyor.";
	error_unknown_game = "Yapılandırılmamış oyun. Devam edilemiyor.";
	error_win7_required = "Windows 7 veya daha yeni bir işletim sistemi gerekmektedir.";
	success_initialized = "Easy Anti-Cheat başarıyla başlatıldı";
	success_loaded = "Easy Anti-Cheat sisteme başarıyla yüklendi";
	error_create_process = "Oyun işlemi oluşturulamadı: {0}";
	error_create_thread = "Arka plan dizisi oluşturulamadı!";
	error_disallowed_cdn_path = "Beklenmedik hata. (Hatalı İDA bağlantısı)";
	error_empty_executable_field = "Oyun ikili dosyasına giden dosya yolu sağlanmadı.";
	error_failed_path_query = "İşlem dosya yolu alınamadı";
	error_failed_to_execute = "Oyun işlemi çalıştırılamadı.";
	error_game_binary_is_directory = "Hedef çalıştırılabilir dosyası bir dizindir!";
	error_game_binary_is_dot_app = "Hedef çalıştırılabilir dosyası bir dizindir, ikili dosyayı bunun yerine .app içerisinde hedefle!";
	error_game_binary_not_found = "Oyun ikili dosyası bulunamadı: '{0}'";
	error_game_binary_not_found_wine = "Oyun ikili dosyası bulunamadı (Wine)";
	error_game_security_violation = "Oyun Güvenliği İhlali {0}";
	error_generic_ex = "Beklenmedik hata. {0}";
	error_instance_count_limit = "Maksimum eş zamanlı oyun oluşumuna ulaşıldı!";
	error_internal = "Dâhili hata!";
	error_invalid_executable_path = "Geçersiz oyun çalıştırılabilir dosyası yolu!";
	error_memory_ex = "{0} oyununu başlatmak için yetersiz bellek";
	error_missing_binary_path = "Oyunun çalıştırılabilir dosyası yolu eksik.";
	error_missing_directory_path = "Çalışma dizini dosya yolu eksik.";
	error_module_initialize = "{0} ile modül başlatma başarısız";
	error_module_loading = "Hile önleme modülü yükleme başarısız.";
	error_set_environment_variables = "Oyun işlemi için ortam değişkenleri ayarlanamadı.";
	error_unrecognized_blacklisted_driver = "N/A tespit edildi. Lütfen yüklemeyi kaldır ve tekrar dene.";
	error_unsupported_machine_arch = "Desteklenmeyen ana makine mimarisi. ({0})";
	error_working_directory_not_found = "Çalışma dizini mevcut değil.";
	error_x8664_required = "Desteklenmeyen işletim sistemi. Windows'un 64-bit (x86-64) sürümü gereklidir.";
	warn_module_download_size = "HTTP Yanıt Boyutu: {0}. Boş istemci modunda başlatılıyor.";
	warn_vista_deprecation = "Easy Anti-Cheat, uyumlu kod imzaları artık oluşturulamadığı için Windows Vista desteğini Ekim 2020'de sonlandırmalıdır. https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus sayfasına göz at";
	error_configuration = "Hile önleme yapılandırması doğrulanamadı.";
	warn_win7_update_required = "Lütfen Windows güncellemelerini gerçekleştir, Ekim 2020'ye kadar bulunması gereken SHA-2 kod imza desteği sisteminde bulunmuyor. https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update sayfasına göz at";
	error_service_update_timeout = "Easy Anti-Cheat hizmet güncellemesi zaman aşımına uğradı."
	error_launch_ex = "Başlatma Hatası: {0}";
};
setup:
{
	btn_finish = "Bitir";
	btn_install = "Hemen Yükle";
	btn_repair = "Onar";
	btn_uninstall = "Yüklemeyi Kaldır";
	epic_link = "© Epic Games, Inc";
	install_progress = "Yükleniyor…";
	install_success = "Yükleme Başarılı";
	licenses_link = "Lisanslar";
	privacy_link = "Gizlilik";
	repair_progress = "Onarılıyor…";
	title = "Easy Anti-Cheat Hizmet Kurulumu";
	uninstall_progress = "Yükleme Kaldırılıyor…";
	uninstall_success = "Kaldırma Başarılı";
};
setup_error:
{
	error_cancelled = "İşlem kullanıcı tarafından iptal edildi";
	error_encrypted = "Easy Anti-Cheat yükleme klasörü şifrelendi";
	error_intro = "Easy Anti-Cheat Kurulumu Başarısız";
	error_not_installed = "Easy Anti-Cheat kurulu değil";
	error_registry = "Kayıt defterine erişim engellendi";
	error_rights = "Yetersiz İzinler";
	error_service = "Hizmet oluşturulamıyor";
	error_service_ex = "Hizmet oluşturulamıyor {0}";
	error_system = "System32'ye erişim engellendi";
};