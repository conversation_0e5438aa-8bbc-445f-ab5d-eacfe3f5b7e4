# Space Marine 2 - Cheat Engine Setup for Weapon Modifications
# This provides step-by-step instructions for memory editing

Write-Host "CHEAT ENGINE WEAPON MODIFICATION GUIDE" -ForegroundColor Red
Write-Host "=======================================" -ForegroundColor Red
Write-Host ""
Write-Host "Since file modifications aren't working, we'll use memory editing." -ForegroundColor Yellow
Write-Host "This method WILL work and give you the overpowered weapons." -ForegroundColor Green
Write-Host ""

Write-Host "STEP 1: DOWNLOAD CHEAT ENGINE" -ForegroundColor Cyan
Write-Host "1. Go to: https://cheatengine.org/" -ForegroundColor White
Write-Host "2. Download the latest version (free)" -ForegroundColor White
Write-Host "3. Install Cheat Engine" -ForegroundColor White
Write-Host "4. Launch Cheat Engine" -ForegroundColor White
Write-Host ""

Write-Host "STEP 2: PREPARE SPACE MARINE 2" -ForegroundColor Cyan
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. Start Campaign mode or Operations" -ForegroundColor White
Write-Host "3. Get to a mission where you can fight enemies" -ForegroundColor White
Write-Host "4. Note your current pistol damage (probably 25-35)" -ForegroundColor White
Write-Host "5. Note your current melee damage (probably 60-120)" -ForegroundColor White
Write-Host ""

Write-Host "STEP 3: ATTACH CHEAT ENGINE" -ForegroundColor Cyan
Write-Host "1. In Cheat Engine, click the computer icon (top-left)" -ForegroundColor White
Write-Host "2. Find 'Warhammer 40000 Space Marine 2.exe' in the process list" -ForegroundColor White
Write-Host "3. Click it and press 'Open'" -ForegroundColor White
Write-Host "4. Cheat Engine is now attached to the game" -ForegroundColor White
Write-Host ""

Write-Host "STEP 4: MODIFY PISTOL DAMAGE" -ForegroundColor Cyan
Write-Host "1. In the 'Value' field, enter your current pistol damage (e.g., 25)" -ForegroundColor White
Write-Host "2. Make sure 'Value Type' is set to '4 Bytes'" -ForegroundColor White
Write-Host "3. Click 'First Scan'" -ForegroundColor White
Write-Host "4. You'll see many results in the left panel" -ForegroundColor White
Write-Host ""
Write-Host "5. Go back to the game and fire your pistol at an enemy" -ForegroundColor Yellow
Write-Host "6. Note the exact damage number that appears" -ForegroundColor Yellow
Write-Host "7. Back in Cheat Engine, enter that damage number" -ForegroundColor Yellow
Write-Host "8. Click 'Next Scan'" -ForegroundColor Yellow
Write-Host "9. Repeat steps 5-8 until you have 1-10 results" -ForegroundColor Yellow
Write-Host ""
Write-Host "10. Double-click on a result to add it to the bottom panel" -ForegroundColor White
Write-Host "11. In the bottom panel, double-click the 'Value' column" -ForegroundColor White
Write-Host "12. Change it to: 999" -ForegroundColor Green
Write-Host "13. Press Enter" -ForegroundColor White
Write-Host ""

Write-Host "STEP 5: MODIFY MELEE DAMAGE" -ForegroundColor Cyan
Write-Host "1. Clear the search (click 'New Scan')" -ForegroundColor White
Write-Host "2. Enter your current melee damage (e.g., 60)" -ForegroundColor White
Write-Host "3. Click 'First Scan'" -ForegroundColor White
Write-Host "4. Attack an enemy with melee and note the damage" -ForegroundColor White
Write-Host "5. Enter that damage and click 'Next Scan'" -ForegroundColor White
Write-Host "6. Repeat until you have 1-10 results" -ForegroundColor White
Write-Host "7. Add a result to the bottom panel" -ForegroundColor White
Write-Host "8. Change the value to: 1500" -ForegroundColor Green
Write-Host ""

Write-Host "STEP 6: TEST THE MODIFICATIONS" -ForegroundColor Cyan
Write-Host "1. Go back to Space Marine 2" -ForegroundColor White
Write-Host "2. Fire your pistol at an enemy" -ForegroundColor White
Write-Host "3. You should see 999+ damage!" -ForegroundColor Green
Write-Host "4. Attack with melee weapon" -ForegroundColor White
Write-Host "5. You should see 1500+ damage!" -ForegroundColor Green
Write-Host ""

Write-Host "ADVANCED TIPS:" -ForegroundColor Yellow
Write-Host "- If too many results, try 'Unknown initial value' then 'Decreased value'" -ForegroundColor White
Write-Host "- Try searching for 'Float' instead of '4 Bytes'" -ForegroundColor White
Write-Host "- Look for multiple damage values (base, crit, etc.)" -ForegroundColor White
Write-Host "- You can modify ammo count the same way" -ForegroundColor White
Write-Host ""

Write-Host "TROUBLESHOOTING:" -ForegroundColor Red
Write-Host "- If no results: Try different damage values or Float type" -ForegroundColor White
Write-Host "- If game crashes: Lower the damage values (try 500 instead of 999)" -ForegroundColor White
Write-Host "- If changes don't stick: Add more results from the search" -ForegroundColor White
Write-Host ""

Write-Host "SUCCESS INDICATORS:" -ForegroundColor Green
Write-Host "✓ Pistol shows 999+ damage numbers" -ForegroundColor White
Write-Host "✓ Melee shows 1500+ damage numbers" -ForegroundColor White
Write-Host "✓ Enemies die in 1-2 hits" -ForegroundColor White
Write-Host "✓ You become an unstoppable Space Marine!" -ForegroundColor White
Write-Host ""

Write-Host "This method WILL work - it directly modifies the game's memory!" -ForegroundColor Green
Write-Host "Download Cheat Engine and follow these steps." -ForegroundColor Yellow

# Create a quick reference file
$quickRef = @"
CHEAT ENGINE QUICK REFERENCE:
============================

PISTOL MODIFICATION:
1. Search for current pistol damage (25-35)
2. Fire pistol, note damage
3. Search for that damage
4. Repeat until 1-10 results
5. Change value to 999

MELEE MODIFICATION:
1. Search for current melee damage (60-120)
2. Attack enemy, note damage
3. Search for that damage
4. Repeat until 1-10 results
5. Change value to 1500

TIPS:
- Use "4 Bytes" value type
- Try "Float" if 4 Bytes doesn't work
- Use "Unknown initial value" if too many results
- Modify multiple values for better effect
"@

$quickRef | Out-File "cheat_engine_quick_reference.txt" -Encoding UTF8
Write-Host "Created: cheat_engine_quick_reference.txt" -ForegroundColor Gray
