__request   =   {
   __select   =   {
      __id   =   "cat"
      __select   =   {
         __id   =   "obj"
         __path   =   "objectives/*"
         __type   =   "model"
      }
      __path   =   "categories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "ObjectivesDataLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{cat}.{obj}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

