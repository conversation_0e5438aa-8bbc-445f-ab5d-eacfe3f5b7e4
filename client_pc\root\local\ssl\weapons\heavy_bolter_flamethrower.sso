// Space Marine 2 - Replace Heavy Bolter with Flamethrower
// This directly replaces the Heavy Bolter weapon with flamethrower mechanics

FirearmLibraryPve = {
    firearms = {
        heavy_bolter = {
            uid = "heavy_bolter"
            name = "Heavy Flamethrower"
            category = "heavy"
            
            // Flamethrower properties
            weaponType = "heavy_weapon"
            damageType = "fire"
            fireMode = "continuous"
            
            // High damage, short range
            damage = {
                base = 45.0
                armorPenetration = 0.3
                criticalMultiplier = 1.5
                falloffStart = 10.0
                falloffEnd = 20.0
            }
            
            // Continuous fire mechanics
            fireRate = {
                roundsPerMinute = 1200
                burstLength = -1
                spinUpTime = 0.5
                cooldownTime = 2.0
            }
            
            // Fuel system
            ammo = {
                maxAmmo = 200
                clipSize = 200
                reloadTime = 4.0
                ammoPerShot = 2
                ammoType = "fuel"
            }
            
            // Short range
            range = {
                effective = 15.0
                maximum = 20.0
                optimal = 8.0
            }
            
            // High close-range accuracy
            accuracy = {
                hipFire = 0.9
                aimDownSight = 0.95
                movementPenalty = 0.3
                recoil = {
                    vertical = 0.2
                    horizontal = 0.1
                    pattern = "minimal"
                }
            }
            
            // Heavy weapon handling
            handling = {
                weight = 8.5
                aimDownSightTime = 1.2
                movementSpeedMultiplier = 0.7
                swapTime = 2.5
            }
            
            // Flame projectile
            projectile = {
                type = "flame_stream"
                speed = 25.0
                gravity = 0.5
                lifetime = 0.8
                penetration = 0
                areaOfEffect = 3.0
                maxTargets = 8
            }
            
            // Overheating
            specialMechanics = {
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 1.5
                    coolingRate = 15.0
                    overheatPenalty = 3.0
                }
                
                continuousFire = {
                    enabled = true
                    damagePerTick = 45.0
                    tickRate = 0.05
                    fuelConsumption = 2.0
                }
            }
            
            // Status effects
            statusEffects = {
                burning = {
                    enabled = true
                    applyChance = 0.9
                    duration = 5.0
                    damagePerSecond = 10.0
                    stackable = true
                    maxStacks = 3
                }
                
                fear = {
                    enabled = true
                    applyChance = 0.3
                    duration = 2.0
                    affectedTypes = ["basic_enemy", "cultist"]
                }
            }
            
            // Visual effects
            effects = {
                muzzleFlash = "sfx_flamethrower_muzzle"
                projectileTrail = "sfx_flame_stream"
                impactEffect = "sfx_flame_impact"
                scorchMark = "scr_flamethrower"
                
                flameVisuals = {
                    color = [255, 120, 0]
                    intensity = 2.5
                    width = 1.2
                    length = 15.0
                    particleCount = 100
                }
            }
            
            // Audio
            audio = {
                fireSound = "wpn_flamethrower_fire"
                reloadSound = "wpn_flamethrower_reload"
                emptySound = "wpn_flamethrower_empty"
                spinUpSound = "wpn_flamethrower_spinup"
                cooldownSound = "wpn_flamethrower_cooldown"
                
                loopingFire = true
                fadeInTime = 0.2
                fadeOutTime = 0.5
            }
            
            // Environmental
            environmental = {
                igniteObjects = true
                clearVegetation = true
                meltIce = true
                spreadFire = true
                
                igniteChance = 0.8
                spreadRadius = 2.0
                burnDuration = 10.0
            }
            
            // UI
            ui = {
                icon = "ui_heavy_bolter_icon"  // Keep original icon for now
                description = "Heavy flamethrower that deals continuous fire damage and applies burning effects to enemies."
                unlockLevel = 1
                weaponClass = "Heavy"
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
