﻿<?xml version="1.0" encoding="utf-8"?>
<LayoutRoot>
	<RootPanel
		Orientation="Vertical">
		<LayoutPanel
			Orientation="Horizontal">
			<LayoutAnchorablePaneGroup
				Orientation="Vertical"
				DockWidth="528">
				<LayoutAnchorablePane
					Id="77c823bf-06b6-42dd-abb4-06792db5f2eb"
					DockWidth="528"
					DockHeight="1.2441613588110403*"
					DockMinWidth="200"
					DockMinHeight="50"
					FloatingWidth="1412"
					FloatingHeight="773"
					FloatingLeft="-129"
					FloatingTop="534">
					<LayoutAnchorable
						AutoHideMinWidth="100"
						AutoHideMinHeight="100"
						Title="Content Browser 1"
						IsSelected="True"
						ContentId="Content Browser"
						FloatingLeft="-129"
						FloatingTop="534"
						FloatingWidth="1412"
						FloatingHeight="773"
						LastActivationTimeStamp="07/11/2024 17:03:03" />
				</LayoutAnchorablePane>
				<LayoutAnchorablePaneGroup
					Orientation="Horizontal"
					DockHeight="0.7558386411889597*"
					FloatingWidth="460"
					FloatingHeight="918"
					FloatingLeft="-1684"
					FloatingTop="570">
					<LayoutAnchorablePane
						DockHeight="1.18109436115867*"
						FloatingWidth="460"
						FloatingHeight="918"
						FloatingLeft="-1684"
						FloatingTop="570">
						<LayoutAnchorable
							AutoHideMinWidth="100"
							AutoHideMinHeight="100"
							Title="Tool Shelf"
							IsSelected="True"
							ContentId="ToolShelfView"
							FloatingLeft="-1684"
							FloatingTop="570"
							FloatingWidth="460"
							FloatingHeight="918"
							LastActivationTimeStamp="07/11/2024 17:02:40"
							PreviousContainerId="90d908fb-871b-4348-81b9-a1bfaa5356cf"
							PreviousContainerIndex="1" />
					</LayoutAnchorablePane>
				</LayoutAnchorablePaneGroup>
			</LayoutAnchorablePaneGroup>
			<LayoutPanel
				Orientation="Horizontal"
				DockWidth="1.6276565975812647*">
				<LayoutPanel
					Orientation="Horizontal">
					<LayoutPanel
						Orientation="Vertical">
						<LayoutPanel
							Orientation="Horizontal"
							DockHeight="0.8127802690582959*">
							<LayoutPanel
								Orientation="Vertical">
								<LayoutPanel
									Orientation="Horizontal">
									<LayoutPanel
										Orientation="Vertical">
										<LayoutDocumentPaneGroup
											Orientation="Vertical">
											<LayoutDocumentPane
												Id="40b535de-5dec-446d-bfd0-e7772cad87eb"
												DockWidth="1.68720127456187*"
												DockHeight="1.18109436115867*">
												<LayoutAnchorable
													AutoHideMinWidth="100"
													AutoHideMinHeight="100"
													Title="Viewport"
													IsSelected="True"
													IsLastFocusedDocument="True"
													ContentId="EngineRenderer"
													FloatingLeft="699"
													FloatingTop="512"
													FloatingWidth="1936"
													FloatingHeight="1056"
													LastActivationTimeStamp="07/11/2024 17:04:40"
													PreviousContainerId="40b535de-5dec-446d-bfd0-e7772cad87eb"
													PreviousContainerIndex="1" />
											</LayoutDocumentPane>
										</LayoutDocumentPaneGroup>
									</LayoutPanel>
								</LayoutPanel>
							</LayoutPanel>
						</LayoutPanel>
						<LayoutAnchorablePane
							Id="721eb96b-a93d-4839-97e9-f8c587202087"
							DockHeight="217"
							DockMinWidth="200"
							DockMinHeight="50"
							FloatingWidth="200"
							FloatingHeight="50">
							<LayoutAnchorable
								AutoHideMinWidth="100"
								AutoHideMinHeight="100"
								Title="Problems"
								IsSelected="True"
								ContentId="ProblemsView"
								LastActivationTimeStamp="07/11/2024 17:04:30" />
							<LayoutAnchorable
								AutoHideMinWidth="100"
								AutoHideMinHeight="100"
								Title="Log Viewer"
								ContentId="LogViewer"
								FloatingLeft="-1263"
								FloatingTop="804"
								FloatingWidth="960"
								FloatingHeight="735"
								LastActivationTimeStamp="07/11/2024 17:04:28"
								PreviousContainerId="40b535de-5dec-446d-bfd0-e7772cad87eb"
								PreviousContainerIndex="1" />
						</LayoutAnchorablePane>
					</LayoutPanel>
				</LayoutPanel>
			</LayoutPanel>
			<LayoutAnchorablePaneGroup
				Orientation="Vertical"
				DockWidth="418"
				DockHeight="1.18109436115867*">
				<LayoutAnchorablePane
					Id="90d908fb-871b-4348-81b9-a1bfaa5356cf"
					DockWidth="418"
					DockHeight="1.18109436115867*"
					FloatingWidth="460"
					FloatingHeight="918"
					FloatingLeft="1893"
					FloatingTop="547">
					<LayoutAnchorable
						AutoHideMinWidth="100"
						AutoHideMinHeight="100"
						Title="Outliner: example_scene_template_2x2.scn"
						IsSelected="True"
						ContentId="SceneOutline"
						FloatingLeft="1893"
						FloatingTop="547"
						FloatingWidth="460"
						FloatingHeight="918"
						LastActivationTimeStamp="07/11/2024 17:02:40" />
				</LayoutAnchorablePane>
				<LayoutAnchorablePaneGroup
					Orientation="Horizontal"
					FloatingWidth="460"
					FloatingHeight="918"
					FloatingLeft="-216"
					FloatingTop="567">
					<LayoutAnchorablePane
						DockHeight="1.18109436115867*"
						FloatingWidth="460"
						FloatingHeight="918"
						FloatingLeft="-216"
						FloatingTop="567">
						<LayoutAnchorable
							AutoHideMinWidth="100"
							AutoHideMinHeight="100"
							Title="Inspector"
							IsSelected="True"
							ContentId="Inspector"
							ToolTip="&lt;empty&gt;"
							FloatingLeft="-216"
							FloatingTop="567"
							FloatingWidth="460"
							FloatingHeight="918"
							LastActivationTimeStamp="07/11/2024 17:02:36"
							PreviousContainerId="90d908fb-871b-4348-81b9-a1bfaa5356cf"
							PreviousContainerIndex="1" />
					</LayoutAnchorablePane>
				</LayoutAnchorablePaneGroup>
			</LayoutAnchorablePaneGroup>
		</LayoutPanel>
	</RootPanel>
	<TopSide />
	<RightSide />
	<LeftSide />
	<BottomSide />
	<FloatingWindows>
		<LayoutAnchorableFloatingWindow>
			<LayoutAnchorablePaneGroup
				Orientation="Horizontal"
				FloatingWidth="645"
				FloatingHeight="352"
				FloatingLeft="2577"
				FloatingTop="364">
				<LayoutAnchorablePane
					Id="77141eed-121f-4b35-a352-64e49d7bd542"
					DockMinWidth="200"
					DockMinHeight="50"
					FloatingWidth="645"
					FloatingHeight="352"
					FloatingLeft="2577"
					FloatingTop="364" />
			</LayoutAnchorablePaneGroup>
		</LayoutAnchorableFloatingWindow>
	</FloatingWindows>
	<Hidden>
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Find Results"
			IsSelected="True"
			ContentId="Find Results"
			ToolTip="Find Results"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="721eb96b-a93d-4839-97e9-f8c587202087"
			PreviousContainerIndex="1" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Find in project"
			IsSelected="True"
			ContentId="FindAndReplaceView"
			ToolTip="Find in project"
			FloatingLeft="2577.33333333333"
			FloatingTop="364"
			FloatingWidth="645.333333333333"
			FloatingHeight="352"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="77141eed-121f-4b35-a352-64e49d7bd542"
			PreviousContainerIndex="0" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Viewport 2"
			IsSelected="True"
			ContentId="XgineViewport2"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="40b535de-5dec-446d-bfd0-e7772cad87eb"
			PreviousContainerIndex="4" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Viewport 3"
			IsSelected="True"
			ContentId="XgineViewport3"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="40b535de-5dec-446d-bfd0-e7772cad87eb"
			PreviousContainerIndex="4" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Viewport 4"
			IsSelected="True"
			ContentId="XgineViewport4"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="40b535de-5dec-446d-bfd0-e7772cad87eb"
			PreviousContainerIndex="4" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Output"
			IsSelected="True"
			ContentId="Output"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="721eb96b-a93d-4839-97e9-f8c587202087"
			PreviousContainerIndex="0" />
		<LayoutAnchorable
			AutoHideMinWidth="100"
			AutoHideMinHeight="100"
			Title="Content Browser 2"
			IsSelected="True"
			ContentId="Content Browser 2"
			LastActivationTimeStamp="07/11/2024 17:04:40"
			PreviousContainerId="77c823bf-06b6-42dd-abb4-06792db5f2eb"
			PreviousContainerIndex="1" />
	</Hidden>
</LayoutRoot>