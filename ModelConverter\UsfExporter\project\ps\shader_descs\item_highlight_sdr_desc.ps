item_highlight {
   __caption = "item_highlight"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   highlightColor {
      __type = "color"
   }
   
   occludedColor {
      __type = "color"
   }
   
   flashing_speed {
      __type = float
   }
   
   max_vis_dist {
      __type = float
   }
   
   begin_fade_dist {
      __type = float
   }
   
   useZSample {
      __type = "bool"
   }
   
   infrared {
      __type = "bool"
   }
   
   fadeZone {
      __type = float
   }
   
   onOutlineThroughObj {
      __type = "bool"
   }
   
   onStroke {
      __type = "bool"
   }
   
   onBlur {
      __type = "bool"
   }
   
   outlineType {
      __type = "enum"
      __list = ["non_maskable", "maskable"]
   }
   
   maskPass {
      __type = "bool"
   }
   
   outline {
      __type = "bool"
   }
   
   editor_mode {
      __type = "bool"
   }
   
   wireframe {
      __type = "bool"
   }
   
}