__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "location"
         __path   =   "locations/*"
         __type   =   "model"
      }
      __showParentModelCaption   =   True
      __models   =   [
         {
            __path   =   "currentCategories/*"
         },
         {
            __path   =   "introCategories/*"
         }
      ]
      __type   =   "modelMulti"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "HudLocationDataLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{location}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

