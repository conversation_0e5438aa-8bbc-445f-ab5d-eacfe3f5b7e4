__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __format   =   "{value}"
            __showValue   =   True
            __path   =   "versionUid"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __format   =   "{value}"
         __path   =   "/*"
         __type   =   "model"
      }
      __format   =   "{value}"
      __path   =   "versions/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "MeleeVersionsLibrary"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

