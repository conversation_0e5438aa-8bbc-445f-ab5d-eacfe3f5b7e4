# Space Marine 2 - Direct PAK File Injection
# This injects our weapon mods directly into the main PAK files

Write-Host "Direct PAK File Injection Method" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red

$mainPak = "client_pc\root\paks\client\default\default.pak"
$resourcesPak = "client_pc\root\paks\client\resources.pak"

# Create backup if not exists
if (-not (Test-Path "$mainPak.original")) {
    Copy-Item $mainPak "$mainPak.original" -Force
    Write-Host "Created original backup: $mainPak.original" -ForegroundColor Green
}

Write-Host "Attempting to extract and modify PAK files..." -ForegroundColor Yellow

# Try to extract the PAK file (it's a ZIP)
$extractDir = "pak_extract_temp"
if (Test-Path $extractDir) {
    Remove-Item $extractDir -Recurse -Force
}

try {
    # Try to extract as ZIP
    Expand-Archive -Path $mainPak -DestinationPath $extractDir -Force
    Write-Host "Successfully extracted PAK file" -ForegroundColor Green
    
    # Look for weapon files
    $weaponFiles = Get-ChildItem -Path $extractDir -Recurse -Filter "*weapon*" -ErrorAction SilentlyContinue
    $sslFiles = Get-ChildItem -Path $extractDir -Recurse -Filter "*.sso" -ErrorAction SilentlyContinue
    
    Write-Host "Found weapon-related files:" -ForegroundColor Cyan
    $weaponFiles | ForEach-Object { Write-Host "  $($_.FullName)" -ForegroundColor Gray }
    
    Write-Host "Found SSL files:" -ForegroundColor Cyan
    $sslFiles | ForEach-Object { Write-Host "  $($_.FullName)" -ForegroundColor Gray }
    
    # If we found SSL files, replace them
    if ($sslFiles.Count -gt 0) {
        Write-Host "Injecting our weapon modifications..." -ForegroundColor Yellow
        
        # Copy our SSL files to the extracted directory
        $ourSSLDir = "$extractDir\ssl\weapons"
        if (-not (Test-Path $ourSSLDir)) {
            New-Item -ItemType Directory -Path $ourSSLDir -Force | Out-Null
        }
        
        Copy-Item "client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso" $ourSSLDir -Force
        Copy-Item "client_pc\root\local\ssl\weapons\lightning_sword_complete.sso" $ourSSLDir -Force
        
        Write-Host "Injected our SSL files into PAK structure" -ForegroundColor Green
        
        # Repack the PAK file
        Write-Host "Repacking modified PAK file..." -ForegroundColor Yellow
        Remove-Item $mainPak -Force
        Compress-Archive -Path "$extractDir\*" -DestinationPath "$mainPak.zip" -Force
        Rename-Item "$mainPak.zip" $mainPak -Force
        
        Write-Host "Successfully repacked PAK file with modifications!" -ForegroundColor Green
    } else {
        Write-Host "No SSL files found in PAK - trying different approach..." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "PAK extraction failed: $_" -ForegroundColor Red
    Write-Host "PAK file may be encrypted or compressed differently" -ForegroundColor Yellow
}

# Clean up
if (Test-Path $extractDir) {
    Remove-Item $extractDir -Recurse -Force
}

Write-Host ""
Write-Host "METHOD 2: File System Override" -ForegroundColor Cyan
Write-Host "Trying alternative file locations..." -ForegroundColor Yellow

# Try copying to different locations where the game might look
$alternativeLocations = @(
    "client_pc\root\ssl\weapons",
    "client_pc\ssl\weapons", 
    "ssl\weapons",
    "weapons",
    "client_pc\root\data\weapons",
    "data\weapons"
)

foreach ($location in $alternativeLocations) {
    if (-not (Test-Path $location)) {
        New-Item -ItemType Directory -Path $location -Force | Out-Null
    }
    
    Copy-Item "client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso" $location -Force -ErrorAction SilentlyContinue
    Copy-Item "client_pc\root\local\ssl\weapons\lightning_sword_complete.sso" $location -Force -ErrorAction SilentlyContinue
    
    Write-Host "Copied SSL files to: $location" -ForegroundColor Gray
}

Write-Host ""
Write-Host "METHOD 3: Configuration Override" -ForegroundColor Cyan

# Create game configuration files that might force loading our mods
$gameConfig = @"
// Space Marine 2 Configuration Override
ssl_mod_path = "local/ssl"
enable_local_mods = true
disable_file_validation = true
allow_weapon_modifications = true
"@

$gameConfig | Out-File "client_pc\root\game.cfg" -Encoding UTF8
$gameConfig | Out-File "game.cfg" -Encoding UTF8

Write-Host "Created configuration override files" -ForegroundColor Green

Write-Host ""
Write-Host "TESTING INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "1. Close Space Marine 2 completely" -ForegroundColor White
Write-Host "2. Launch Space Marine 2 again" -ForegroundColor White
Write-Host "3. Test weapons in Campaign mode" -ForegroundColor White
Write-Host "4. If still not working, try METHOD 4 below" -ForegroundColor White

Write-Host ""
Write-Host "METHOD 4: Memory Editing (Cheat Engine)" -ForegroundColor Red
Write-Host "If file modifications don't work at all:" -ForegroundColor Yellow
Write-Host "1. Download Cheat Engine" -ForegroundColor White
Write-Host "2. Attach to Space Marine 2 process" -ForegroundColor White
Write-Host "3. Search for pistol damage (25-35)" -ForegroundColor White
Write-Host "4. Change to 999" -ForegroundColor White
Write-Host "5. Search for melee damage (60-120)" -ForegroundColor White
Write-Host "6. Change to 1500" -ForegroundColor White

Write-Host ""
Write-Host "Ready to test! Try launching the game now." -ForegroundColor Green
