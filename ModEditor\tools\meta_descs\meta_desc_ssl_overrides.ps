// For override project's ssl classes descs

DbgWindow {
   __members {
		sslClass {
			__type = "string"
			__viewType = "none"
		}
		"#ssl" {
			__extraData = {
				__generateSslSubClass = true
			}
			__viewType {
				__type = "button"
				__value = "Open SSL"
            __hidden = false
			}
		}
	}
	__extraData = {
		__sslVarsSection = "."
		__sslClass = "./sslClass"
	}
}

DebugServerCommand = {
	 __members {
		 "#ssl" {
		 __type = "ssl"
		 __extraData = {
			__generateSslSubClass = true
		}
		__viewType {
			__type = "button"
			__value = "Open SSL"
			__hidden = false
			}
		}
		sslClass {
		 __type = "string"
		 __removeFromEngineBin   =   True
		 __viewType {
			__hidden = true
		 }
		}
	 }
	 __extraData = {
		__sslVarsSection = "."
		__sslClass = "./sslClass"
	}
}