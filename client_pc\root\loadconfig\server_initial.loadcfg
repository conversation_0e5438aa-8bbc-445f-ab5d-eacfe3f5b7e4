steps:
  - RES3_INITIAL_SSL
  - RES3_INITIAL_SSL_DESC
  - RES3_INITIAL_TD
  - RES3_INITIAL_ADAPTER_LOADING
  - RES3_INITIAL_GFX
  - RES3_INITIAL_MORPHEME
  - RES3_INITIAL_FONTS
  - RES3_INITIAL_ALL

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_INITIAL_SSL

  - name: adapter_loading
    on:
      - step: RES3_INITIAL_ADAPTER_LOADING

rules:
  #
  # ssl
  #
  - name: process intital ssl
    res_type: res_desc_ssl
    step: RES3_INITIAL_SSL

  - name: process intital spdb
    res_type: res_desc_spdb
    step: RES3_INITIAL_SSL
    
  #
  # ssl descs
  #
  - name: process intital ssolib
    res_type: res_desc_ssolib
    step: RES3_INITIAL_SSL_DESC

  - name: process intital sso
    res_type: res_desc_sso
    step: RES3_INITIAL_SSL_DESC

  - name: process intital cls
    res_type: res_desc_cls
    step: RES3_INITIAL_SSL_DESC

  - name: process intital prop
    res_type: res_desc_prop
    step: RES3_INITIAL_SSL_DESC

  - name: process intital prefab
    res_type: res_prefab_desc
    step: RES3_INITIAL_SSL_DESC

  - name: process initial prefab asset
    res_type: res_desc_prefab_asset
    step: RES3_INITIAL_SSL_DESC

  #
  # basic descs
  #
  - name: process intital basic descs
    res_type: res_basic_ssl_desc
    step: RES3_INITIAL_SSL_DESC

  #
  # td & sd
  #

  - name: process material templates
    res_type: res_desc_material_templates
    step: RES3_INITIAL_TD

  - name: process default tds
    res_type: res_desc_td_defaults
    step: RES3_INITIAL_TD

  - name: process tds
    res_type: res_desc_td
    step: RES3_INITIAL_TD

  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: RES3_INITIAL_TD

  #
  # gfx
  #
  - name: process intital gfx
    res_type: res_desc_gfx
    step: RES3_INITIAL_GFX

  #
  # morpheme
  #
  - name: process initial morpheme
    res_type: res_desc_morpheme
    step: RES3_INITIAL_MORPHEME

  - name: process animations
    res_type: res_desc_animation
    step: RES3_INITIAL_MORPHEME

  #
  # fonts
  #
  - name: process imgui fonts
    res_type: res_desc_imgui_fonts
    step: RES3_INITIAL_FONTS

  #
  # all
  #
  - name: exclude all other
    exclude: true
