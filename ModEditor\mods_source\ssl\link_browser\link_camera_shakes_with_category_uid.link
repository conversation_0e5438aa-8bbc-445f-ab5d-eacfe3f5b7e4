__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
	     __id   =   "shake"
         __onlyChanged   =   False
         __groupByParent   =   2
         __where   =   {
            __parentType   =   "^ProceduralShakeCamera$"
         }
         __type   =   "modelRecursive"
      }
      __path   =   "catalogShakes/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "CameraShakesProceduralLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{shake}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

