# Space Marine 2 - Verify Hex Modifications
# This script checks if our damage modifications actually took effect

Write-Host "Verifying Hex Modifications..." -ForegroundColor Green

$gameFile = "client_pc\root\paks\client\default\default.pak"
$backupFile = "client_pc\root\paks\client\default\default.pak.backup"

if (-not (Test-Path $gameFile)) {
    Write-Error "Game file not found!"
    exit 1
}

if (-not (Test-Path $backupFile)) {
    Write-Error "Backup file not found!"
    exit 1
}

Write-Host "Reading game file..." -ForegroundColor Yellow
$gameBytes = [System.IO.File]::ReadAllBytes($gameFile)
$backupBytes = [System.IO.File]::ReadAllBytes($backupFile)

Write-Host "Game file size: $($gameBytes.Length) bytes" -ForegroundColor Cyan
Write-Host "Backup file size: $($backupBytes.Length) bytes" -ForegroundColor Cyan

if ($gameBytes.Length -ne $backupBytes.Length) {
    Write-Error "File sizes don't match! Modification may have corrupted the file."
    exit 1
}

# Check for our specific modifications
$damageValue999 = [BitConverter]::GetBytes([int32]999)  # E7 03 00 00
$damageValue1500 = [BitConverter]::GetBytes([int32]1500)  # DC 05 00 00

$modifications999 = 0
$modifications1500 = 0

Write-Host "Searching for damage value 999 (E7 03 00 00)..." -ForegroundColor Yellow
for ($i = 0; $i -le ($gameBytes.Length - 4); $i++) {
    if ($gameBytes[$i] -eq $damageValue999[0] -and
        $gameBytes[$i+1] -eq $damageValue999[1] -and
        $gameBytes[$i+2] -eq $damageValue999[2] -and
        $gameBytes[$i+3] -eq $damageValue999[3]) {
        $modifications999++
        if ($modifications999 -le 5) {
            Write-Host "  Found 999 at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
        }
    }
}

Write-Host "Searching for damage value 1500 (DC 05 00 00)..." -ForegroundColor Yellow
for ($i = 0; $i -le ($gameBytes.Length - 4); $i++) {
    if ($gameBytes[$i] -eq $damageValue1500[0] -and
        $gameBytes[$i+1] -eq $damageValue1500[1] -and
        $gameBytes[$i+2] -eq $damageValue1500[2] -and
        $gameBytes[$i+3] -eq $damageValue1500[3]) {
        $modifications1500++
        if ($modifications1500 -le 5) {
            Write-Host "  Found 1500 at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "MODIFICATION RESULTS:" -ForegroundColor Cyan
Write-Host "Found $modifications999 instances of damage value 999" -ForegroundColor White
Write-Host "Found $modifications1500 instances of damage value 1500" -ForegroundColor White

if ($modifications999 -gt 0 -or $modifications1500 -gt 0) {
    Write-Host ""
    Write-Host "SUCCESS: Hex modifications are present!" -ForegroundColor Green
    Write-Host "The damage values have been modified in the game file." -ForegroundColor Green
    Write-Host ""
    Write-Host "If you're not seeing effects in-game:" -ForegroundColor Yellow
    Write-Host "1. Game is running in online mode (server overrides)" -ForegroundColor White
    Write-Host "2. Anti-cheat is blocking the modifications" -ForegroundColor White
    Write-Host "3. Need to run in offline mode" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "PROBLEM: No modifications found!" -ForegroundColor Red
    Write-Host "The hex editing may not have worked properly." -ForegroundColor Red
    Write-Host "Try running the hex modification scripts again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Set Steam to offline mode" -ForegroundColor White
Write-Host "2. Add launch parameters: -offline -dev" -ForegroundColor White
Write-Host "3. Test in Campaign mode (offline)" -ForegroundColor White
Write-Host "4. If still not working, try Cheat Engine" -ForegroundColor White
