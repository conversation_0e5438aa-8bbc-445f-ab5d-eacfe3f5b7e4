#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	# Use the embedded font.
};
bugreport:
{
	btn_continue = "Выйти в Интернет для поиска решения и закрыть игру.";
	btn_exit = "Закрыть игру.";
	btn_hidedetails = "Скрыть детали";
	btn_showdetails = "Показать детали";
	chk_sendreport = "Отправить сообщение об ошибке";
	error_code = "Код ошибки:";
	lbl_body1 = "К сожалению, с запуском игры возникла проблема";
	lbl_body2 = "Помогите нам, сообщив о ней.";
	lbl_body3 = "Easy Anti-Cheat может поискать решение проблемы в Интернете и постараться помочь.";
	lbl_header = "Не удалось запустить игру";
	title = "Ошибка запуска";
};
game_error:
{
	error_catalogue_corrupted = "Каталог хэшей Easy Anti-Cheat повреждён";
	error_catalogue_not_found = "Каталог EAC не найден";
	error_certificate_revoked = "Сертификат каталога EAC аннулирован";
	error_corrupted_memory = "Память повреждена";
	error_corrupted_network = "Нарушение обмена пакетами";
	error_file_forbidden = "Неизвестный игровой файл";
	error_file_not_found = "Нет нужного файла";
	error_file_version = "Неизвестная версия файла";
	error_module_forbidden = "Запрещенный модуль";
	error_system_configuration = "Недопустимая конфигурация системы";
	error_system_version = "Подозрительный системный файл";
	error_tool_forbidden = "Запрещенный инструмент";
	error_violation = "Внутренняя ошибка программы";
	error_virtual = "Запрещён запуск на виртуальной машине.";
	peer_client_banned = "Участник заблокирован системой защиты.";
	peer_heartbeat_rejected = "Участник отвергнут системой защиты.";
	peer_validated = "Проверка участников системой защиты пройдена.";
	peer_validation_failed = "Проверка участников системой защиты не пройдена.";
	executable_not_hashed = "Не найден хэш исполняемого файла игры в каталоге хэшей.";
};
launcher:
{
	btn_cancel = "Отмена";
	btn_exit = "Выход";
	error_cancel = "Запуск отменен";
	error_filenotfound = "Файл не найден";
	error_init = "Ошибка инициализации";
	error_install = "Ошибка установки";
	error_launch = "Ошибка запуска";
	error_nolib = "Не удалось загрузить библиотеку Easy Anti-Cheat";
	loading = "ЗАГРУЗКА";
	wait = "Подождите";
	initializing = "ИНИЦИАЛИЗАЦИЯ";
	success_waiting_for_game = "ОЖИДАНИЕ ИГРЫ";
	success_closing = "Успешно выполнено";
	network_error = "Сетевая ошибка";
	error_no_settings_file = "Файл {0} не найден";
	error_invalid_settings_format = "У файла {0} неподходящий формат JSON";
	error_missing_required_field = "В файле {0} нет требуемого поля ({1})";
	error_invalid_eos_identifier = "{0} содержит неверный идентификатор EOS: ({1})";
	download_progress = "Прогресс загрузки: {0}";
};
launcher_error:
{
	error_already_running = "Уже запущено приложение, использующее Easy Anti-Cheat! {0}";
	error_application = "В клиенте игры произошла ошибка приложения. Код ошибки: {0}";
	error_bad_exe_format = "Требуется 64-разрядная ОС";
	error_bitset_32 = "Используйте версию игры 32-bit";
	error_bitset_64 = "Используйте версию игры 64-bit";
	error_cancelled = "Действие отменено пользователем";
	error_certificate_validation = "Ошибка при проверке подлинности сертификата подписи программы для Easy Anti-Cheat";
	error_connection = "Не удалось подключиться к сети доставки контента!";
	error_debugger = "Обнаружен отладчик. Закройте его и попробуйте еще раз";
	error_disk_space = "Не хватает места на диске.";
	error_dns = "Не удалось преобразовать DNS-адрес сети доставки контента!";
	error_dotlocal = "Обнаружено перенаправление DLL через DotLocal";
	error_dotlocal_instructions = "Удалите следующий файл";
	error_file_not_found = "Файл не найден:";
	error_forbidden_tool = "Закройте {0} перед запуском игры";
	error_forbidden_driver = "Выгрузите {0} перед запуском игры.";
	error_generic = "Непредвиденная ошибка.";
	error_kernel_debug = "Easy Anti-Cheat не может быть запущена при включенной отладки ядра";
	error_kernel_dse = "Easy Anti-Cheat не может быть запущена при отключенной обязательной проверке подписи драйверов";
	error_kernel_modified = "Обнаружена запрещенная модификация ядра Windows";
	error_library_load = "Не удалось загрузить библиотеку Easy Anti-Cheat";
	error_memory = "Не хватает памяти для запуска игры";
	error_module_load = "Не удалось загрузить модуль борьбы с программами для нечестной игры";
	error_patched = "Обнаружен измененный загрузчик для Windows";
	error_process = "Не удается создать процесс";
	error_process_crash = "Процесс внезапно прервался";
	error_safe_mode = "Easy Anti-Cheat не получается запустить в Безопасном режиме Windows";
	error_socket = "Что-то не дает приложению выйти в Интернет!";
	error_ssl = "Не удалось создать SSL-подключение к службе сети доставки контента!";
	error_start = "Не удалось запустить игру";
	error_uncpath_forbidden = "Не удаётся запустить игру через общий сетевой ресурс. (UNC-путь).";
	error_connection_failed = "Ошибка подключения: ";
	error_missing_game_id = "Отсутствует идентификатор игры";
	error_dns_resolve_failed = "Не удалось преобразовать DNS для прокси-сервера";
	error_dns_connection_failed = "Не удалось подключиться к сети распространения контента! Curl Code: {0}!";
	error_http_response = "HTTP-код ответа: {0} Curl Code: {1}";
	error_driver_handle = "Непредвиденная ошибка. (Не удалось открыть дескриптор драйвера)";
	error_incompatible_service = "Несовместимая служба Easy Anti-Cheat уже запущена. Пожалуйста, выйдите из других запущенных игр или перезагрузите компьютер";
	error_incompatible_driver_version = "Несовместимая версия драйвера Easy Anti-Cheat уже запущена. Пожалуйста, выйдите из других запущенных игр или перезагрузите компьютер";
	error_another_launcher = "Непредвиденная ошибка. (Программа запуска уже работает)";
	error_game_running = "Непредвиденная ошибка. (Игра уже запущена)";
	error_patched_boot_loader = "Обнаружен исправленный загрузчик Windows. (Защита от исправлений ядра отключена)";
	error_unknown_process = "Клиент игры не распознан. Продолжение невозможно.";
	error_unknown_game = "Ненастроенная игра. Не удаётся продолжить.";
	error_win7_required = "Требуется Windows 7 или более новая версия.";
	success_initialized = "Служба Easy Anti-Cheat запущена";
	success_loaded = "Служба Easy Anti-Cheat загружена в игре";
	error_create_process = "Не удалось создать игровой процесс: {0}";
	error_create_thread = "Не удалось создать фоновый поток!";
	error_disallowed_cdn_path = "Непредвиденная ошибка. (Некорректная ссылка CDN)";
	error_empty_executable_field = "Путь к бинарному файлу игры не указан.";
	error_failed_path_query = "Не удалось получить путь к процессу";
	error_failed_to_execute = "Не удалось последовать игровому процессу.";
	error_game_binary_is_directory = "Целевой исполняемый файл — это каталог!";
	error_game_binary_is_dot_app = "Целевой исполняемый файл — это каталог, укажите двоичный файл .app!";
	error_game_binary_not_found = "Не обнаружен двоичный файл игры: '{0}'";
	error_game_binary_not_found_wine = "Не удалось найти двоичный файл игры (Wine)";
	error_game_security_violation = "Нарушение правил безопасности игры {0}";
	error_generic_ex = "Непредвиденная ошибка. {0}";
	error_instance_count_limit = "Запущено максимальное количество игр одновременно!";
	error_internal = "Внутренняя ошибка!";
	error_invalid_executable_path = "Неверный путь к исполняемому файлу игры!";
	error_memory_ex = "Недостаточно памяти для запуска игры {0}";
	error_missing_binary_path = "Отсутствует путь к исполняемому файлу игры.";
	error_missing_directory_path = "Отсутствует путь к рабочему каталогу.";
	error_module_initialize = "Ошибка инициализации модуля с {0}";
	error_module_loading = "Не удалось загрузить модуль системы защиты от мошенничества.";
	error_set_environment_variables = "Не удалось установить переменные среды для игрового процесса.";
	error_unrecognized_blacklisted_driver = "Обнаружен N/A. Пожалуйста, выгрузите и попробуйте ещё раз.";
	error_unsupported_machine_arch = "Неподдерживаемая архитектура хост-машины. ({0})";
	error_working_directory_not_found = "Рабочий каталог не существует.";
	error_x8664_required = "ОС не поддерживается. Требуется 64-битная (x86-64) версия Windows.";
	warn_module_download_size = "Размер ответа HTTP: {0}. Запуск в режиме нулевого клиента.";
	warn_vista_deprecation = "Easy Anti-Cheat должен прекратить поддержку Windows Vista в октябре 2020 года, поскольку совместимые подписи кода больше не могут быть созданы. Подробнее на https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Не удалось проверить конфигурацию защиты от мошенничества.";
	warn_win7_update_required = "Запустите обновления Windows, поскольку в вашей системе отсутствует критически важная поддержка подписи кода SHA-2, необходимая к октябрю 2020 года. Читайте подробнее по ссылке: https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Истекло время ожидания при обновлении службы Easy Anti-Cheat."
	error_launch_ex = "Ошибка запуска: {0}";
};
setup:
{
	btn_finish = "Финиш";
	btn_install = "Установить";
	btn_repair = "Помощь в восстановлении";
	btn_uninstall = "Удалить";
	epic_link = "© Epic Games, Inc";
	install_progress = "Установка...";
	install_success = "Установка выполнена";
	licenses_link = "Лицензии";
	privacy_link = "Конфиденциальность";
	repair_progress = "Восстановление...";
	title = "Установка сервиса Easy Anti-Cheat";
	uninstall_progress = "Удаление...";
	uninstall_success = "Удаление выполнено";
};
setup_error:
{
	error_cancelled = "Действие отменено пользователем";
	error_encrypted = "Папка, куда устанавливалась программа по предотвращению использования читов, зашифрована";
	error_intro = "Настройка Easy Anti-Cheat не удалась";
	error_not_installed = "Служба Easy Anti-Cheat не установлена.";
	error_registry = "Ошибка при копировании исполняемого файла";
	error_rights = "Не хватает привилегий";
	error_service = "Не удается создать службу";
	error_service_ex = "Не удается создать службу {0}";
	error_system = "Нет доступа к System32";
};