__request   =   {
   __select   =   {
      __id   =   "weapon"
      __select   =   {
         __id   =   "skin"
         __path   =   "skins/*"
         __type   =   "model"
      }
      __path   =   "meleeActors/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "MeleeSkinsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{weapon} : {skin}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

