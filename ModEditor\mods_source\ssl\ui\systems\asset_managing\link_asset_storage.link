__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "asset"
         __path   =   "assets/*"
         __type   =   "model"
      }
      __path   =   "assetCategories/*"
      __type   =   "model"
   }
   __groupByParents   =   False
   __where   =   {
      __name   =   "^ui.+assetStorage$"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{asset}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"


