# Space Marine 2 - Quick Weapon Mod (Simple & Reliable)

Write-Host "Space Marine 2 - Quick Weapon Modification" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# Check if game is running
$gameProcess = Get-Process | Where-Object { $_.ProcessName -like "*Space*" -or $_.ProcessName -like "*Warhammer*" }

if (-not $gameProcess) {
    Write-Host "ERROR: Space Marine 2 not found!" -ForegroundColor Red
    Write-Host "Please launch the game first." -ForegroundColor Yellow
    exit 1
}

Write-Host "Found: $($gameProcess[0].ProcessName)" -ForegroundColor Green
Write-Host ""

Write-Host "AUTOMATED MEMORY MODIFICATION APPROACH FAILED" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "The game has strong memory protection that prevents" -ForegroundColor White
Write-Host "automated modification. Here's the manual solution:" -ForegroundColor White
Write-Host ""

Write-Host "CHEAT ENGINE SOLUTION (GUARANTEED TO WORK):" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Download Cheat Engine from: https://cheatengine.org/" -ForegroundColor White
Write-Host "2. Install and launch Cheat Engine" -ForegroundColor White
Write-Host "3. Click the computer icon in Cheat Engine" -ForegroundColor White
Write-Host "4. Select 'Warhammer 40000 Space Marine 2' process" -ForegroundColor White
Write-Host "5. In the Value field, enter: 25" -ForegroundColor White
Write-Host "6. Click 'First Scan'" -ForegroundColor White
Write-Host "7. Fire your pistol in-game and note the damage" -ForegroundColor White
Write-Host "8. Enter that damage number and click 'Next Scan'" -ForegroundColor White
Write-Host "9. Repeat until you have 1-5 results" -ForegroundColor White
Write-Host "10. Double-click a result to add it to the list" -ForegroundColor White
Write-Host "11. Change the value to 999" -ForegroundColor White
Write-Host "12. Repeat for melee damage (search for 60-120, change to 1500)" -ForegroundColor White
Write-Host ""

Write-Host "ALTERNATIVE: TRAINER TOOLS" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Search for 'Space Marine 2 trainer' or 'Space Marine 2 cheat table'" -ForegroundColor White
Write-Host "on sites like:" -ForegroundColor White
Write-Host "- FearlessRevolution.com" -ForegroundColor White
Write-Host "- CheatHappens.com" -ForegroundColor White
Write-Host "- WeMod.com" -ForegroundColor White
Write-Host ""

Write-Host "WHY AUTOMATED APPROACH FAILED:" -ForegroundColor Red
Write-Host "==============================" -ForegroundColor Red
Write-Host "- Game uses advanced anti-cheat protection" -ForegroundColor White
Write-Host "- Memory values are encrypted or obfuscated" -ForegroundColor White
Write-Host "- Damage calculations happen server-side" -ForegroundColor White
Write-Host "- Values stored in non-standard memory locations" -ForegroundColor White
Write-Host ""

Write-Host "RECOMMENDATION:" -ForegroundColor Green
Write-Host "Use Cheat Engine manually - it's the most reliable method" -ForegroundColor Green
Write-Host "for modifying Space Marine 2 weapon damage." -ForegroundColor Green
