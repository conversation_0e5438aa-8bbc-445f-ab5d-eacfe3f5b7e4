// For override common's ssl classes descs

Iactor {
   __viewType {
      __hide_hierarchy_in_types = true
   }
}

PropBase {
   __viewType {
      __hide_hierarchy_in_types = true
   }
}

DiagramType {
   __type = "section"
   __viewType {
      __hidden = true
   }
}

ViewLinkData {
   __type = "DiagramType"
   __viewType = "link"
   __members {
      joints {
         __type = "array"
         __elementType = {
            __type = "vec2"
         }
         __defaultValue = [
            [
               0,0
            ]
            [
               0,0
            ]
         ]
      }
      labelX {
         __type = "float"
         __defaultValue = 0.0
      }
      labelY {
         __type = "float"
         __defaultValue = 0.0
      }
      connectorIndex {
         __type = "int"
         __defaultValue = -1
      }
   }   
}

ViewShapeData {
   __type = "DiagramType"
   __members = {
      x {
         __type = "float"
         __defaultValue = 0.0
      }
      y {
         __type = "float"
         __defaultValue = 0.0
      }
      width {
         __type = "float"
         __defaultValue = 100
      }
      height {
         __type = "float"
         __defaultValue = 80
      }
      colors {
         __type = "array"
		 __fixedSize = 2
         __elementType = {
            __type = "string"
         }
      }
   }
   __viewType {
      __type = "shape"
   }
}

ViewStateData {
   __type = "ViewShapeData"
}

ViewTreeShapeData {
   __type = "ViewShapeData"
   __viewType {
      __type = "treeShape"
   }
   __members {
      links {
         __type = "array"
         __elementType = {
            __type = "ViewLinkData"
         }
      }
   }
}

NodeGroup {
   __type = "section"
   __members {
      x {
         __type = "float"
      }
      y {
         __type = "float"
      }
      width {
         __type = "float"
      }
      height {
         __type = "float"
      }
      comment {
         __type = "string"
      }
      isCollapsed {
         __type = "bool"
      }
      headerColor {
         __type = "string"
      }
      bodyColor {
         __type = "string"
      }
   }
   __extraData = {
      "pd:WidthPath" = "width"
      "pd:HeightPath" = "height"
      "pd:CustomCaptionPath" = "comment"
      "pd:XPath" = "x"
      "pd:YPath" = "y"
   }
   __diagram = "group"
}

SslLink {
   __members {
     toState {
      __viewType {
         __hidden = true
      }
     }
   }

   __diagram {
      __toProperty = "toState"
      __destinationSet = "states"
   }
}

sslDiagram {
   __type = "ssl"
}

SslState {
   __members {
      isInitial {
         __viewType {
            __hidden = true
         }
      }
      links {
         __viewType {
            __hidden = true
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
            __generateSslTemplate = <<<@if OWNER_TYPE override GetFsm () : @OWNER_TYPE
   return (@OWNER_TYPE)parent.GetFsm()
end>>>
         }
         __viewType {
            __hiddenIn = "Diagram"
            __type = "button"
            __value = "Open SSL"
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

SslFsm {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
      groups   =   {
         __internalTypes   =   {
            NodeGroup   =   {
            }
         }
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __removeFromEngineBin   =   True
      }
      states {
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
   }
}

DecisionTree {
   __viewType {
      __type = "diagram"
      __value = "Open editor"
   }
   __members {
      actions   =   {
         __viewType {
            __hidden = true
         }
      }
      desc   =   {
         __viewType {
            __hidden = true
         }
      }
      groups   =   {
         __internalTypes   =   {
            NodeGroup   =   {
            }
         }
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __removeFromEngineBin   =   True
      }
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
   }
}

DecisionAction {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __hiddenIn = "Diagram"
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

DecisionNodeCondition {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
            __generateSslTemplate = <<<@if OWNER_TYPE override GetTree () : @OWNER_TYPE
   return (@OWNER_TYPE)parent.GetTree()
end>>>
         }
         __viewType {
            __hiddenIn = "Diagram"
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
      childs {
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

UiContainer {
   __members {
      "#ssl" {
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __hidden = false
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

BehaviourTree {
   __viewType {
      __type = "diagram"
      __value = "Open editor"
   }
   __members {
      rootNode   =   {
         __viewType {
            __hidden = true
         }
      }
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
	  groups {
         __internalTypes   =   {
            NodeGroup   =   {
            }
         }
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __removeFromEngineBin   =   True
      }
    freeBranches {
         __ptr   =   BehaviourNode
         __family   =   ".ssl"
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __skipDefaults   =   True
         __removeFromEngineBin   =   True
      }
   }
   __extraData = {
      __sslVarsSection = "."
   }
}

BehaviourNode {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
            __generateSslTemplate = <<<@if OWNER_TYPE override GetTree () : @OWNER_TYPE
   return (@OWNER_TYPE)parent.GetTree()
end>>>
         }
         __viewType {
            __hiddenIn = "Diagram"
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __type = "none"
            __hidden = true
         }
      }
      name {
         __viewType {
            __hidden = true
         }
      }
      dbgIndex {
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
   __diagram {
      __nameVar = "name"
      __debugIndexVar = "dbgIndex"
   }   
}

BehaviourNodeLeaf {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __hidden = true
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }   
}

BehaviourNodeComposite {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __hidden = true
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
      childNodes {
         __viewType {
            __hidden = true
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

BehaviourNodeDecorator {
   __members {
      "#ssl" {
         __type = "sslDiagram"
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __hidden = true
            __type = "button"
            __value = "Open SSL"
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
      childNode {
         __viewType {
            __hidden = false
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

DebugUiButton {
   __members {
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType = "none"
      }
      "#ssl" {
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
            __hidden = false
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

DebugUiCheckbox {
   __members {
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType = "none"
      }
      "#ssl" {
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
            __hidden = false
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

DebugUiLinelayoutcontainer {
   __members {
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType = "none"
      }
      "#ssl" {
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
            __hidden = false
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

DbgUiBase {
   __members {
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType = "none"
      }
      "#ssl" {
         __extraData = {
            __generateSslSubClass = true
         }
         __viewType {
            __type = "button"
            __value = "Open SSL"
            __hidden = false
         }
      }
   }
   __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

BlueprintLibraryFunction   =   {
   __members   =   {
      external   =   {
         __elementType   =   {
            __type   =   "string"
            __diagram   =   {
               __type   =   "ioExternalMark"
            }
         }
         __type   =   "array"
      __removeFromEngineBin = true
         __viewType   =   {
            __hidden   =   True
         }
      }
   }
   __diagram   =   {
      __type   =   "ioGroupShape"
   }
}

InputActionBase = {
    __members {
       "#ssl" {
       __type = "ssl"
       __extraData = {
         __generateSslSubClass = true
      }
      __viewType {
         __type = "button"
         __value = "[deprecated] Open SSL"
         __hidden = false
         }
      }
      sslClass {
         __type = "string"
         __removeFromEngineBin = true
         __viewType {
            __hidden = true
         }
      }
    }
    __extraData = {
      __sslVarsSection = "."
      __sslClass = "./sslClass"
   }
}

BlueprintNode = {
   __diagram {
      __nameVar = "captionVar"
   }
}

BluePrint   =   {
   __members   =   {
      groups   =   {
         __internalTypes   =   {
            NodeGroup   =   {
            }
         }
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __removeFromEngineBin   =   True
      }
   }
}

BlueprintStaticNode = {
   __members {
      captionVar {
         __type = "string"
         __viewType {
            __hidden = true
         }
         __removeFromEngineBin = true
      }
   }
   __diagram {
      __nameVar = "captionVar"
   }
}

BlueprintStatic   =   {
   __members   =   {
      groups   =   {
         __internalTypes   =   {
            NodeGroup   =   {
            }
         }
         __type   =   "collection"
         __viewType   =   {
            __hidden   =   True
         }
         __removeFromEngineBin   =   True
      }
   }
}