__request   =   {
   __select   =   {
      __id   =   "cat"
      __select   =   {
         __id   =   "desc"
         __path   =   "eventDescriptions/*"
         __type   =   "model"
      }
      __path   =   "eventDescriptionCollections/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "GameEventDescriptionLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{desc}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

