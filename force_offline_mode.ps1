# Space Marine 2 - Force Offline Mode for Mods
# This script helps set up offline mode for mod testing

Write-Host "Setting up Offline Mode for Space Marine 2 Mods" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 1: STEAM OFFLINE MODE" -ForegroundColor Yellow
Write-Host "1. Close Space Marine 2 completely" -ForegroundColor White
Write-Host "2. In Steam, go to: Steam > Go Offline" -ForegroundColor White
Write-Host "3. Click 'Restart in Offline Mode'" -ForegroundColor White
Write-Host "4. Wait for <PERSON> to restart in offline mode" -ForegroundColor White
Write-Host ""

Write-Host "STEP 2: DISABLE NETWORK (OPTIONAL)" -ForegroundColor Yellow
Write-Host "For maximum isolation:" -ForegroundColor White
Write-Host "1. Disconnect from internet/WiFi" -ForegroundColor White
Write-Host "2. Or disable network adapter temporarily" -ForegroundColor White
Write-Host ""

Write-Host "STEP 3: LAUNCH PARAMETERS" -ForegroundColor Yellow
Write-Host "Add these launch parameters in Steam:" -ForegroundColor White
Write-Host "1. Right-click Space Marine 2 in Steam" -ForegroundColor White
Write-Host "2. Properties > General > Launch Options" -ForegroundColor White
Write-Host "3. Add: -offline -nosteam -dev" -ForegroundColor Cyan
Write-Host ""

# Create launch parameter file
$launchParams = @"
-offline
-nosteam
-dev
-console
-noeac
"@

$launchParams | Out-File "launch_parameters.txt" -Encoding UTF8
Write-Host "Created launch_parameters.txt with recommended parameters" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 4: BACKUP CURRENT SAVE" -ForegroundColor Yellow
Write-Host "Backing up current save files..." -ForegroundColor White

# Backup save files
$saveDir = "C:\Program Files (x86)\Steam\userdata\1758870712\2183900"
$backupDir = "save_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

if (Test-Path $saveDir) {
    Copy-Item $saveDir $backupDir -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Save backup created: $backupDir" -ForegroundColor Green
} else {
    Write-Host "Save directory not found - may not be needed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "STEP 5: TEST SEQUENCE" -ForegroundColor Yellow
Write-Host "1. Ensure Steam is in offline mode" -ForegroundColor White
Write-Host "2. Launch Space Marine 2" -ForegroundColor White
Write-Host "3. Start Campaign mode (offline)" -ForegroundColor White
Write-Host "4. Test weapons in first mission" -ForegroundColor White
Write-Host ""

Write-Host "ALTERNATIVE: OPERATIONS MODE" -ForegroundColor Cyan
Write-Host "If Campaign doesn't work:" -ForegroundColor White
Write-Host "1. Try Operations mode" -ForegroundColor White
Write-Host "2. Create private lobby" -ForegroundColor White
Write-Host "3. Set to 'Friends Only' or 'Private'" -ForegroundColor White
Write-Host "4. Start solo mission" -ForegroundColor White
Write-Host ""

Write-Host "EXPECTED RESULTS IN OFFLINE MODE:" -ForegroundColor Green
Write-Host "- Pistol: 999+ damage, one-shot kills" -ForegroundColor White
Write-Host "- Melee: 1500+ damage, instant kills" -ForegroundColor White
Write-Host "- No server validation interference" -ForegroundColor White
Write-Host "- Mods should work properly" -ForegroundColor White
Write-Host ""

Write-Host "If still not working in offline mode:" -ForegroundColor Red
Write-Host "- Game may have local anti-cheat" -ForegroundColor White
Write-Host "- Need to try different file locations" -ForegroundColor White
Write-Host "- May need memory editing instead" -ForegroundColor White
Write-Host ""

Write-Host "Ready! Set Steam to offline mode and test." -ForegroundColor Green
