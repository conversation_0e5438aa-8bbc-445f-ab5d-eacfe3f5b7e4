__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __path   =   "<project_dir>/local/animations/morpheme/{arg}/mr_link_browser_data.json|<project_dir>/resources/morpheme/{arg}/mr_link_browser_data.json"
            __root   =   "{arg}.paramsBool"
            __type   =   "jsonLink"
         }
         __path   =   "nameBundle"
         __type   =   "model"
      }
      __path   =   "/Content/properties/prop_morpheme_controller/morphemeController/"
      __type   =   "model"
   }
   __type   =   "document"
}
__type   =   "linkBrowser"

