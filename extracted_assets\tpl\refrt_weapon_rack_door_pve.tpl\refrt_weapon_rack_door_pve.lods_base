lodInfo = {
   maxLodDist = [
      10,
      20,
      40,
      60,
      100,
      300
   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      glr = {
         collision = {
            abilityProjectile = false
            light = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            soundOcclusion = true
            soundObstruction = true
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      eye_green = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      mesh = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      mesh_lod1 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      mesh_lod2 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
