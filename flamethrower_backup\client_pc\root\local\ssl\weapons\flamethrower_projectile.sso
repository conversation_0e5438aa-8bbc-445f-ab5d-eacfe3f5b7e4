// Space Marine 2 - Flamethrower Projectile System
// This file defines the flame stream projectile behavior

FirearmDescriber = {
    shotsDesc = {
        flame_stream = {
            uid = "flame_stream"
            name = "Flame Stream"
            type = "continuous_projectile"
            
            // Basic projectile physics
            physics = {
                speed = 25.0
                mass = 0.1
                gravity = 0.5
                airResistance = 0.8
                lifetime = 0.8
                bounces = 0
                penetration = 0
            }
            
            // Damage configuration
            damage = {
                base = 45.0
                type = "fire"
                armorPenetration = 0.3
                criticalChance = 0.15
                criticalMultiplier = 1.5
                
                // Area damage
                areaOfEffect = {
                    enabled = true
                    radius = 3.0
                    shape = "cone"
                    angle = 45.0
                    maxTargets = 8
                    friendlyFire = false
                    
                    // Damage falloff within AoE
                    falloff = {
                        type = "linear"
                        centerMultiplier = 1.0
                        edgeMultiplier = 0.3
                    }
                }
                
                // Damage over time while projectile exists
                continuousDamage = {
                    enabled = true
                    tickRate = 0.05        // 20 ticks per second
                    tickDamage = 45.0      // Full damage per tick
                    duration = 0.8         // Lasts for projectile lifetime
                }
            }
            
            // Status effect application
            statusEffects = {
                burning = {
                    enabled = true
                    applyChance = 0.9
                    duration = 5.0
                    damagePerSecond = 10.0
                    stackable = true
                    maxStacks = 3
                    
                    // Burning visual effects
                    effect = "sfx_enemy_burning"
                    sound = "sfx_burning_loop"
                    
                    // Spread burning to nearby enemies
                    spread = {
                        enabled = true
                        radius = 2.0
                        chance = 0.3
                        maxTargets = 3
                    }
                }
                
                // Fear effect on weaker enemies
                fear = {
                    enabled = true
                    applyChance = 0.3
                    duration = 2.0
                    affectedTypes = [
                        "tyranid_basic",
                        "cultist",
                        "chaos_marine_basic"
                    ]
                    
                    // Fear behavior
                    behavior = "flee"
                    speedMultiplier = 1.5
                    accuracyPenalty = 0.5
                }
            }
            
            // Environmental interactions
            environmental = {
                igniteObjects = {
                    enabled = true
                    chance = 0.8
                    radius = 2.0
                    duration = 10.0
                    spreadChance = 0.4
                    spreadRadius = 1.5
                }
                
                clearVegetation = {
                    enabled = true
                    radius = 2.0
                    regrowthTime = 30.0
                }
                
                meltIce = {
                    enabled = true
                    radius = 1.5
                    refreezeTime = 60.0
                }
                
                // Create temporary fire patches on ground
                groundFire = {
                    enabled = true
                    duration = 5.0
                    radius = 1.5
                    damagePerSecond = 15.0
                    maxPatches = 10
                    
                    // Visual effect for ground fire
                    effect = "sfx_ground_fire"
                    scorchMark = "scr_ground_burn"
                }
            }
            
            // Collision behavior
            collision = {
                terrain = {
                    behavior = "spread"
                    spreadRadius = 2.0
                    createGroundFire = true
                }
                
                enemies = {
                    behavior = "damage"
                    penetrate = false
                    applyStatusEffects = true
                }
                
                objects = {
                    behavior = "ignite"
                    destructible = true
                    igniteChance = 0.8
                }
            }
            
            // Visual effects
            visual = {
                // Main flame stream
                flameStream = {
                    effect = "sfx_flamethrower_stream"
                    color = [255, 120, 0]
                    intensity = 2.5
                    width = 1.2
                    length = 15.0
                    
                    // Animation properties
                    flicker = {
                        rate = 0.1
                        intensity = 0.3
                    }
                    
                    // Particle system
                    particles = {
                        count = 100
                        lifetime = 1.5
                        speed = 15.0
                        spread = 30.0
                    }
                }
                
                // Impact effects
                impact = {
                    effect = "sfx_flame_impact"
                    duration = 1.0
                    radius = 2.0
                    sparks = true
                    smoke = true
                }
                
                // Scorch marks
                scorchMark = {
                    texture = "scr_flamethrower"
                    size = 2.5
                    duration = 60.0
                    fadeTime = 10.0
                }
            }
            
            // Audio effects
            audio = {
                flameLoop = {
                    sound = "wpn_flamethrower_flame_loop"
                    volume = 0.8
                    pitch = 1.0
                    loop = true
                    fadeIn = 0.2
                    fadeOut = 0.5
                }
                
                impact = {
                    sound = "wpn_flame_impact"
                    volume = 0.6
                    pitchVariation = 0.2
                    maxInstances = 3
                }
            }
            
            __type = "ProjectileDescription"
        }
    }
    
    __type = "FirearmDescriber"
}
