steps:
  - RES3_SCENE_SSL
  - RES3_SCENE_SSL_DESC
  - RES3_SCENE_SSL_DESC_FINALIZE
  - RES3_SCENE_SDR_PRESETS
  - RES3_SCENE_ADAPTER_LOADING
  - RES3_SCENE_TPL
  - RES3_SCENE_TPL_MARKUP
  - RES3_SCENE_ALL
  

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_SCENE_SSL
  - name: finalize_ssl_descs_loading
    on:
      - step: RES3_SCENE_SSL_DESC_FINALIZE   
  - name: adapter_loading
    on:
      - step: RES3_SCENE_ADAPTER_LOADING      

rules:
  # ssl
  #
  - name: process scene ssl
    res_type: res_desc_ssl
    step: RES3_SCENE_SSL

  #
  # ssl descs
  #
  - name: process scene ssolib
    res_type: res_desc_ssolib
    step: RES3_SCENE_SSL_DESC

  - name: process scene sso
    res_type: res_desc_sso
    step: RES3_SCENE_SSL_DESC

  - name: process scene cls
    res_type: res_desc_cls
    step: RES3_SCENE_SSL_DESC

  - name: process scene prop
    res_type: res_desc_prop
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab
    res_type: res_prefab_desc
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab asset
    res_type: res_desc_prefab_asset
    step: RES3_SCENE_SSL_DESC

  #
  # basic descs
  #
  - name: process scene basic descs
    res_type: res_basic_ssl_desc
    step: RES3_SCENE_SSL_DESC

  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: RES3_SCENE_SDR_PRESETS

  #
  # tpl descs
  #
  - name: process scene tpl descs
    res_type: res_desc_tpl
    step: RES3_SCENE_TPL

  #
  # tpl_markup descs
  #
  - name: process scene tpl_markup descs
    res_type: res_desc_tpl_markup
    step: RES3_SCENE_TPL_MARKUP

  #
  # all
  #
  - name: load all other SCENE resources
    use_default_loader: true
    step: RES3_SCENE_ALL