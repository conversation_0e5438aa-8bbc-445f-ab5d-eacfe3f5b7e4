{"asset": {"generator": "Khronos glTF Blender I/O v4.3.47", "version": "2.0"}, "scene": 0, "scenes": [{"extras": {"vs": {"export_list": [{"name": "Collection.dmx", "collection": {"name": "Collection", "type": "Collection"}, "ob_type": "COLLECTION", "icon": "GROUP"}, {"name": "barrel_glr.dmx", "ob_type": "OBJECT", "icon": "OUTLINER_OB_MESH", "obj": {"name": "barrel_glr", "type": "Object"}}]}, "name": "", "type": "tpl", "ps": "options = {\n   geom = {\n      optimize_pc = true\n      share_geometry = true\n      build_skin_compound = true\n      weld_vtx = 0.001000\n      weld_uv = 0.001000\n      export_decals = false\n      subdivide_vis = false\n      subdivide_edge = false\n      subdivide_merge = false\n      subdivide_merge_count = 200\n      disable_tpl_edge = true\n      dyn_cont = true\n   }\n   advanced = {\n      disable_bulldozer1 = true\n      disable_export = false\n      disable_resource_manager = false\n      disable_shadow_map_converter = false\n      memory_file_size = 96\n      optim_tpl = true\n      dont_run_lsagen = true\n      show_lsagen_wnd = false\n      disable_scene_optimization = false\n      disable_scene_group_optimization = false\n      remove_degenerated_faces_havok = true\n      export_mtl_in_text_format = true\n      optimize_indices_for_tl_cache = true\n   }\n   compression = {\n      compress_verts_tpl = true\n      compress_stat_verts = {\n         precision = 0.002000\n         __type_id = \"yes\"\n      }\n      compress_normals = true\n      compress_norm_to_4verts = true\n      compress_texture = true\n   }\n   src_filename = \"destructions\\\\phys_imp_barrel_02.mb\"\n   src_in_common = false\n   src_dir_key = \"database-src-dir\"\n   sync_folder = true\n}\n"}, "name": "Scene", "nodes": [14]}], "nodes": [{"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "camera"}, "name": "front", "translation": [0, 0, 1000.0999755859375]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "camera"}, "name": "persp", "rotation": [-0.23293699324131012, -0.16371721029281616, -0.03981665149331093, 0.95778489112854], "translation": [-1.1043874025344849, 1.9924191236495972, 3.136078119277954]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "saberDxNode"}, "name": "s3dSaberDxTechnicalNode"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "camera"}, "name": "side", "rotation": [0, 0.7071068286895752, 0, 0.7071067094802856], "translation": [1000.0999755859375, 0, 0]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": "camera"}, "name": "top", "rotation": [-0.70710688829422, 0, 0, 0.7071066498756409], "translation": [0, 1000.0999755859375, 0]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_position\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\ncdt_low_res_geometry\n\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 0, "name": "barrel_glr", "translation": [0, -0.012007713317871094, 0]}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "cdt_high_res_geometry\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nlod_compex_level\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 1, "name": "barrel_mesh_lod1"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "cdt_high_res_geometry\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nlod_compex_level\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 2, "name": "barrel_mesh_lod2"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "cdt_off_all\n\nlod_compex_level\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 3, "name": "barrel_mesh_lod3"}, {"extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "cdt_off_all\n\nlod_compex_level\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 4, "name": "barrel_mesh_lod4", "translation": [0, -0.5470287203788757, 0]}, {"children": [6, 7, 8, 9], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\ncdt_high_res_geometry\n\n\n\n\nlod_compex_level\n\nlod_system_root\ndistances\n10.0 25.0 50.0 80.0 120.0\nis_shared\nFalse\nhide_at_last_distance\nTrue\n\n", "mayaNodeId": "mesh", "vs": {}}, "mesh": 5, "name": "barrel_mesh"}, {"mesh": 6, "name": "squarrel", "translation": [0, -0.5470287203788757, 0]}, {"children": [5, 10, 11], "name": "barrel_body", "translation": [0, 0.5470287203788757, 0]}, {"children": [12], "extras": {"pses": {"version": 258, "ps": {"str": "tpl_name = \"destructions\\\\phys_imp_barrel_02.tpl\";__type = \"iactor\";ExportOptions = {generateCollisionData = true;buildSkinCompound = true;};"}}, "affixes": "", "mayaNodeId": "saberActor", "actor": {"actorTypeString": "tpl_desc", "actorType": "USF_ACTORTYPE_EXTERNAL", "tplName": "destructions\\phys_imp_barrel_02.tpl", "animations": [], "sourceUsfUids": [], "refName": "", "isNested": false, "isRTC": false, "clsName": "", "isSubNested": false}}, "name": "tpl_desc"}, {"children": [0, 1, 2, 3, 4, 13], "extras": {"pses": {"version": 258, "ps": {"str": ""}}, "affixes": "", "mayaNodeId": ""}, "name": ".root."}], "materials": [{"doubleSided": true, "name": "Material_0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}, {"doubleSided": true, "name": "imp_barrel_02_mat0", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1]}}, {"doubleSided": true, "name": "test_squarrel", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "material = {\n   version = 2\n   type = 0\n}\nlayers = [\n\n]\nuvSets = [\n\n]\ncolorSets = [\n\n]\n"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_glr", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2, "TEXCOORD_1": 3, "TEXCOORD_2": 4, "TEXCOORD_3": 5, "COLOR_0": 6, "COLOR_1": 7, "COLOR_2": 8}, "indices": 9, "material": 0}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"imp_barrel_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"imp_barrel_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"imp_barrel_02_hdetm\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_mesh_lod1", "primitives": [{"attributes": {"POSITION": 10, "NORMAL": 11, "TEXCOORD_0": 12, "TEXCOORD_1": 13, "TEXCOORD_2": 14, "TEXCOORD_3": 15, "COLOR_0": 16, "COLOR_1": 17, "COLOR_2": 18}, "indices": 19, "material": 1}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"imp_barrel_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"imp_barrel_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"imp_barrel_02_hdetm\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_mesh_lod2", "primitives": [{"attributes": {"POSITION": 20, "NORMAL": 21, "TEXCOORD_0": 22, "TEXCOORD_1": 23, "TEXCOORD_2": 24, "TEXCOORD_3": 25, "COLOR_0": 26, "COLOR_1": 27, "COLOR_2": 28}, "indices": 29, "material": 1}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"imp_barrel_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"imp_barrel_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"imp_barrel_02_hdetm\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_mesh_lod3", "primitives": [{"attributes": {"POSITION": 30, "NORMAL": 31, "TEXCOORD_0": 32, "TEXCOORD_1": 33, "TEXCOORD_2": 34, "TEXCOORD_3": 35, "COLOR_0": 36, "COLOR_1": 37, "COLOR_2": 38}, "indices": 39, "material": 1}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"imp_barrel_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"imp_barrel_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"imp_barrel_02_hdetm\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_mesh_lod4", "primitives": [{"attributes": {"POSITION": 40, "NORMAL": 41, "TEXCOORD_0": 42, "TEXCOORD_1": 43, "TEXCOORD_2": 44, "TEXCOORD_3": 45, "COLOR_0": 46, "COLOR_1": 47, "COLOR_2": 48}, "indices": 49, "material": 1}]}, {"extras": {"materials": [{"vertexColorUsage": "VCU_NO", "data": {"str": "auxiliaryTextures = {\n   mask = {\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ncolorSets = [\n]\ndynamicParameters = [\n]\nextraUVData = {\n   uvSetIdx = -1\n}\nextraVertexColorData = {\n   colorA = {\n      colorSetIdx = -1\n   }\n   colorB = {\n      colorSetIdx = -1\n   }\n   colorG = {\n      colorSetIdx = -1\n   }\n   colorR = {\n      colorSetIdx = -1\n   }\n}\nlayers = [\n   {\n      albedo_tint = [\n         255,\n         255,\n         255,\n         255\n      ]\n      blending = {\n         heightmap = {\n            colorSetIdx = -1\n            invert = 0\n            softnessMultiplier = 1.000000\n            useHeightmapBlending = 0\n         }\n         normalAdditiveBlend = 0\n         upVector = {\n            angle = 90.000000\n            enabled = 0\n            falloff = 0.000000\n         }\n         weights = {\n            colorSetIdx = -1\n            multiplier = 1.000000\n            useFromLayerAlpha = 0\n            useFromMaskChannel = -1\n         }\n      }\n      heightmapOverride = \"\"\n      heightmapUVOverride = {\n         enabled = 0\n         tilingU = 1.000000\n         tilingV = 1.000000\n         uvSetIdx = -1\n      }\n      isVisible = 1\n      subMaterial = \"default\"\n      textureName = \"imp_barrel_02\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      type = 0\n      uvSetIdx = 0\n   }\n]\nmaterial = {\n   name = \"imp_barrel_02_mat0\"\n   parent = \"\"\n   subMaterial = \"\"\n   type = 0\n   version = 2\n}\nocclusion = {\n   occlusionTexture = {\n      colorSetIdx = -1\n      isVisible = 1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\nparallax = {\n   baseLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"imp_barrel_02_hdetm\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n   parallaxSettings = {\n      enableSecondParallaxLayer = 0\n      flatten = {\n         colorSetIdx = -1\n      }\n      overrideSecondParallaxTexture = 0\n   }\n   secondLayerParallax = {\n      colorSetIdx = -1\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = 0\n   }\n}\nreliefNormalmaps = {\n   macro = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro1 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n   micro2 = {\n      end = 0.000000\n      falloff = 5.000000\n      isVisible = 1\n      scale = 1.000000\n      start = 0.000000\n      textureName = \"\"\n      tilingU = 1.000000\n      tilingV = 1.000000\n      uvSetIdx = -1\n   }\n}\ntransparency = {\n   colorSetIdx = -1\n   dirtModifyAlpha = 0\n   enabled = 0\n   multiplier = 1.000000\n   sources = 0\n   useBlendMaskAlpha = 0\n}\nuvSets = [\n   {\n      isUsedInLayersOnly = 1\n      name = \"map1\"\n   }\n]"}}], "skinngMethod": "CLASSIC_LINEAR"}, "name": "barrel_mesh", "primitives": [{"attributes": {"POSITION": 50, "NORMAL": 51, "TEXCOORD_0": 52, "TEXCOORD_1": 53, "TEXCOORD_2": 54, "TEXCOORD_3": 55, "COLOR_0": 56, "COLOR_1": 57, "COLOR_2": 58}, "indices": 59, "material": 1}]}, {"name": "Cube.001", "primitives": [{"attributes": {"POSITION": 60, "NORMAL": 61, "TEXCOORD_0": 62}, "indices": 63, "material": 2}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 62, "max": [0.37065815925598145, 0.5390459895133972, 0.35251688957214355], "min": [-0.37065815925598145, -0.5390459895133972, -0.35251688957214355], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 62, "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 62, "type": "VEC2"}, {"bufferView": 3, "componentType": 5126, "count": 62, "type": "VEC2"}, {"bufferView": 4, "componentType": 5126, "count": 62, "type": "VEC2"}, {"bufferView": 5, "componentType": 5126, "count": 62, "type": "VEC2"}, {"bufferView": 6, "componentType": 5121, "count": 62, "normalized": true, "type": "VEC4"}, {"bufferView": 7, "componentType": 5123, "count": 62, "normalized": true, "type": "VEC4"}, {"bufferView": 8, "componentType": 5123, "count": 62, "normalized": true, "type": "VEC4"}, {"bufferView": 9, "componentType": 5123, "count": 120, "type": "SCALAR"}, {"bufferView": 10, "componentType": 5126, "count": 2174, "max": [0.38247671723365784, 0.5513291954994202, 0.3823302090167999], "min": [-0.38095104694366455, -0.5516600012779236, -0.3823302090167999], "type": "VEC3"}, {"bufferView": 11, "componentType": 5126, "count": 2174, "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 2174, "type": "VEC2"}, {"bufferView": 13, "componentType": 5126, "count": 2174, "type": "VEC2"}, {"bufferView": 14, "componentType": 5126, "count": 2174, "type": "VEC2"}, {"bufferView": 15, "componentType": 5126, "count": 2174, "type": "VEC2"}, {"bufferView": 16, "componentType": 5121, "count": 2174, "normalized": true, "type": "VEC4"}, {"bufferView": 17, "componentType": 5123, "count": 2174, "normalized": true, "type": "VEC4"}, {"bufferView": 18, "componentType": 5123, "count": 2174, "normalized": true, "type": "VEC4"}, {"bufferView": 19, "componentType": 5123, "count": 4452, "type": "SCALAR"}, {"bufferView": 20, "componentType": 5126, "count": 1388, "max": [0.38693690299987793, 0.5519343018531799, 0.3828034996986389], "min": [-0.3868299722671509, -0.5545749664306641, -0.3828034996986389], "type": "VEC3"}, {"bufferView": 21, "componentType": 5126, "count": 1388, "type": "VEC3"}, {"bufferView": 22, "componentType": 5126, "count": 1388, "type": "VEC2"}, {"bufferView": 23, "componentType": 5126, "count": 1388, "type": "VEC2"}, {"bufferView": 24, "componentType": 5126, "count": 1388, "type": "VEC2"}, {"bufferView": 25, "componentType": 5126, "count": 1388, "type": "VEC2"}, {"bufferView": 26, "componentType": 5121, "count": 1388, "normalized": true, "type": "VEC4"}, {"bufferView": 27, "componentType": 5123, "count": 1388, "normalized": true, "type": "VEC4"}, {"bufferView": 28, "componentType": 5123, "count": 1388, "normalized": true, "type": "VEC4"}, {"bufferView": 29, "componentType": 5123, "count": 2226, "type": "SCALAR"}, {"bufferView": 30, "componentType": 5126, "count": 267, "max": [0.37546399235725403, 0.5411373972892761, 0.3754640221595764], "min": [-0.37546399235725403, -0.5510537624359131, -0.3754640221595764], "type": "VEC3"}, {"bufferView": 31, "componentType": 5126, "count": 267, "type": "VEC3"}, {"bufferView": 32, "componentType": 5126, "count": 267, "type": "VEC2"}, {"bufferView": 33, "componentType": 5126, "count": 267, "type": "VEC2"}, {"bufferView": 34, "componentType": 5126, "count": 267, "type": "VEC2"}, {"bufferView": 35, "componentType": 5126, "count": 267, "type": "VEC2"}, {"bufferView": 36, "componentType": 5121, "count": 267, "normalized": true, "type": "VEC4"}, {"bufferView": 37, "componentType": 5123, "count": 267, "normalized": true, "type": "VEC4"}, {"bufferView": 38, "componentType": 5123, "count": 267, "normalized": true, "type": "VEC4"}, {"bufferView": 39, "componentType": 5123, "count": 1008, "type": "SCALAR"}, {"bufferView": 40, "componentType": 5126, "count": 70, "max": [0.37870967388153076, 1.0744519233703613, 0.378709614276886], "min": [-0.37870967388153076, 0, -0.378709614276886], "type": "VEC3"}, {"bufferView": 41, "componentType": 5126, "count": 70, "type": "VEC3"}, {"bufferView": 42, "componentType": 5126, "count": 70, "type": "VEC2"}, {"bufferView": 43, "componentType": 5126, "count": 70, "type": "VEC2"}, {"bufferView": 44, "componentType": 5126, "count": 70, "type": "VEC2"}, {"bufferView": 45, "componentType": 5126, "count": 70, "type": "VEC2"}, {"bufferView": 46, "componentType": 5121, "count": 70, "normalized": true, "type": "VEC4"}, {"bufferView": 47, "componentType": 5123, "count": 70, "normalized": true, "type": "VEC4"}, {"bufferView": 48, "componentType": 5123, "count": 70, "normalized": true, "type": "VEC4"}, {"bufferView": 49, "componentType": 5123, "count": 180, "type": "SCALAR"}, {"bufferView": 50, "componentType": 5126, "count": 1809, "max": [0.3806900084018707, 0.5510537028312683, 0.3806900084018707], "min": [-0.3806900084018707, -0.5510537624359131, -0.3806900084018707], "type": "VEC3"}, {"bufferView": 51, "componentType": 5126, "count": 1809, "type": "VEC3"}, {"bufferView": 52, "componentType": 5126, "count": 1809, "type": "VEC2"}, {"bufferView": 53, "componentType": 5126, "count": 1809, "type": "VEC2"}, {"bufferView": 54, "componentType": 5126, "count": 1809, "type": "VEC2"}, {"bufferView": 55, "componentType": 5126, "count": 1809, "type": "VEC2"}, {"bufferView": 56, "componentType": 5121, "count": 1809, "normalized": true, "type": "VEC4"}, {"bufferView": 57, "componentType": 5123, "count": 1809, "normalized": true, "type": "VEC4"}, {"bufferView": 58, "componentType": 5123, "count": 1809, "normalized": true, "type": "VEC4"}, {"bufferView": 59, "componentType": 5123, "count": 8922, "type": "SCALAR"}, {"bufferView": 60, "componentType": 5126, "count": 24, "max": [0.3960992395877838, 1.1311955451965332, 0.3960992395877838], "min": [-0.3960992395877838, 0.0026619434356689453, -0.3960992395877838], "type": "VEC3"}, {"bufferView": 61, "componentType": 5126, "count": 24, "type": "VEC3"}, {"bufferView": 62, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 63, "componentType": 5123, "count": 36, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 744, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 744, "byteOffset": 744, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 1488, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 1984, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 2480, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 2976, "target": 34962}, {"buffer": 0, "byteLength": 248, "byteOffset": 3472, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 3720, "target": 34962}, {"buffer": 0, "byteLength": 496, "byteOffset": 4216, "target": 34962}, {"buffer": 0, "byteLength": 240, "byteOffset": 4712, "target": 34963}, {"buffer": 0, "byteLength": 26088, "byteOffset": 4952, "target": 34962}, {"buffer": 0, "byteLength": 26088, "byteOffset": 31040, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 57128, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 74520, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 91912, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 109304, "target": 34962}, {"buffer": 0, "byteLength": 8696, "byteOffset": 126696, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 135392, "target": 34962}, {"buffer": 0, "byteLength": 17392, "byteOffset": 152784, "target": 34962}, {"buffer": 0, "byteLength": 8904, "byteOffset": 170176, "target": 34963}, {"buffer": 0, "byteLength": 16656, "byteOffset": 179080, "target": 34962}, {"buffer": 0, "byteLength": 16656, "byteOffset": 195736, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 212392, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 223496, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 234600, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 245704, "target": 34962}, {"buffer": 0, "byteLength": 5552, "byteOffset": 256808, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 262360, "target": 34962}, {"buffer": 0, "byteLength": 11104, "byteOffset": 273464, "target": 34962}, {"buffer": 0, "byteLength": 4452, "byteOffset": 284568, "target": 34963}, {"buffer": 0, "byteLength": 3204, "byteOffset": 289020, "target": 34962}, {"buffer": 0, "byteLength": 3204, "byteOffset": 292224, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 295428, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 297564, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 299700, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 301836, "target": 34962}, {"buffer": 0, "byteLength": 1068, "byteOffset": 303972, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 305040, "target": 34962}, {"buffer": 0, "byteLength": 2136, "byteOffset": 307176, "target": 34962}, {"buffer": 0, "byteLength": 2016, "byteOffset": 309312, "target": 34963}, {"buffer": 0, "byteLength": 840, "byteOffset": 311328, "target": 34962}, {"buffer": 0, "byteLength": 840, "byteOffset": 312168, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 313008, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 313568, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 314128, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 314688, "target": 34962}, {"buffer": 0, "byteLength": 280, "byteOffset": 315248, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 315528, "target": 34962}, {"buffer": 0, "byteLength": 560, "byteOffset": 316088, "target": 34962}, {"buffer": 0, "byteLength": 360, "byteOffset": 316648, "target": 34963}, {"buffer": 0, "byteLength": 21708, "byteOffset": 317008, "target": 34962}, {"buffer": 0, "byteLength": 21708, "byteOffset": 338716, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 360424, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 374896, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 389368, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 403840, "target": 34962}, {"buffer": 0, "byteLength": 7236, "byteOffset": 418312, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 425548, "target": 34962}, {"buffer": 0, "byteLength": 14472, "byteOffset": 440020, "target": 34962}, {"buffer": 0, "byteLength": 17844, "byteOffset": 454492, "target": 34963}, {"buffer": 0, "byteLength": 288, "byteOffset": 472336, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 472624, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 472912, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 473104, "target": 34963}], "buffers": [{"byteLength": 473176, "uri": "phys_imp_barrel_02.bin"}]}