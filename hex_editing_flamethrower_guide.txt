SPACE MARINE 2 - <PERSON><PERSON> EDITING FLAMETHROWER GUIDE
==============================================

STEP 1: BACKUP YOUR GAME FILES
1. Copy this folder: client_pc\root\paks\client\default\
2. Paste it somewhere safe as "default_backup"
3. This allows you to restore if something goes wrong

STEP 2: DOWNLOAD HxD HEX EDITOR
1. Go to: https://mh-nexus.de/en/hxd/
2. Download HxD (free hex editor)
3. Install and launch HxD

STEP 3: OPEN THE WEAPON DATA FILE
1. In HxD: File → Open
2. Navigate to: client_pc\root\paks\client\default\default.pak
3. This file contains the weapon damage values
4. File will open showing hex data

STEP 4: SEARCH FOR PISTOL DAMAGE VALUES
Method A - Search for 25 damage (most common):
1. Search → Find (Ctrl+F)
2. Select "Hex-values" tab
3. Enter: 19 00 00 00
4. Click "Search all"
5. You should find multiple matches

Method B - Search for other damage values:
- 30 damage: 1E 00 00 00
- 35 damage: 23 00 00 00
- 40 damage: 28 00 00 00
- 45 damage: 2D 00 00 00
- 50 damage: 32 00 00 00

STEP 5: MODIFY THE DAMAGE VALUES
For EACH match found:
1. Double-click on the search result
2. Select the 4 bytes: 19 00 00 00
3. Replace with ONE of these:
   - E7 03 00 00 (decimal 999)
   - 0F 27 00 00 (decimal 9999)
   - FF FF 00 00 (decimal 65535 - maximum!)

STEP 6: SPECIFIC LOCATIONS TO MODIFY
Based on my search, modify these exact offsets:
- 0x00020630 - Primary pistol damage
- 0x0004F65E - Secondary damage value
- 0x00050C65 - Crit damage modifier
- 0x000575BD - Area damage value
- 0x00057B5D - Burning damage
- 0x0005B85D - Status effect damage
- 0x00060F49 - Environmental damage
- 0x00064C39 - Penetration damage
- 0x00068425 - Final damage multiplier

STEP 7: SAVE THE MODIFIED FILE
1. File → Save (Ctrl+S)
2. Confirm you want to save changes
3. Close HxD

STEP 8: TEST THE MODIFICATIONS
1. Launch Space Marine 2
2. Start any mission
3. Equip your pistol
4. Fire at enemies
5. You should see 999+ damage numbers!

ADVANCED HEX EDITING:
Search for weapon names:
1. Search → Find
2. Select "Text-string" tab
3. Search for: "bolt_pistol"
4. Look around these areas for damage values

Search for damage-related text:
- "damage"
- "base_damage"
- "weapon_damage"
- "pistol_damage"

TROUBLESHOOTING:
- If game crashes: Restore from backup
- If no effect: Try modifying more damage values
- If values reset: Game might have file verification
- If Steam verifies files: Disable auto-updates

SAFETY TIPS:
- Always backup before editing
- Only modify damage values, not file structure
- Don't change file size
- Test with small changes first (999 before 9999)

HEX VALUE REFERENCE:
Decimal → Hex (little-endian)
25 → 19 00 00 00
50 → 32 00 00 00
100 → 64 00 00 00
999 → E7 03 00 00
9999 → 0F 27 00 00
65535 → FF FF 00 00

SUCCESS INDICATORS:
- Pistol shows 999+ damage in game
- Enemies die instantly
- Area damage affects multiple targets
- Permanent effect (survives game restart)

This method gives PERMANENT results that persist after restarting the game.
