# Space Marine 2 - Copy Mods from mods_source to local
# This script copies modified files from ModEditor/mods_source to client_pc/root/local
# maintaining the exact folder structure

param(
    [string]$SourcePath = "ModEditor\mods_source",
    [string]$DestPath = "client_pc\root\local",
    [switch]$WhatIf = $false,
    [switch]$IncludeResources = $true
)

Write-Host "Space Marine 2 Mod Copy Script" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Check if source directory exists
if (-not (Test-Path $SourcePath)) {
    Write-Error "Source path '$SourcePath' not found!"
    exit 1
}

# Create destination directory if it doesn't exist
if (-not (Test-Path $DestPath)) {
    Write-Host "Creating destination directory: $DestPath" -ForegroundColor Yellow
    if (-not $WhatIf) {
        New-Item -ItemType Directory -Path $DestPath -Force | Out-Null
    }
}

# Define file types to copy
$ModFileTypes = @("*.sso", "*.cls", "*.ssl")
$ResourceFileTypes = @("*.tpl", "*.pct", "*.resource", "*.cdt", "*.geom_dbg")

$FileTypesToCopy = $ModFileTypes
if ($IncludeResources) {
    $FileTypesToCopy += $ResourceFileTypes
}

Write-Host "File types to copy: $($FileTypesToCopy -join ', ')" -ForegroundColor Cyan

# Function to copy files with structure preservation
function Copy-ModFiles {
    param($Source, $Dest, $Types, $WhatIfMode)
    
    $CopiedCount = 0
    $SkippedCount = 0
    
    foreach ($FileType in $Types) {
        $Files = Get-ChildItem -Path $Source -Filter $FileType -Recurse
        
        foreach ($File in $Files) {
            # Calculate relative path from source
            $RelativePath = $File.FullName.Substring($Source.Length + 1)
            $DestFile = Join-Path $Dest $RelativePath
            $DestDir = Split-Path $DestFile -Parent
            
            # Check if file is newer or doesn't exist in destination
            $ShouldCopy = $true
            if (Test-Path $DestFile) {
                $SourceTime = $File.LastWriteTime
                $DestTime = (Get-Item $DestFile).LastWriteTime
                if ($SourceTime -le $DestTime) {
                    $ShouldCopy = $false
                    $SkippedCount++
                }
            }
            
            if ($ShouldCopy) {
                Write-Host "Copying: $RelativePath" -ForegroundColor White
                
                if (-not $WhatIfMode) {
                    # Create destination directory if it doesn't exist
                    if (-not (Test-Path $DestDir)) {
                        New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
                    }
                    
                    # Copy the file
                    Copy-Item $File.FullName $DestFile -Force
                }
                $CopiedCount++
            }
        }
    }
    
    return @{Copied = $CopiedCount; Skipped = $SkippedCount}
}

# Perform the copy operation
Write-Host "`nStarting copy operation..." -ForegroundColor Yellow
$Results = Copy-ModFiles -Source (Resolve-Path $SourcePath).Path -Dest (Resolve-Path $DestPath -ErrorAction SilentlyContinue) -Types $FileTypesToCopy -WhatIfMode $WhatIf

Write-Host "`nCopy operation completed!" -ForegroundColor Green
Write-Host "Files copied: $($Results.Copied)" -ForegroundColor Green
Write-Host "Files skipped (up to date): $($Results.Skipped)" -ForegroundColor Yellow

if ($WhatIf) {
    Write-Host "`nNote: This was a dry run. Use without -WhatIf to actually copy files." -ForegroundColor Cyan
}

Write-Host "`nNext steps:" -ForegroundColor Magenta
Write-Host "1. Verify files are in correct locations in client_pc/root/local/" -ForegroundColor White
Write-Host "2. Check any .pak mods in client_pc/root/mods/" -ForegroundColor White
Write-Host "3. Launch Space Marine 2 to test" -ForegroundColor White
