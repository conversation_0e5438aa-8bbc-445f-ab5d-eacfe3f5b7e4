
        #version 330 core
        
        layout (location = 0) in vec3 aPos;
        layout (location = 1) in vec2 aTexCoord;
        layout (location = 2) in vec3 aNormal;
        
        uniform mat4 model;
        uniform mat4 view;
        uniform mat4 projection;
        uniform float time;
        
        out vec2 TexCoord;
        out vec3 Normal;
        out vec3 FragPos;
        out float Time;
        
        void main() {
            // Add flame movement
            vec3 pos = aPos;
            pos.x += sin(time * 2.0 + aPos.y * 3.0) * 0.1;
            pos.z += cos(time * 1.5 + aPos.y * 2.0) * 0.05;
            
            FragPos = vec3(model * vec4(pos, 1.0));
            Normal = mat3(transpose(inverse(model))) * aNormal;
            TexCoord = aTexCoord;
            Time = time;
            
            gl_Position = projection * view * vec4(FragPos, 1.0);
        }
        