__request   =   {
   __select   =   {
      __id   =   "layer"
      __select   =   {
         __id   =   "final"
         __onlyChanged   =   False
         __groupByParent   =   2
         __format   =   "{name}"
         __where   =   {
            __parentType   =   "^InputActionBase$"
         }
         __type   =   "modelRecursive"
      }
      __path   =   "layers/*"
      __format   =   "{name}"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "KeymapGameEditor"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{layer}.{final}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

