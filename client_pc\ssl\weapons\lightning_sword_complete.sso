// Space Marine 2 - Complete Lightning Sword with Full Animations
// This replaces the chainsword with a fully animated lightning sword

MeleeWeaponLibraryPve = {
    meleeWeapons = {
        chainsword = {
            uid = "chainsword"
            name = "Thunder Blade"
            description = "Blessed power sword crackling with the Emperor's lightning"
            category = "melee"
            
            // Weapon type and behavior
            weaponType = "power_sword"
            damageType = "electrical"
            attackMode = "lightning_strike"
            
            // Devastating electrical damage
            damage = {
                base = 1500.0
                armorPenetration = 0.8
                criticalMultiplier = 3.0
                damageType = "electrical"
                statusEffect = "electrocution"
                chainLightning = true
            }
            
            // Lightning-fast attacks
            attackSpeed = {
                lightAttackSpeed = 2.0
                heavyAttackSpeed = 1.5
                comboSpeed = 2.5
                recoveryTime = 0.3
            }
            
            // Perfect handling
            handling = {
                weight = 1.5
                swingSpeed = 2.0
                blockSpeed = 1.8
                parryWindow = 0.5
                staminaCost = 0.5
            }
            
            // Lightning range
            range = {
                lightAttackRange = 3.0
                heavyAttackRange = 4.0
                blockRange = 2.0
                chainLightningRange = 8.0
            }
            
            // Complete visual effects system
            effects = {
                // Blade effects
                bladeAura = "fx_lightning_blade_constant_arc"
                bladeGlow = "fx_electric_blue_glow"
                bladeParticles = "fx_sparking_electricity"
                
                // Swing effects
                swingTrail = "fx_lightning_arc_trail"
                swingSparks = "fx_electric_sparks_burst"
                swingLight = "fx_blue_lightning_flash"
                
                // Impact effects
                lightAttackImpact = "fx_lightning_strike_small"
                heavyAttackImpact = "fx_lightning_explosion_large"
                criticalImpact = "fx_chain_lightning_burst"
                
                // Environmental effects
                environmentalEffects = {
                    groundLightning = "fx_ground_electric_arcs"
                    airIonization = "fx_air_electric_shimmer"
                    metalResonance = "fx_metal_electric_glow"
                    chainArcs = "fx_chain_lightning_spread"
                }
                
                // Screen effects
                screenEffects = {
                    electricFlash = "fx_screen_blue_flash"
                    staticOverlay = "fx_screen_static_burst"
                    lightningGlow = "fx_screen_electric_glow"
                }
            }
            
            // Complete audio system
            audio = {
                // Primary sounds
                idleHum = "sfx_lightning_sword_electric_hum"
                lightAttack = "sfx_lightning_crack_sharp"
                heavyAttack = "sfx_thunder_boom_deep"
                
                // Impact sounds
                metalImpact = "sfx_electric_metal_clash"
                fleshImpact = "sfx_electric_sizzle_burn"
                criticalImpact = "sfx_lightning_explosion"
                
                // Environmental audio
                environmentalAudio = {
                    sparkleLoop = "sfx_electric_sparks_continuous"
                    humLoop = "sfx_power_field_hum"
                    crackleSound = "sfx_electricity_crackle"
                    ionizationSound = "sfx_air_ionization_buzz"
                }
                
                // Chain lightning audio
                chainLightningAudio = {
                    chainSound = "sfx_chain_lightning_zap"
                    arcSound = "sfx_electric_arc_snap"
                    thunderRoll = "sfx_distant_thunder_roll"
                }
                
                // 3D audio properties
                audioProperties = {
                    volume = 1.3
                    pitch = 1.1
                    spatialBlend = 0.9
                    dopplerLevel = 0.3
                    rolloffMode = "linear"
                }
            }
            
            // Advanced mechanics
            specialMechanics = {
                // Chain lightning system
                chainLightning = {
                    enabled = true
                    maxTargets = 5
                    chainRange = 8.0
                    damageReduction = 0.8
                    chainDelay = 0.1
                    visualEffect = "fx_chain_lightning_arc"
                }
                
                // Electrical overload
                electricalOverload = {
                    enabled = true
                    chargeTime = 3.0
                    overloadDamage = 2000.0
                    overloadRadius = 6.0
                    cooldownTime = 10.0
                    visualEffect = "fx_electrical_overload_explosion"
                }
                
                // Status effects
                statusEffects = {
                    electrocution = {
                        enabled = true
                        applyChance = 1.0
                        duration = 6.0
                        damagePerSecond = 100.0
                        stunChance = 0.5
                        visualEffect = "fx_enemy_electrocuted"
                    }
                    
                    systemShock = {
                        enabled = true
                        applyChance = 0.7
                        duration = 4.0
                        effect = "disable_abilities"
                        visualEffect = "fx_enemy_system_shock"
                    }
                }
                
                // Environmental interaction
                environmentalInteraction = {
                    conductsElectricity = true
                    overloadsElectronics = true
                    ionizesAir = true
                    attractsLightning = true
                    disruptsShields = true
                }
            }
            
            // Animation system
            animations = {
                // Weapon animations
                weaponAnimations = {
                    idle = "anim_lightning_sword_electric_idle"
                    lightAttack1 = "anim_lightning_slash_horizontal"
                    lightAttack2 = "anim_lightning_thrust_forward"
                    lightAttack3 = "anim_lightning_uppercut_arc"
                    heavyAttack = "anim_lightning_overhead_slam"
                    block = "anim_lightning_defensive_stance"
                    parry = "anim_lightning_deflection"
                }
                
                // Character animations
                characterAnimations = {
                    combatStance = "anim_marine_lightning_combat_ready"
                    attackStance = "anim_marine_aggressive_lightning"
                    defensiveStance = "anim_marine_lightning_guard"
                    movementModifier = "anim_marine_energized_movement"
                }
                
                // First person animations
                firstPersonAnimations = {
                    lightAttackFP = "anim_fp_lightning_slash"
                    heavyAttackFP = "anim_fp_lightning_slam"
                    blockFP = "anim_fp_lightning_block"
                    inspectFP = "anim_fp_lightning_sword_inspect"
                }
                
                // Special attack animations
                specialAnimations = {
                    chainLightning = "anim_chain_lightning_cast"
                    electricalOverload = "anim_electrical_overload_charge"
                    lightningStrike = "anim_lightning_strike_from_sky"
                }
            }
            
            // Combo system
            comboSystem = {
                combos = {
                    lightningFlurry = {
                        sequence = ["light", "light", "heavy"]
                        damage = 2500.0
                        effect = "fx_lightning_flurry_combo"
                        sound = "sfx_lightning_flurry_combo"
                    }
                    
                    thunderStrike = {
                        sequence = ["heavy", "light", "heavy"]
                        damage = 3000.0
                        effect = "fx_thunder_strike_combo"
                        sound = "sfx_thunder_strike_combo"
                        areaEffect = true
                    }
                    
                    chainDestruction = {
                        sequence = ["light", "heavy", "light", "heavy"]
                        damage = 4000.0
                        effect = "fx_chain_destruction_combo"
                        sound = "sfx_chain_destruction_combo"
                        chainLightningBonus = true
                    }
                }
            }
            
            // UI display
            ui = {
                icon = "ui_lightning_sword_icon"
                description = "Crackling power sword that channels the Emperor's wrath through devastating electrical attacks"
                weaponClass = "Lightning Melee"
                unlockLevel = 1
                
                // Enhanced stats display
                stats = {
                    damage = 10
                    speed = 9
                    range = 7
                    special = 10
                    durability = 8
                }
                
                // UI effects
                uiEffects = {
                    iconSparks = "fx_ui_electric_sparks"
                    selectionEffect = "fx_ui_lightning_border"
                    chargeIndicator = "fx_ui_electrical_charge"
                }
            }
            
            __type = "MeleeWeaponDescription"
        }
    }
    
    __type = "MeleeWeaponLibrary"
}
