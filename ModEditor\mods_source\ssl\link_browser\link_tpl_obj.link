__request   =   {
   __select   =   {
      __where   =   {
         __parameters   =   {
            name   =   {
               __value   =   "^.+$"
               __type   =   "string_param"
            }
         }
      }
      __path   =   "<database_dir>/*/{arg}.tpl/obj_list.json|<tpl_assets_dir>/*/{arg}.tpl.asset/tpl/obj_list.json"
      __selectOnlyLeafNodes   =   False
      __root   =   "engine_objects"
      __type   =   "jsonLink"
   }
   __format   =   "{value}"
   __showValue   =   True
   __tag   =   "templateName"
   __type   =   "modelTag"
}
__addEmpty   =   {
   __value   =   True
}
__type   =   "linkBrowser"

