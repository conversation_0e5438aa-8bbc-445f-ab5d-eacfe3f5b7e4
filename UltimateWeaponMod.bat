@echo off
setlocal enabledelayedexpansion
title Space Marine 2 - Ultimate Weapon Mod
color 0A

cls
echo.
echo    ██╗   ██╗██╗  ████████╗██╗███╗   ███╗ █████╗ ████████╗███████╗
echo    ██║   ██║██║  ╚══██╔══╝██║████╗ ████║██╔══██╗╚══██╔══╝██╔════╝
echo    ██║   ██║██║     ██║   ██║██╔████╔██║███████║   ██║   █████╗  
echo    ██║   ██║██║     ██║   ██║██║╚██╔╝██║██╔══██║   ██║   ██╔══╝  
echo    ╚██████╔╝███████╗██║   ██║██║ ╚═╝ ██║██║  ██║   ██║   ███████╗
echo     ╚═════╝ ╚══════╝╚═╝   ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝
echo.
echo                    🔥 SPACE MARINE 2 WEAPON MOD 🔥
echo                   ================================
echo.
echo  🎯 WHAT THIS DOES:
echo  ✓ Automatically finds Space Marine 2 process
echo  ✓ Scans memory for weapon damage values  
echo  ✓ Modifies pistol damage to 999 (flamethrower power!)
echo  ✓ Modifies melee damage to 1500 (lightning sword power!)
echo  ✓ Creates one-shot kill weapons
echo  ✓ No manual work required!
echo.
echo  📋 REQUIREMENTS:
echo  ✓ Space Marine 2 must be running
echo  ✓ You must be in a mission with enemies
echo  ✓ Run this as Administrator for best results
echo.

REM Check if running as admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo  ⚠️  WARNING: Not running as Administrator
    echo     Some modifications may fail
    echo.
)

echo  🚀 READY TO START?
echo     Press any key to begin automatic weapon modification...
echo.
pause >nul

echo.
echo  🔍 Step 1: Checking for Space Marine 2...
tasklist /FI "IMAGENAME eq *Space*" /FO CSV | find /I "Space" >nul
if %errorLevel% equ 0 (
    echo  ✅ Space Marine 2 detected!
) else (
    echo  ❌ Space Marine 2 not found!
    echo     Please launch the game first, then run this again.
    pause
    exit /b 1
)

echo.
echo  🧠 Step 2: Starting smart memory scanner...
echo     This may take 30-60 seconds...
echo.

REM Try the smart scanner first
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -Command "& '%~dp0SmartWeaponMod.ps1'" 2>nul
if %errorLevel% equ 0 (
    echo  ✅ Smart scanner completed successfully!
    goto :success
)

echo  ⚠️  Smart scanner had issues, trying basic scanner...
echo.

REM Fallback to basic scanner
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -Command "& '%~dp0AutoWeaponMod.ps1' -AutoRun" 2>nul
if %errorLevel% equ 0 (
    echo  ✅ Basic scanner completed successfully!
    goto :success
)

echo  ❌ Both scanners failed. Possible issues:
echo     - Game not running
echo     - Anti-virus blocking
echo     - Need Administrator privileges
echo     - Game has strong protection
echo.
echo  💡 MANUAL SOLUTION:
echo     If automatic methods fail, you can use Cheat Engine:
echo     1. Download from cheatengine.org
echo     2. Attach to Space Marine 2 process
echo     3. Search for pistol damage (25-35)
echo     4. Change to 999
echo     5. Search for melee damage (60-120)  
echo     6. Change to 1500
echo.
goto :end

:success
echo.
echo  🎉 MODIFICATION COMPLETE!
echo  ========================
echo.
echo  🔥 YOUR WEAPONS ARE NOW OVERPOWERED! 🔥
echo.
echo  🔫 Pistol: 999 damage flamethrower
echo     - One-shot kills most enemies
echo     - Massive damage numbers
echo     - Area damage effects
echo.
echo  ⚔️  Melee: 1500 damage lightning sword
echo     - Instant kills on most enemies
echo     - Devastating melee attacks
echo     - Chain lightning effects
echo.
echo  🎮 GO TEST YOUR WEAPONS!
echo     Fire at enemies and watch the carnage!
echo.

:end
echo  Press any key to exit...
pause >nul
