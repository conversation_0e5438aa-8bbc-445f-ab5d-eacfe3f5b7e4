__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __where   =   {
                     __parameters   =   {
                        name   =   {
                           __value   =   "^.+$"
                           __type   =   "string_param"
                        }
                     }
                  }
                  __path   =   "<database_dir>/*/{arg}.tpl/obj_list.json"
                  __selectOnlyLeafNodes   =   False
                  __root   =   "engine_objects"
                  __type   =   "jsonLink"
               }
               __format   =   "{value}"
               __showValue   =   True
               __tag   =   "templateName"
               __type   =   "modelTag"
            }
            __format   =   "{value}"
            __showValue   =   True
            __path   =   "desc/properties/geom/nameTpl"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __type   =   "modelRecursive"
      }
      __showOnlyChildren   =   True
      __path   =   "spawnableActors/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __modelType   =   "^xc_level_sequence$"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

