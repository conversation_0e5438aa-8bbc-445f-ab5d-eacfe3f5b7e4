// Space Marine 2 - Auto-Equip Flamethrower on Spawn
// This file modifies the default player loadout to include the flamethrower

// Override default heavy weapon class loadout
WeaponLoadoutTemplatesLibrary = {
    templates = {
        // Heavy class gets flamethrower by default
        heavy_default = {
            uid = "heavy_default"
            name = "Heavy Flamethrower"
            class = "heavy"
            
            weapons = {
                primary = {
                    weaponUid = "flamethrower_heavy"
                    ammo = 200
                    condition = 1.0
                    modifications = []
                }
                
                secondary = {
                    weaponUid = "bolt_pistol"
                    ammo = 60
                    condition = 1.0
                    modifications = []
                }
                
                melee = {
                    weaponUid = "chainsword"
                    condition = 1.0
                    modifications = []
                }
                
                throwable = {
                    weaponUid = "frag_grenade"
                    count = 2
                }
            }
            
            equipment = {
                armor = "heavy_armor"
                helmet = "heavy_helmet"
                backpack = "fuel_tank"
            }
            
            perks = [
                "heavy_weapon_specialist",
                "fire_resistance",
                "area_damage_boost"
            ]
            
            __type = "WeaponLoadoutTemplate"
        }
        
        // Also add flamethrower option to assault class for testing
        assault_flamethrower = {
            uid = "assault_flamethrower"
            name = "Assault Flamethrower"
            class = "assault"
            
            weapons = {
                primary = {
                    weaponUid = "flamethrower_heavy"
                    ammo = 200
                    condition = 1.0
                    modifications = []
                }
                
                secondary = {
                    weaponUid = "bolt_pistol"
                    ammo = 60
                    condition = 1.0
                    modifications = []
                }
                
                melee = {
                    weaponUid = "chainsword"
                    condition = 1.0
                    modifications = []
                }
                
                throwable = {
                    weaponUid = "frag_grenade"
                    count = 2
                }
            }
            
            equipment = {
                armor = "assault_armor"
                helmet = "assault_helmet"
                backpack = "fuel_tank"
            }
            
            perks = [
                "mobility_boost",
                "fire_resistance",
                "area_damage_boost"
            ]
            
            __type = "WeaponLoadoutTemplate"
        }
        
        // Tactical class with flamethrower option
        tactical_flamethrower = {
            uid = "tactical_flamethrower"
            name = "Tactical Flamethrower"
            class = "tactical"
            
            weapons = {
                primary = {
                    weaponUid = "flamethrower_heavy"
                    ammo = 200
                    condition = 1.0
                    modifications = []
                }
                
                secondary = {
                    weaponUid = "bolt_pistol"
                    ammo = 60
                    condition = 1.0
                    modifications = []
                }
                
                melee = {
                    weaponUid = "chainsword"
                    condition = 1.0
                    modifications = []
                }
                
                throwable = {
                    weaponUid = "frag_grenade"
                    count = 2
                }
            }
            
            equipment = {
                armor = "tactical_armor"
                helmet = "tactical_helmet"
                backpack = "fuel_tank"
            }
            
            perks = [
                "tactical_awareness",
                "fire_resistance",
                "area_damage_boost"
            ]
            
            __type = "WeaponLoadoutTemplate"
        }
    }
    
    __type = "WeaponLoadoutTemplatesLibrary"
}

// Override player spawn equipment
PlayerSpawnEquipment = {
    defaultLoadouts = {
        heavy = {
            primary = "flamethrower_heavy"
            secondary = "bolt_pistol"
            melee = "chainsword"
            throwable = "frag_grenade"
            
            ammo = {
                primary = 200
                secondary = 60
                throwable = 2
            }
        }
        
        assault = {
            primary = "flamethrower_heavy"  // Give flamethrower to assault too
            secondary = "bolt_pistol"
            melee = "chainsword"
            throwable = "frag_grenade"
            
            ammo = {
                primary = 200
                secondary = 60
                throwable = 2
            }
        }
        
        tactical = {
            primary = "flamethrower_heavy"  // Give flamethrower to tactical too
            secondary = "bolt_pistol"
            melee = "chainsword"
            throwable = "frag_grenade"
            
            ammo = {
                primary = 200
                secondary = 60
                throwable = 2
            }
        }
    }
    
    __type = "PlayerSpawnEquipment"
}

// Force flamethrower in weapon selection
WeaponSelectionOverride = {
    forceWeapons = {
        heavy_weapons = [
            "flamethrower_heavy"
        ]
        
        primary_weapons = [
            "flamethrower_heavy"
        ]
    }
    
    // Make flamethrower available in all weapon categories
    weaponCategories = {
        heavy = [
            "flamethrower_heavy"
        ]
        
        special = [
            "flamethrower_heavy"
        ]
        
        primary = [
            "flamethrower_heavy"
        ]
    }
    
    __type = "WeaponSelectionOverride"
}
