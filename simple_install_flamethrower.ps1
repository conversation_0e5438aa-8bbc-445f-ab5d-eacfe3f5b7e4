# Space Marine 2 - Simple Flamethrower Installation
# This script installs the flamethrower mod

Write-Host "Installing Flamethrower Mod..." -ForegroundColor Green

# Paths
$SourceFile = "ModEditor\mods_source\ssl\weapons\heavy_bolter_flamethrower_mod.sso"
$DestDir = "client_pc\root\local\ssl\weapons"
$DestFile = "$DestDir\heavy_bolter_flamethrower_mod.sso"

# Check source file
if (-not (Test-Path $SourceFile)) {
    Write-Error "Source file not found: $SourceFile"
    exit 1
}

# Create destination directory
if (-not (Test-Path $DestDir)) {
    New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
    Write-Host "Created directory: $DestDir" -ForegroundColor Yellow
}

# Copy file
Copy-Item $SourceFile $DestFile -Force

# Verify
if (Test-Path $DestFile) {
    Write-Host "SUCCESS: Flamethrower mod installed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "How to test:" -ForegroundColor Cyan
    Write-Host "1. Launch Space Marine 2" -ForegroundColor White
    Write-Host "2. Start any mission" -ForegroundColor White
    Write-Host "3. Find Heavy Bolter weapon" -ForegroundColor White
    Write-Host "4. It now has flamethrower stats!" -ForegroundColor White
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "- High damage (120 per shot)" -ForegroundColor White
    Write-Host "- Short range (15m max)" -ForegroundColor White
    Write-Host "- Area damage (3m radius)" -ForegroundColor White
    Write-Host "- Burning effects" -ForegroundColor White
    Write-Host "- Fear effects" -ForegroundColor White
    Write-Host ""
    Write-Host "Ready to burn heretics!" -ForegroundColor Red
} else {
    Write-Error "Installation failed!"
    exit 1
}
