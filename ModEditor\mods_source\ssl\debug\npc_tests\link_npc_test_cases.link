__request   =   {
   __select   =   {
      __select   =   {
         __id   =   "test"
         __where   =   {
            __type   =   "NpcTestCaseBehaviour"
         }
         __type   =   "modelRecursive"
      }
      __where   =   {
         __type   =   "NpcTestCasesCategoryArchetype"
      }
      __type   =   "modelRecursive"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "NpcTestCases*"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{test}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

