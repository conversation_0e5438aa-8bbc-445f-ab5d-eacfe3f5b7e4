lodInfo = {
   maxLodDist = [

   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      zone_edle1_blue_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone_edle2_blue_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone_edle1_red_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone_edle2_red_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone_edle1_grey_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone_edle2_grey_square = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
