steps:
  - SDR_PRESETS
  - MATERIAL_TEMPLATES
  - TD_DEFAULTS
  - TD
  - PCT
  - ADAPTER_LOADING
  - MTL_MUTATORS
  - TPL_MARKUP_DESCS
  - LOAD
  - TPL_MARKUP
  - EXCLUDE

functions:
  - name: adapter_loading
    on:
      - step: ADAPTER_LOADING      

rules:
  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: SDR_PRESETS

  - name: load material_templates
    res_type: res_desc_material_templates
    step: MATERIAL_TEMPLATES

  - name: load td_defaults
    res_type: res_desc_td_defaults
    step: TD_DEFAULTS

  - name: load td
    res_type: res_desc_td
    step: TD

  - name: load pct
    res_type: res_desc_pct
    on:
      - mask: 'res://pct/*'
    step: PCT

  - name: load pct
    res_type: res_desc_pct
    on:
      - mask: 'res://tpl/*'
    step: PCT

  - name: load mtl_mutators
    res_type: res_desc_mtl_mut
    step: MTL_MUTATORS

  - name: load tpl markup related descs
    res_type: res_basic_ssl_desc
    step: TPL_MARKUP_DESCS

  - name: load tpl markup descs
    res_type: res_desc_tpl_markup
    step: TPL_MARKUP

  - name: load tpl
    res_type: res_desc_tpl
    step: LOAD

  - name: load tpl assets
    res_type: res_desc_tpl_asset
    step: LOAD

  - name: exclude all other
    exclude: true