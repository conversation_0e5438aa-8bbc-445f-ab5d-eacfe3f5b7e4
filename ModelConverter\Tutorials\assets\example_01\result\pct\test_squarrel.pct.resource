__type: res_desc_pct
downsampled: false
header:
  faceSize: 1398096
  format: 51
  mipLevel:
  - offset: 0
    size: 1048576
  - offset: 1048576
    size: 262144
  - offset: 1310720
    size: 65536
  - offset: 1376256
    size: 16384
  - offset: 1392640
    size: 4096
  - offset: 1396736
    size: 1024
  - offset: 1397760
    size: 256
  - offset: 1398016
    size: 64
  - offset: 1398080
    size: 16
  nFaces: 1
  nMipMap: 9
  sign: 1346978644
  size: 1398096
  sx: 1024
  sy: 1024
  sz: 1
imageOffset: 136
isCopyDst: false
isImposter: false
isLightmap: false
linkTd: res://td/test_squarrel.td.resource
mipMaps: []
pct: test_squarrel.pct
predownsampleMipsCount: 0
source: D:\tools_test\UsfExporter\test_squarrel.tga
texName: test_squarrel
texType: ''
tileLayout: 0
tiles: []
useHeaderFromResource: true
