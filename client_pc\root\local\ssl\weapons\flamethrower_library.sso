// Space Marine 2 - Flamethrower Weapon Library Integration
// This file adds our flamethrower to the main weapon library

FirearmLibraryPve = {
    firearms = {
        // Add our flamethrower to the existing firearms list
        flamethrower_heavy = {
            uid = "flamethrower_heavy"
            name = "Heavy Flamethrower"
            category = "heavy"
            
            // Basic weapon properties
            weaponType = "heavy_weapon"
            damageType = "fire"
            fireMode = "continuous"
            
            // Damage settings
            damage = {
                base = 45.0
                armorPenetration = 0.3
                criticalMultiplier = 1.5
                falloffStart = 10.0
                falloffEnd = 20.0
            }
            
            // Fire rate and mechanics
            fireRate = {
                roundsPerMinute = 1200    // Very high "rounds" per minute for continuous fire
                burstLength = -1          // Infinite burst (continuous)
                spinUpTime = 0.5          // Half second spin-up
                cooldownTime = 2.0        // 2 second cooldown
            }
            
            // Ammunition
            ammo = {
                maxAmmo = 200
                clipSize = 200
                reloadTime = 4.0
                ammoPerShot = 2
                ammoType = "fuel"
            }
            
            // Range characteristics
            range = {
                effective = 15.0
                maximum = 20.0
                optimal = 8.0
            }
            
            // Accuracy and handling
            accuracy = {
                hipFire = 0.9             // Very accurate at close range
                aimDownSight = 0.95       // Slightly better when aiming
                movementPenalty = 0.3     // 30% accuracy loss when moving
                recoil = {
                    vertical = 0.2
                    horizontal = 0.1
                    pattern = "minimal"    // Very little recoil
                }
            }
            
            // Weapon handling
            handling = {
                weight = 8.5
                aimDownSightTime = 1.2
                movementSpeedMultiplier = 0.7
                swapTime = 2.5
            }
            
            // Projectile configuration
            projectile = {
                type = "flame_stream"
                speed = 25.0
                gravity = 0.5
                lifetime = 0.8
                penetration = 0
                areaOfEffect = 3.0
                maxTargets = 8
            }
            
            // Special mechanics
            specialMechanics = {
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 1.5
                    coolingRate = 15.0
                    overheatPenalty = 3.0
                }
                
                continuousFire = {
                    enabled = true
                    damagePerTick = 45.0
                    tickRate = 0.05          // 20 ticks per second
                    fuelConsumption = 2.0    // Fuel per tick
                }
            }
            
            // Status effects
            statusEffects = {
                burning = {
                    enabled = true
                    applyChance = 0.9
                    duration = 5.0
                    damagePerSecond = 10.0
                    stackable = true
                    maxStacks = 3
                }
                
                fear = {
                    enabled = true
                    applyChance = 0.3
                    duration = 2.0
                    affectedTypes = ["basic_enemy", "cultist"]
                }
            }
            
            // Visual effects
            effects = {
                muzzleFlash = "sfx_flamethrower_muzzle"
                projectileTrail = "sfx_flame_stream"
                impactEffect = "sfx_flame_impact"
                scorchMark = "scr_flamethrower"
                
                flameVisuals = {
                    color = [255, 120, 0]     // Orange flame
                    intensity = 2.5
                    width = 1.2
                    length = 15.0
                    particleCount = 100
                }
            }
            
            // Audio effects
            audio = {
                fireSound = "wpn_flamethrower_fire"
                reloadSound = "wpn_flamethrower_reload"
                emptySound = "wpn_flamethrower_empty"
                spinUpSound = "wpn_flamethrower_spinup"
                cooldownSound = "wpn_flamethrower_cooldown"
                
                loopingFire = true
                fadeInTime = 0.2
                fadeOutTime = 0.5
            }
            
            // Environmental interactions
            environmental = {
                igniteObjects = true
                clearVegetation = true
                meltIce = true
                spreadFire = true
                
                igniteChance = 0.8
                spreadRadius = 2.0
                burnDuration = 10.0
            }
            
            // UI and progression
            ui = {
                icon = "ui_flamethrower_icon"
                description = "Heavy flamethrower that deals continuous fire damage and applies burning effects to enemies."
                unlockLevel = 15
                weaponClass = "Heavy"
                
                masteryProgression = {
                    tier1 = {
                        name = "Extended Range"
                        description = "Increases effective range by 25%"
                        effect = "range_increase"
                        value = 1.25
                    }
                    tier2 = {
                        name = "Fuel Efficiency"
                        description = "Reduces fuel consumption by 20%"
                        effect = "ammo_efficiency"
                        value = 0.8
                    }
                    tier3 = {
                        name = "Enhanced Burning"
                        description = "Burning effects last 50% longer"
                        effect = "status_duration"
                        value = 1.5
                    }
                    tier4 = {
                        name = "Rapid Cooling"
                        description = "Faster heat dissipation"
                        effect = "cooling_rate"
                        value = 1.5
                    }
                    tier5 = {
                        name = "Inferno Blast"
                        description = "Overheating triggers explosive area damage"
                        effect = "overheat_explosion"
                        value = 150.0
                    }
                }
            }
            
            // Weapon variants/skins
            variants = {
                default = {
                    name = "Standard"
                    model = "wpn_flamethrower_01"
                    texture = "wpn_flamethrower_01_diff"
                }
                
                veteran = {
                    name = "Veteran Pattern"
                    model = "wpn_flamethrower_01"
                    texture = "wpn_flamethrower_01_veteran_diff"
                    unlockLevel = 25
                }
                
                relic = {
                    name = "Relic Flamer"
                    model = "wpn_flamethrower_01"
                    texture = "wpn_flamethrower_01_relic_diff"
                    unlockLevel = 40
                    bonuses = {
                        damage = 1.1
                        range = 1.15
                    }
                }
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
