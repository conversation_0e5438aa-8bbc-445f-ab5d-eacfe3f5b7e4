glt {
   __caption = "glt"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   no_backface_culling {
      __type = "bool"
   }
   
   doubleSidedLighting {
      __type = "enum"
      __list = ["none", "normal", "relativeLocY", "relativeView_HZD"]
   }
   
   backFaceSpecMult {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   reflectance {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   porosity {
      __type = "slider"
      __min = 0.000000
      __max = 1.000000
      __step = 0.010000
   }
   
   no_ssao {
      __type = "bool"
   }
   
   z_func_less {
      __type = "bool"
   }
   
   blendMode {
      __type = "enum"
      __list = ["none", "blend", "add", "add_no_alpha"]
   }
   
   transpWriteDepth {
      __type = "bool"
   }
   
   blurShadows {
      __type = "enum"
      __list = ["none", "low", "high"]
   }
   
   allowHashedTransparent {
      __type = "bool"
   }
   
   autoTextureScaling {
      __type = "bool"
   }
   
   fresnelHighlight {
      __type = "section"
      power {
         __type = float
      }
      
      intensity {
         __type = float
      }
      
      tint {
         __type = "color"
      }
      
   }
   
   albedo_tint {
      __type = "color"
   }
   
   metallicTint {
      __type = "color"
   }
   
   colorization {
      __type = "section"
      use {
         __type = "bool"
      }
      
      useTranslucencyAsMask {
         __type = "bool"
      }
      
      color {
         __type = "color"
      }
      
      amount {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.050000
      }
      
   }
   
   fuzzy {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      coreDarkness {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.050000
      }
      
      power {
         __type = float
         __min = 0.000000
      }
      
      edgeBrightness {
         __type = float
         __min = 0.000000
      }
      
      edgeDesat {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.050000
      }
      
      edgeColor {
         __type = "color"
      }
      
   }
   
   metalness {
      __type = "section"
      scale {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      bias {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
   }
   
   second_spec {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      metallicTint {
         __type = "color"
      }
      
      metalness {
         __type = "section"
         scale {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         bias {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
      }
      
      roughness {
         __type = "section"
         scale {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         bias {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
      }
      
   }
   
   chameleon {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      color0 {
         __type = "color"
      }
      
      color1 {
         __type = "color"
      }
      
      color2 {
         __type = "color"
      }
      
      color3 {
         __type = "color"
      }
      
      offset1 {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      offset2 {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      fresnelPwr {
         __type = float
      }
      
   }
   
   anisotropic_spec {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      angle {
         __type = float
      }
      
      dirMap = {
         __type = "texture"
         __callback = ""
      }
      
      anisotropy {
         __type = float
      }
      
      intensity {
         __type = float
      }
      
      extra {
         __type = "section"
         anisotropy {
            __type = float
         }
         
         intensity {
            __type = float
         }
         
         shift {
            __type = float
         }
         
      }
      
   }
   
   roughness {
      __type = "section"
      scale {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      bias {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
   }
   
   emissive {
      __type = "section"
      tint {
         __type = "color"
      }
      
      intensity {
         __type = float
      }
      
      adaptiveIntensity {
         __type = "bool"
      }
      
      bloomIntensity {
         __type = float
      }
      
      animation {
         __type = "section"
         gradient {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = ""
            }
            
            speed {
               __type = float
            }
            
         }
         
         blink {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = ""
            }
            
            speed {
               __type = float
            }
            
            texPhaseOffset = {
               __type = "texture"
               __callback = ""
            }
            
         }
         
      }
      
   }
   
   detail {
      __type = "section"
      density {
         __type = float
      }
      
      scale {
         __type = float
      }
      
      useWorldScale {
         __type = "bool"
      }
      
   }
   
   detailDiffuse {
      __type = "section"
      density {
         __type = float
      }
      
      scale {
         __type = float
      }
      
      useDetailMask {
         __type = "bool"
      }
      
      useWorldScale {
         __type = "bool"
      }
      
   }
   
   occlusionMask {
      __type = "section"
      vertex_scale {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      tex_scale {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
   }
   
   reflection {
      __type = "section"
      disableSSR {
         __type = "bool"
      }
      
   }
   
   refraction {
      __type = "section"
      blendMode {
         __type = "enum"
         __list = ["blend", "add", "glass"]
      }
      
   }
   
   terr_blend {
      __type = "section"
      disable_terr_soft_blend {
         __type = "bool"
      }
      
      blend_by_mask {
         __type = "section"
         mask = {
            __type = "texture"
            __callback = ""
         }
         
         no_save_local_height {
            __type = "bool"
         }
         
      }
      
      apply_colorize {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         rand_colorize {
            __type = "bool"
         }
         
         gradient_colorize {
            __type = "bool"
         }
         
         power {
            __type = float
         }
         
      }
      
      distance_blend {
         __type = "section"
         normals_spec_by_dist {
            __type = "bool"
         }
         
         start_dist {
            __type = float
         }
         
         power {
            __type = float
            __min = 0.000000
            __max = 1.000000
         }
         
      }
      
   }
   
   vertex_animation {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      animType {
         __type = "enum"
         __list = ["vegetation", "cloth", "wind_vegetation", "pulsation"]
      }
      
      cloth {
         __type = "section"
         synchronizeWithGlobalWind {
            __type = "bool"
         }
         
         synchronizedWithWindSettings {
            __type = "section"
            isConstrained {
               __type = "bool"
            }
            
            waveSpeedMultiplier {
               __type = float
            }
            
            globalWindScale {
               __type = float
            }
            
            maxWindAmplitude {
               __type = float
            }
            
            dynamicWindNoiseFreq {
               __type = float
            }
            
            dynamicWindNoiseDistScale {
               __type = float
            }
            
            maxAnimationOffset {
               __type = float
            }
            
            dynamicWindScale {
               __type = float
            }
            
         }
         
         linearWave {
            __type = "section"
            wave1 {
               __type = "section"
               freq {
                  __type = float
               }
               
               weight {
                  __type = "slider"
                  __min = 0.000000
                  __max = 1.000000
                  __step = 0.001000
               }
               
            }
            
            wave2 {
               __type = "section"
               freq {
                  __type = float
               }
               
               weight {
                  __type = "slider"
                  __min = 0.000000
                  __max = 1.000000
                  __step = 0.001000
               }
               
            }
            
            wave3 {
               __type = "section"
               freq {
                  __type = float
               }
               
               weight {
                  __type = "slider"
                  __min = 0.000000
                  __max = 1.000000
                  __step = 0.001000
               }
               
            }
            
            direction {
               __type = "section"
               X {
                  __type = float
               }
               
               Y {
                  __type = float
               }
               
               Z {
                  __type = float
               }
               
            }
            
            coneAngle {
               __type = float
            }
            
            deviationFreq {
               __type = float
            }
            
            amplitude {
               __type = float
            }
            
            freq {
               __type = float
            }
            
         }
         
         radialWave {
            __type = "section"
            amplitude {
               __type = float
            }
            
            freq {
               __type = float
            }
            
         }
         
         wind {
            __type = "section"
            useLCS {
               __type = "bool"
            }
            
            direction {
               __type = "section"
               X {
                  __type = float
               }
               
               Y {
                  __type = float
               }
               
               Z {
                  __type = float
               }
               
            }
            
            gusts {
               __type = "section"
               minFreq {
                  __type = float
               }
               
               maxFreq {
                  __type = float
               }
               
               calmValue {
                  __type = float
               }
               
            }
            
         }
         
      }
      
      vegetation {
         __type = "section"
         synchronizeWithGlobalWind {
            __type = "bool"
         }
         
         synchronizedWithWindSettings {
            __type = "section"
            globalWindScale {
               __type = float
            }
            
            dynamicWindScale {
               __type = float
            }
            
            maxWindAmplitude {
               __type = float
            }
            
            leafsSway {
               __type = "section"
               X {
                  __type = float
               }
               
               Y {
                  __type = float
               }
               
               Z {
                  __type = float
               }
               
            }
            
            leafsSwayFreq {
               __type = float
            }
            
            leafsSwayDetailAmp {
               __type = float
            }
            
            leafsSwayDetailFreq {
               __type = float
            }
            
         }
         
         bending {
            __type = "section"
            centerOffset {
               __type = "section"
               X {
                  __type = float
               }
               
               Y {
                  __type = float
               }
               
               Z {
                  __type = float
               }
               
            }
            
            amplitude {
               __type = float
            }
            
            speed {
               __type = float
            }
            
         }
         
         noise {
            __type = "section"
            amplitude {
               __type = float
            }
            
            speed {
               __type = float
            }
            
         }
         
      }
      
      wind_vegetation {
         __type = "section"
         vegetationType {
            __type = "enum"
            __list = ["leafs", "trunk", "grass"]
         }
         
         isHierarchy {
            __type = "bool"
         }
         
      }
      
      pulsation {
         __type = "section"
         bending {
            __type = "section"
            amplitude {
               __type = float
            }
            
            speed {
               __type = float
            }
            
         }
         
         noise {
            __type = "section"
            amplitude {
               __type = float
            }
            
            speed {
               __type = float
            }
            
         }
         
      }
      
   }
   
   parallax {
      __type = "section"
      scale {
         __type = float
      }
      
      shadow {
         __type = "bool"
      }
      
   }
   
   sparkles {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      tiling {
         __type = float
      }
      
      noiseTiling {
         __type = float
      }
      
      size {
         __type = float
      }
      
      intensityScale {
         __type = float
      }
      
      viewSlide {
         __type = float
      }
      
   }
   
   subsurface {
      __type = "section"
      translucency {
         __type = "section"
         mode {
            __type = "enum"
            __list = ["none", "simple"]
         }
         
         simple {
            __type = "section"
            applyBackLightTint {
               __type = "bool"
            }
            
            backLightTint {
               __type = "color"
            }
            
         }
         
         intensity {
            __type = float
         }
         
         opacityMask = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
      scattering {
         __type = "section"
         profile {
            __type = "enum"
            __list = ["disabled", "preset_1", "preset_2", "preset_3", "preset_4", "preset_5", "preset_6", "preset_7", "preset_8", "preset_9", "preset_10", "preset_11", "preset_12", "preset_13", "preset_14", "preset_15", "preset_16", "preset_17", "preset_18", "preset_19", "preset_20", "preset_21", "preset_22", "preset_23", "preset_24", "preset_25", "preset_26", "preset_27", "preset_28", "preset_29", "preset_30", "preset_31"]
         }
         
         useDiffAlphaMask {
            __type = "bool"
         }
         
      }
      
   }
   
   skin {
      __type = "section"
      wrinkles {
         __type = "section"
         enable {
            __type = "bool"
         }
         
         texArrayNormals = {
            __type = "texture"
            __callback = "nm"
         }
         
         texArrayMasks = {
            __type = "texture"
            __callback = ""
         }
         
      }
      
   }
   
   nmScale {
      __type = "spin"
      __sensivity = 0.500000
      __min = 0.000000
      __max = 8.000000
      __step = 0.100000
   }
   
   perturbation {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      first {
         __type = "section"
         NM = {
            __type = "texture"
            __callback = "det"
         }
         
         scale {
            __type = float
         }
         
         time_scale {
            __type = float
         }
         
         wave_len {
            __type = float
         }
         
      }
      
      second {
         __type = "section"
         NM = {
            __type = "texture"
            __callback = "det"
         }
         
         scale {
            __type = float
         }
         
         time_scale {
            __type = float
         }
         
         wave_len {
            __type = float
         }
         
      }
      
   }
   
   tex {
      __type = "section"
      NM = {
         __type = "texture"
         __callback = "nm"
      }
      
      spec = {
         __type = "texture"
         __callback = "spec"
      }
      
      det = {
         __type = "texture"
         __callback = "det"
      }
      
      refCube = {
         __type = "texture"
         __callback = "cube"
      }
      
      heightDetMask = {
         __type = "texture"
         __callback = "hdetm"
      }
      
      emissiveTex = {
         __type = "texture"
         __callback = "em"
      }
      
      detailDiffuse = {
         __type = "texture"
         __callback = ""
      }
      
      diffOverride = {
         __type = "texture"
         __callback = ""
      }
      
      layerOverride = {
         __type = "texture"
         __callback = ""
      }
      
      dirt0Override = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   fakeLight {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      intensity {
         __type = float
         __min = 0.000000
      }
      
   }
   
   tintByMask {
      __type = "section"
      maskFromAlbedoAlpha {
         __type = "bool"
      }
      
      tintMode {
         __type = "enum"
         __list = ["multiply", "linear_blend"]
      }
      
      albedo {
         __type = "color"
      }
      
      metallness {
         __type = "color"
      }
      
      carpaintMetallness {
         __type = "color"
      }
      
      tintMask = {
         __type = "texture"
         __callback = ""
      }
      
      albedoG {
         __type = "color"
      }
      
      metallnessG {
         __type = "color"
      }
      
      albedoB {
         __type = "color"
      }
      
      metallnessB {
         __type = "color"
      }
      
      albedoA {
         __type = "color"
      }
      
      metallnessA {
         __type = "color"
      }
      
   }
   
   combiner {
      __type = "enum"
      __list = ["default", "glass", "eyes", "eyes2", "perturbation"]
   }
   
   glass {
      __type = "section"
      filter {
         __type = "color"
      }
      
      density {
         __type = float
      }
      
   }
   
   customization {
      __type = "section"
      metalnessFromAlbedo {
         __type = "bool"
      }
      
      mask = {
         __type = "texture"
         __callback = ""
      }
      
      bitmask = {
         __type = "texture"
         __callback = ""
      }
      
      texArrayDiffuse = {
         __type = "texture"
         __callback = ""
      }
      
      texArrayNormals = {
         __type = "texture"
         __callback = "nm"
      }
      
      texArraySpec = {
         __type = "texture"
         __callback = "spec"
      }
      
      texArrayEmissiveMask = {
         __type = "texture"
         __callback = "em"
      }
      
      rotation_map = {
         __type = "texture"
         __callback = "dir"
      }
      
      layer0 {
         __type = "section"
         tintBlendMode {
            __type = "enum"
            __list = ["multiply", "linear_blend"]
         }
         
         enabled {
            __type = "bool"
         }
         
         density {
            __type = float
         }
         
         useCustomUVSet {
            __type = "bool"
         }
         
         blendMode {
            __type = "enum"
            __list = ["tint_only", "tex_hardlight", "tex_blend"]
         }
         
      }
      
      layer1 {
         __type = "section"
         tintBlendMode {
            __type = "enum"
            __list = ["multiply", "linear_blend"]
         }
         
         enabled {
            __type = "bool"
         }
         
         density {
            __type = float
         }
         
         useCustomUVSet {
            __type = "bool"
         }
         
         blendMode {
            __type = "enum"
            __list = ["tint_only", "tex_hardlight", "tex_blend"]
         }
         
      }
      
      layer2 {
         __type = "section"
         tintBlendMode {
            __type = "enum"
            __list = ["multiply", "linear_blend"]
         }
         
         enabled {
            __type = "bool"
         }
         
         density {
            __type = float
         }
         
         useCustomUVSet {
            __type = "bool"
         }
         
         blendMode {
            __type = "enum"
            __list = ["tint_only", "tex_hardlight", "tex_blend"]
         }
         
      }
      
      layer3 {
         __type = "section"
         tintBlendMode {
            __type = "enum"
            __list = ["multiply", "linear_blend"]
         }
         
         enabled {
            __type = "bool"
         }
         
         density {
            __type = float
         }
         
         useCustomUVSet {
            __type = "bool"
         }
         
         blendMode {
            __type = "enum"
            __list = ["tint_only", "tex_hardlight", "tex_blend"]
         }
         
      }
      
   }
   
   eyes {
      __type = "section"
      roughness {
         __type = "section"
         scale {
            __type = float
         }
         
         bias {
            __type = float
         }
         
      }
      
      x {
         __type = float
      }
      
      y {
         __type = float
      }
      
      nmScale {
         __type = "spin"
         __sensivity = 0.500000
         __min = 0.000000
         __max = 8.000000
         __step = 0.100000
      }
      
   }
   
   eyes2 {
      __type = "section"
      irisUVRadius {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      limbusUVWidth {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      anteriorChamberDepth {
         __type = float
      }
      
      lightRefraction {
         __type = "section"
         irisConcavityPower {
            __type = float
         }
         
         irisConcavityScale {
            __type = float
         }
         
      }
      
      limbus {
         __type = "section"
         refractionScale {
            __type = float
         }
         
         strength {
            __type = float
         }
         
      }
      
      normals {
         __type = "section"
         scleraFlattenNormal {
            __type = float
            __min = 0.000000
            __max = 1.000000
         }
         
         irisFlattenNormal {
            __type = float
            __min = 0.000000
            __max = 1.000000
         }
         
      }
      
   }
   
   dissolvable {
      __type = "section"
      enable {
         __type = "bool"
      }
      
      invert {
         __type = "bool"
      }
      
      noiseTexture = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   covering {
      __type = "section"
      forceDisable {
         __type = "bool"
      }
      
      coveringMask = {
         __type = "texture"
         __callback = "hdetm"
      }
      
   }
   
   temporalAA {
      __type = "section"
      affectTransparencyMask {
         __type = "bool"
      }
      
   }
   
}