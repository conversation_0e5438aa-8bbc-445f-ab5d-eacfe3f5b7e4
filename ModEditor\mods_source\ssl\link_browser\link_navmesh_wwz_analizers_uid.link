__request   =   {
   __select   =   {
      __select   =   {
         __onlyChanged   =   False
         __where   =   {
            __type   =   "^hk_ai_NAVMESH_(VAULT|CLIMB_UP|DROP_DOWN|DUCK_UNDER)_ANALIZER_SETTINGS_DESC$"
         }
         __type   =   "modelRecursive"
      }
      __path   =   "settings/analizers/*"
      __where   =   {
         __name   =   "^.+Analizers$"
      }
      __type   =   "model"
   }
   __family   =   ".cls"
   __where   =   {
      __name   =   "^navmesh_wwz"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

