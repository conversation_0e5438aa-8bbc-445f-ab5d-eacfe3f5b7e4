__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __path   =   "<database_dir>/*/{arg}.tpl/anim_list.json|<tpl_assets_dir>/*/{arg}.tpl.asset/tpl/anim_list.json"
            __type   =   "jsonLink"
         }
         __path   =   "nameTpl"
         __type   =   "model"
      }
      __where   =   {
         __parentType   =   "geom"
      }
      __path   =   "properties/*"
      __type   =   "model"
   }
   __modelType   =   "iactor"
   __modelFamily   =   ".cls"
   __type   =   "modelParent"
}
__addEmpty   =   {
   __value   =   True
}
__type   =   "linkBrowser"

