steps:
  - RES3_SCENE_SOUND_BANKS
  - RES3_SCENE_SSL
  - RES3_SCENE_SSL_DESC
  - RES3_SCENE_SSL_DESC_FINALIZE
  - RES3_SCENE_MML_CFG
  - RES3_SCENE_SDR_PRESETS
  - RES3_SCENE_ADAPTER_LOADING
  - RES3_SCENE_MTL_MUTATORS
  - RES3_SCENE_TPL
  - RES3_SCENE_TPL_MARKUP
  - RES3_SCENE_SOUND_EVENTS
  - RES3_SCENE_ALL
  

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_SCENE_SSL 
  - name: finalize_sound_events_loading
    on:
      - step: RES3_SCENE_SOUND_EVENTS
  - name: finalize_ssl_descs_loading
    on:
      - step: RES3_SCENE_SSL_DESC_FINALIZE      
  - name: adapter_loading
    on:
      - step: RES3_SCENE_ADAPTER_LOADING      

rules:
  - name: load scene sound banks
    res_type: res_desc_sound_bank
    step: RES3_SCENE_SOUND_BANKS

  #
  # ssl descs
  #
  - name: process scene ssl
    res_type: res_desc_ssl
    step: RES3_SCENE_SSL

  - name: process scene ssolib
    res_type: res_desc_ssolib
    step: RES3_SCENE_SSL_DESC

  - name: process scene sso
    res_type: res_desc_sso
    step: RES3_SCENE_SSL_DESC

  - name: process scene cls
    res_type: res_desc_cls
    step: RES3_SCENE_SSL_DESC

  - name: process scene prop
    res_type: res_desc_prop
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab
    res_type: res_prefab_desc
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab asset
    res_type: res_desc_prefab_asset
    step: RES3_SCENE_SSL_DESC

  #
  # basic descs
  #
  - name: process scene basic descs
    res_type: res_basic_ssl_desc
    step: RES3_SCENE_SSL_DESC
 
  #
  # tpl descs
  #
  - name: process scene tpl descs
    res_type: res_desc_tpl
    step: RES3_SCENE_TPL
 
  #
  # tpl_markup descs
  #
  - name: process scene tpl_markup descs
    res_type: res_desc_tpl_markup
    step: RES3_SCENE_TPL_MARKUP

  # scene mml cfg
  - name: process scene mml cfg
    res_type: res_desc_mml_cfg
    step: RES3_SCENE_MML_CFG

  # steps RES3_SCENE_MATERIALS_TEMPLATES, RES3_SCENE_SDR_PRESETS, RES3_SCENE_TD_DEFAULTS
  # and RES3_SCENE_PCT later must be added to RES3_SCENE_ALL

  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: RES3_SCENE_SDR_PRESETS

  - name: process mtl_mutators
    res_type: res_desc_mtl_mut
    step: RES3_SCENE_MTL_MUTATORS

  # step RES3_SCENE_SOUND_EVENTS should be executed after all sound banks load
  # in case of async bank loading actual bank will be actually loaded with some delay after RES3_SCENE_SOUND_BANKS step

  - name: process sound media bundles
    res_type: res_desc_sound_media_bundle
    step: RES3_SCENE_SOUND_EVENTS

  - name: process sound events
    res_type: res_desc_sound_event
    step: RES3_SCENE_SOUND_EVENTS

  #
  # other
  #
  - name: load all other RES3_SCENE resources
    use_default_loader: true
    step: RES3_SCENE_ALL
