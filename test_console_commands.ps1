# Space Marine 2 - Console Command Testing
# This script helps test various console command approaches

Write-Host "Space Marine 2 Console Command Testing" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

Write-Host ""
Write-Host "TESTING METHODS:" -ForegroundColor Cyan
Write-Host ""

# Method 1: Launch parameters
Write-Host "Method 1: Steam Launch Parameters" -ForegroundColor Yellow
Write-Host "1. Right-click Space Marine 2 in Steam" -ForegroundColor White
Write-Host "2. Go to Properties > General > Launch Options" -ForegroundColor White
Write-Host "3. Try these launch parameters:" -ForegroundColor White
Write-Host "   -console" -ForegroundColor Gray
Write-Host "   -dev" -ForegroundColor Gray
Write-Host "   -devmode" -ForegroundColor Gray
Write-Host "   -debug" -ForegroundColor Gray
Write-Host "   -cheats" -ForegroundColor Gray
Write-Host "4. Launch game and try pressing:" -ForegroundColor White
Write-Host "   ~ (tilde key)" -ForegroundColor Gray
Write-Host "   F1, F2, F3" -ForegroundColor Gray
Write-Host "   Ctrl+~" -ForegroundColor Gray
Write-Host "   Alt+~" -ForegroundColor Gray
Write-Host ""

# Method 2: Config file modification
Write-Host "Method 2: Config File Modification" -ForegroundColor Yellow
Write-Host "Creating test config files..." -ForegroundColor White

# Create autoexec.cfg
$AutoexecContent = @"
// Space Marine 2 Auto-Execute Console Commands
// This file runs automatically if console is available

// Enable developer mode
developer 1
con_enable 1
sv_cheats 1

// Weapon modifications (if supported)
weapon_damage_multiplier 10.0
weapon_infinite_ammo 1
weapon_no_reload 1

// Player modifications
player_god_mode 1
player_infinite_health 1
player_speed_multiplier 2.0

// Debug commands
showfps 1
noclip 0

echo "Console commands loaded successfully!"
"@

$AutoexecContent | Out-File "autoexec.cfg" -Encoding UTF8
Write-Host "Created: autoexec.cfg" -ForegroundColor Green

# Create console.cfg
$ConsoleContent = @"
// Console configuration for Space Marine 2
bind "~" "toggleconsole"
bind "F1" "toggleconsole"
con_enable "1"
developer "1"
"@

$ConsoleContent | Out-File "console.cfg" -Encoding UTF8
Write-Host "Created: console.cfg" -ForegroundColor Green

# Create user.cfg
$UserContent = @"
// User configuration with cheats
seta developer "1"
seta sv_cheats "1"
seta con_enable "1"
seta weapon_damage "999"
seta infinite_ammo "1"
"@

$UserContent | Out-File "user.cfg" -Encoding UTF8
Write-Host "Created: user.cfg" -ForegroundColor Green

Write-Host ""
Write-Host "Method 3: In-Game Key Testing" -ForegroundColor Yellow
Write-Host "Launch the game and try these keys:" -ForegroundColor White
Write-Host "  ~ (tilde)" -ForegroundColor Gray
Write-Host "  ` (backtick)" -ForegroundColor Gray
Write-Host "  F1, F2, F3, F4" -ForegroundColor Gray
Write-Host "  Ctrl+Shift+C" -ForegroundColor Gray
Write-Host "  Ctrl+Alt+~" -ForegroundColor Gray
Write-Host "  Insert key" -ForegroundColor Gray
Write-Host "  Home key" -ForegroundColor Gray
Write-Host ""

Write-Host "Method 4: Common Console Commands to Try" -ForegroundColor Yellow
Write-Host "If console opens, try these commands:" -ForegroundColor White
Write-Host "  help" -ForegroundColor Gray
Write-Host "  cmdlist" -ForegroundColor Gray
Write-Host "  cvarlist" -ForegroundColor Gray
Write-Host "  god" -ForegroundColor Gray
Write-Host "  noclip" -ForegroundColor Gray
Write-Host "  give all" -ForegroundColor Gray
Write-Host "  weapon_damage 999" -ForegroundColor Gray
Write-Host "  infinite_ammo 1" -ForegroundColor Gray
Write-Host "  sv_cheats 1" -ForegroundColor Gray
Write-Host ""

Write-Host "TESTING INSTRUCTIONS:" -ForegroundColor Red
Write-Host "1. Try Method 1 first (launch parameters)" -ForegroundColor White
Write-Host "2. Copy the .cfg files to game directory" -ForegroundColor White
Write-Host "3. Launch game and test all key combinations" -ForegroundColor White
Write-Host "4. If console opens, try the commands listed" -ForegroundColor White
Write-Host "5. Report back what works!" -ForegroundColor White
Write-Host ""

# Copy config files to game directory
Write-Host "Copying config files to game directory..." -ForegroundColor Yellow
Copy-Item "autoexec.cfg" "client_pc\root\" -Force -ErrorAction SilentlyContinue
Copy-Item "console.cfg" "client_pc\root\" -Force -ErrorAction SilentlyContinue
Copy-Item "user.cfg" "client_pc\root\" -Force -ErrorAction SilentlyContinue

Write-Host "Config files copied to client_pc\root\" -ForegroundColor Green
Write-Host ""
Write-Host "Ready to test console commands!" -ForegroundColor Red
