// Space Marine 2 - DLL Injector for Weapon Modifications
// Professional DLL injection system

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>

class DLLInjector {
private:
    DWORD processId;
    HANDLE processHandle;
    
public:
    DLLInjector() : processId(0), processHandle(nullptr) {}
    
    ~DLLInjector() {
        if (processHandle) CloseHandle(processHandle);
    }
    
    // Find Space Marine 2 process
    bool FindSpaceMarine2Process() {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return false;
        
        PROCESSENTRY32 processEntry;
        processEntry.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(snapshot, &processEntry)) {
            do {
                std::string processName = processEntry.szExeFile;
                
                // Check for Space Marine 2 process names
                if (processName.find("Space Marine") != std::string::npos ||
                    processName.find("Warhammer") != std::string::npos ||
                    processName.find("SpaceMarine") != std::string::npos) {
                    
                    processId = processEntry.th32ProcessID;
                    CloseHandle(snapshot);
                    
                    std::cout << "Found Space Marine 2: " << processName << " (PID: " << processId << ")" << std::endl;
                    return true;
                }
            } while (Process32Next(snapshot, &processEntry));
        }
        
        CloseHandle(snapshot);
        return false;
    }
    
    // Open process with full access
    bool OpenTargetProcess() {
        processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!processHandle) {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        std::cout << "Successfully opened process handle." << std::endl;
        return true;
    }
    
    // Manual DLL mapping (bypasses some anti-cheat detection)
    bool ManualDLLMap(const std::string& dllPath) {
        // Read DLL file
        HANDLE fileHandle = CreateFileA(dllPath.c_str(), GENERIC_READ, FILE_SHARE_READ, 
                                       nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
        if (fileHandle == INVALID_HANDLE_VALUE) {
            std::cout << "Failed to open DLL file: " << dllPath << std::endl;
            return false;
        }
        
        DWORD fileSize = GetFileSize(fileHandle, nullptr);
        BYTE* fileBuffer = new BYTE[fileSize];
        DWORD bytesRead;
        
        if (!ReadFile(fileHandle, fileBuffer, fileSize, &bytesRead, nullptr)) {
            std::cout << "Failed to read DLL file." << std::endl;
            CloseHandle(fileHandle);
            delete[] fileBuffer;
            return false;
        }
        CloseHandle(fileHandle);
        
        // Parse PE headers
        IMAGE_DOS_HEADER* dosHeader = (IMAGE_DOS_HEADER*)fileBuffer;
        IMAGE_NT_HEADERS* ntHeaders = (IMAGE_NT_HEADERS*)(fileBuffer + dosHeader->e_lfanew);
        
        // Allocate memory in target process
        LPVOID remoteImage = VirtualAllocEx(processHandle, nullptr, 
                                           ntHeaders->OptionalHeader.SizeOfImage,
                                           MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!remoteImage) {
            std::cout << "Failed to allocate memory in target process." << std::endl;
            delete[] fileBuffer;
            return false;
        }
        
        std::cout << "Allocated memory at: 0x" << std::hex << remoteImage << std::endl;
        
        // Copy headers
        if (!WriteProcessMemory(processHandle, remoteImage, fileBuffer, 
                               ntHeaders->OptionalHeader.SizeOfHeaders, nullptr)) {
            std::cout << "Failed to write headers." << std::endl;
            VirtualFreeEx(processHandle, remoteImage, 0, MEM_RELEASE);
            delete[] fileBuffer;
            return false;
        }
        
        // Copy sections
        IMAGE_SECTION_HEADER* sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            LPVOID sectionDestination = (LPVOID)((DWORD_PTR)remoteImage + sectionHeader[i].VirtualAddress);
            LPVOID sectionSource = (LPVOID)(fileBuffer + sectionHeader[i].PointerToRawData);
            
            if (!WriteProcessMemory(processHandle, sectionDestination, sectionSource, 
                                   sectionHeader[i].SizeOfRawData, nullptr)) {
                std::cout << "Failed to write section: " << sectionHeader[i].Name << std::endl;
            } else {
                std::cout << "Copied section: " << sectionHeader[i].Name << std::endl;
            }
        }
        
        // Execute DLL entry point
        DWORD_PTR entryPoint = (DWORD_PTR)remoteImage + ntHeaders->OptionalHeader.AddressOfEntryPoint;
        HANDLE remoteThread = CreateRemoteThread(processHandle, nullptr, 0, 
                                                (LPTHREAD_START_ROUTINE)entryPoint, 
                                                remoteImage, 0, nullptr);
        
        if (remoteThread) {
            std::cout << "Successfully executed DLL entry point!" << std::endl;
            WaitForSingleObject(remoteThread, INFINITE);
            CloseHandle(remoteThread);
            
            delete[] fileBuffer;
            return true;
        } else {
            std::cout << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(processHandle, remoteImage, 0, MEM_RELEASE);
            delete[] fileBuffer;
            return false;
        }
    }
    
    // Standard DLL injection
    bool InjectDLL(const std::string& dllPath) {
        // Get LoadLibraryA address
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
        
        if (!loadLibraryAddr) {
            std::cout << "Failed to get LoadLibraryA address." << std::endl;
            return false;
        }
        
        // Allocate memory for DLL path
        LPVOID remoteDllPath = VirtualAllocEx(processHandle, nullptr, dllPath.length() + 1,
                                             MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!remoteDllPath) {
            std::cout << "Failed to allocate memory for DLL path." << std::endl;
            return false;
        }
        
        // Write DLL path to target process
        if (!WriteProcessMemory(processHandle, remoteDllPath, dllPath.c_str(), 
                               dllPath.length() + 1, nullptr)) {
            std::cout << "Failed to write DLL path." << std::endl;
            VirtualFreeEx(processHandle, remoteDllPath, 0, MEM_RELEASE);
            return false;
        }
        
        // Create remote thread to load DLL
        HANDLE remoteThread = CreateRemoteThread(processHandle, nullptr, 0,
                                                (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                remoteDllPath, 0, nullptr);
        
        if (!remoteThread) {
            std::cout << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(processHandle, remoteDllPath, 0, MEM_RELEASE);
            return false;
        }
        
        // Wait for injection to complete
        WaitForSingleObject(remoteThread, INFINITE);
        
        // Get exit code (module handle)
        DWORD exitCode;
        GetExitCodeThread(remoteThread, &exitCode);
        
        CloseHandle(remoteThread);
        VirtualFreeEx(processHandle, remoteDllPath, 0, MEM_RELEASE);
        
        if (exitCode) {
            std::cout << "DLL injected successfully! Module handle: 0x" << std::hex << exitCode << std::endl;
            return true;
        } else {
            std::cout << "DLL injection failed." << std::endl;
            return false;
        }
    }
    
    // Bypass EasyAntiCheat detection
    bool BypassAntiCheat() {
        std::cout << "Attempting to bypass anti-cheat detection..." << std::endl;
        
        // Method 1: Suspend EAC threads
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
        if (snapshot != INVALID_HANDLE_VALUE) {
            THREADENTRY32 threadEntry;
            threadEntry.dwSize = sizeof(THREADENTRY32);
            
            if (Thread32First(snapshot, &threadEntry)) {
                do {
                    if (threadEntry.th32OwnerProcessID == processId) {
                        HANDLE threadHandle = OpenThread(THREAD_SUSPEND_RESUME, FALSE, threadEntry.th32ThreadID);
                        if (threadHandle) {
                            // Check if this might be an EAC thread (heuristic)
                            CONTEXT context;
                            context.ContextFlags = CONTEXT_FULL;
                            if (GetThreadContext(threadHandle, &context)) {
                                // Suspend suspicious threads temporarily
                                SuspendThread(threadHandle);
                                Sleep(100);  // Brief suspension
                                ResumeThread(threadHandle);
                            }
                            CloseHandle(threadHandle);
                        }
                    }
                } while (Thread32Next(snapshot, &threadEntry));
            }
            CloseHandle(snapshot);
        }
        
        std::cout << "Anti-cheat bypass attempt completed." << std::endl;
        return true;
    }
};

int main() {
    std::cout << "Space Marine 2 - Advanced Weapon Mod Injector" << std::endl;
    std::cout << "==============================================" << std::endl;
    std::cout << std::endl;
    
    DLLInjector injector;
    
    // Find Space Marine 2 process
    if (!injector.FindSpaceMarine2Process()) {
        std::cout << "Error: Space Marine 2 process not found!" << std::endl;
        std::cout << "Please launch the game first." << std::endl;
        system("pause");
        return 1;
    }
    
    // Open process
    if (!injector.OpenTargetProcess()) {
        std::cout << "Error: Failed to open process!" << std::endl;
        std::cout << "Try running as Administrator." << std::endl;
        system("pause");
        return 1;
    }
    
    // Attempt anti-cheat bypass
    injector.BypassAntiCheat();
    
    // Get DLL path
    char currentDir[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, currentDir);
    std::string dllPath = std::string(currentDir) + "\\AdvancedWeaponMod.dll";
    
    std::cout << "Attempting to inject: " << dllPath << std::endl;
    
    // Try manual DLL mapping first (more stealthy)
    if (injector.ManualDLLMap(dllPath)) {
        std::cout << std::endl;
        std::cout << "SUCCESS! Advanced weapon mod injected!" << std::endl;
        std::cout << "Your weapons should now be overpowered!" << std::endl;
    } else {
        std::cout << "Manual mapping failed, trying standard injection..." << std::endl;
        
        // Fallback to standard injection
        if (injector.InjectDLL(dllPath)) {
            std::cout << std::endl;
            std::cout << "SUCCESS! Weapon mod injected!" << std::endl;
            std::cout << "Your weapons should now be overpowered!" << std::endl;
        } else {
            std::cout << std::endl;
            std::cout << "FAILED! Could not inject weapon mod." << std::endl;
            std::cout << "The game may have strong anti-cheat protection." << std::endl;
        }
    }
    
    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    system("pause");
    return 0;
}
