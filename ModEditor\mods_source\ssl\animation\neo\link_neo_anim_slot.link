__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __where   =   {
               __type   =   "^mr_anim_slot$"
            }
            __onlyChanged   =   False
            __type   =   "modelRecursive"
         }
         __showOnlyChildren   =   True
         __path   =   "animationSlots"
         __type   =   "model"
      }
      __groupByParents   =   False
      __convert   =   {
         __type   =   "string"
         __family   =   ".mr"
      }
      __type   =   "brand"
   }
   __showOnlyChildren   =   True
   __format   =   "{value}"
   __tag   =   "neoNetworkName"
   __type   =   "modelTag"
}
__type   =   "linkBrowser"

