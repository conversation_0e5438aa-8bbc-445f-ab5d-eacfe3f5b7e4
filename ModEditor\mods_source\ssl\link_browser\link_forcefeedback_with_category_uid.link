__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "forcefeedback"
         __where   =   {
            __type   =   ""
         }
         __path   =   "/*"
         __type   =   "model"
      }
      __path   =   "catalogForceFeedbacks/*"
      __type   =   "model"
   }
   __where   =   {
      __name   =   "ForceFeedbackPresetProjectList"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{forcefeedback}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

