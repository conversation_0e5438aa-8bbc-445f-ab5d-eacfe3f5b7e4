formats = {
	MD = {
		src = "rgb"
	}
	
	MT = {
		src = "a"
	}
	
	MAK = {
		src = "a"
	}
	
	MNM	= {
		src = "rg"
	}
	
	MDT = {
		src = "rg"
	}
	
	MSC = {
		src = "rgb"
	}

	MSCG = {
		src = "r"
	}
	
	MSP	 = {
		src = "a"
	}
	
	MRGH = {
		src = "a"
	}
	
	MH = {
		src = "r"
	}

	MD16U = {
		src = "argb"
	}
	
	MDTM = {
		src = "g"
	}
	
	MBR	 = {
		src = "r"
	}

	MBL	 = {
		src = "g"
	}

	MR = {
		src = "b"
	}

	MF = {
		src = "a"
	}
	
	MLMDIF	= {
		src = "argb"
	}

	MLMDIR = {
		src = "argb"
	}
	MPM = {
		src = "r"
	}

	MRC = {
		src = "rgb"
	}

	HDR = {
		src = "a"
	}

	MEM = {
		src = "rgb"
	}

	MAO = {
		src = "a"
	}
	
	MSCRGHAO = {
		src = "rgb"
	}
	
	MRGHAOD = {
		src = "rgba"
	}
	MMRAOT = {
		src = "rgba"
	}
	MTRAO = {
		src = "rgb"
	}
}


usages = {
	all = {
		MD = {
			comp = BC7
			compf = BC6U
			uncomp = XRGB8888
			dxt = OXT1
			nChanels = 3
			desc = "Diff(rgb)"
			gammaConv = true
		}
		MD_MT = {
			comp = BC7A
			uncomp = ARGB8888
			dxt = XT5
			nChanels = 4
			desc = "Diff(rgb)+Transp(a)"
			gammaConv = true
		}
		MD_MAK = {
			comp = BC7A
			uncomp = ARGB8888
			dxt = XT5
			nChanels = 4
			desc = "Diff(rgb)+AKill(a)"
			gammaConv = true
		}
		MD_MRGH = {
			comp = BC7A
			uncomp = ARGB8888
			dxt = XT5
			nChanels = 4
			desc = "Diff(rgb)+Roughness(a)"
			gammaConv = true
		}
		MD16U = {
			uncomp = ARGB16161616U
			nChanels = 4
			desc = "ARGB 16U"
		}
		MNM	= {
			comp = DXN
			uncomp = XRGB8888
			nChanels = 2
			desc = "Normalmap(rgb)"
		}
		MDT = {
			comp = DXN
			nChanels = 2
			desc = "DetNormal(rgb)"
		}
		MSCG_MRGH = {
			comp = DXN
			nChanels = 2
			desc = "MetGray(r)+Roughness(a)"
		}
		MSC_MRGH = {
			comp = BC7A
			dxt = XT5
			nChanels = 4
			desc = "Metalness(rgb)+Roughness(a) [for baked materials only!]"
		}
		MSCRGHAO = {
			comp = BC7
			dxt = OXT1
			nChanels = 3
			desc = "Metalness(r)+Roughness(g)+AO(b)"
		}
		MMRAOT = {
			comp = BC7A
			dxt = XT5
			nChanels = 4
			desc = "Metalness(r)+Roughness(g)+AO(b)+Translucent(a)"
		}
		MTRAO = {
			comp = BC7
			dxt = OXT1
			nChanels = 3
			desc = "Translucent(r)+Roughness(g)+AO(b)"
		}
		MRGHAOD = {
			comp = BC7A
			dxt = XT5
			nChanels = 4
			desc = "Roughness(r)+AO(g)+Depth(b)+Tranclucent(a)"
		}
		MEM = {
			comp = BC7
			dxt = DXT
			nChanels = 3
			desc = "RGBEm(rgb)"
		}
		MAO = {
			comp = DXT5A
			nChanels = 1
			desc = "AO(a)"
		}
		MEM_MAO = {
			comp = BC7A
			dxt = XT5
			nChanels = 4
			desc = "RGBEm(rgb)+AO(a)"
		}
		MH = {
			comp = DXT5A
			nChanels = 1
			desc = "Heightmap(r)"
		}
		
		MH_MDTM = {
			comp = DXN
			nChanels = 2
			desc = "Height(r)+DetMask(g)"
		}
		
		MDTM = {
			comp = DXT5A
			nChanels = 1
			desc = "DetMask(g)"
		}
		MBR	 = {
			comp = DXT5A
			nChanels = 1
			desc = "Brightener(r)"
		}

		MBL	 = {
			comp = DXT5A
			nChanels = 1
			desc = "Ambient(g)"
		}

		MR = {
			comp = DXT5A
			nChanels = 1
			desc = "Reflection(b)"
		}

		MF = {
			comp = DXT5A
			nChanels = 1
			desc = "Freshnel(a)"
		}

		MBR_MBL	 = {
			comp = DXN
			nChanels = 2
			desc = "Brightener(r)+Ambient(g)"
		}

		MBR_MF	= {
			comp = DXN
			nChanels = 2
			desc = "Brightener(r)+Freshnel(a)"
		}

		MBR_MR	= {
			comp = DXN
			nChanels = 2
			desc = "Brightener(r)+Reflection(b)"
		}

		MBL_MF	= {
			comp = DXN
			nChanels = 2
			desc = "Ambient(g)+Freshnel(a)"
		}

		MBL_MR	= {
			comp = DXN
			nChanels = 2
			desc = "Ambient(g)+Reflection(b)"
		}

		MR_MF = {
			comp = DXN
			nChanels = 2
			desc = "Reflection(b)+Freshnel(a)"
		}
		
		MBR_MBL_MF	= {
			uncomp = ARGB8888
			nChanels = 3
			desc = "Bright(r)+Amb(g)+Frnl(a)"
		}

		MBR_MBL_MR	= {
			uncomp = ARGB8888
			nChanels = 3
			desc = "Bright(r)+Amb(g)+Refl(b)"
		}

		MBR_MR_MF  = {
			uncomp = ARGB8888
			nChanels = 3
			desc = "Bright(r)+Refl(b)+Frnl(a)"
		}

		MBL_MR_MF  = {
			uncomp = ARGB8888
			nChanels = 3
			desc = "Amb(g)+Refl(b)+Frnl(a)"
		}

		MBR_MBL_MR_MF  = {
			uncomp = ARGB8888
			nChanels = 4
			desc = "Bright(r)+Amb(g)+Refl(b)+Frnl(a)"
		}
		MPM = {
			comp = DXT5A
			nChanels = 1
			desc = "MultiplayerMask(r)"
		}
		MLMDIF	= {
			uncomp = ARGB8888
			nChanels = 4
			desc = "DiffLightmap"
		}
		MLMDIR = {
			uncomp = ARGB8888
			nChanels = 4
			desc = "DirLightmap"
		}
		MRC	= {
			comp = BC7
			uncomp = ARGB8888
			dxt = OXT1
			nChanels = 3
			desc = "ReflCube(rgb)"
			gammaConv = true
		}
		MRC_HDR	= {
			comp = BC7A
			uncomp = ARGB8888
			compf = BC7A
			uncompf = ARGB8888
			dxt = XT5
			nChanels = 4
			desc = "ReflCube(rgb)+HDR"
		}
	}
}
