__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __format   =   "{value}"
                  __selectValue   =   True
                  __uniqueValues   =   True
                  __path   =   "name"
                  __type   =   "json"
               }
               __showOnlyChildren   =   True
               __where   =   {
                  __fields   =   [
                     {
                        __invert   =   True
                        __name   =   "name"
                        __check   =   {
                           __type   =   "check_string"
                        }
                     }
                  ]
               }
               __format   =   "{value}"
               __selectValue   =   True
               __selectField   =   "name"
               __path   =   "material_info.*"
               __type   =   "json"
            }
            __showOnlyChildren   =   True
            __format   =   "{value}"
            __recursive   =   True
            __recursivePath   =   "children"
            __selectValue   =   True
            __selectField   =   "name"
            __path   =   "engine_objects"
            __type   =   "json"
         }
         __showOnlyChildren   =   True
         __baseDir   =   "{arg}/.."
         __searchPattern   =   "obj_list.json"
         __recursive   =   False
         __type   =   "file"
      }
      __treeIndent   =   2
      __argFilter   =   {
         __argFilterFormat   =   "field:tpl"
         __argModification   =   "{arg}.tpl"
      }
      __descTypes   =   [
         "res_desc_tpl_asset",
         "res_desc_tpl"
      ]
      __assetSettings   =   {
         __assetResource   =   "linkBaseTpl"
      }
      __where   =   {
         __filters   =   [
            {
               __format   =   "field:tags"
               __value   =   "usage:Hidden"
               __mode   =   "Exclude"
            },
            {
               __format   =   "path"
               __value   =   "scenes/"
               __mode   =   "Exclude"
            }
         ]
      }
      __format   =   "{name no ext}"
      __type   =   "resource"
   }
   __showOnlyChildren   =   True
   __format   =   "{value}"
   __showValue   =   True
   __tag   =   "templateName"
   __type   =   "modelTag"
}
__addEmpty   =   {
   __value   =   True
}
__type   =   "linkBrowser"

