build-dir: $(project-dir)
meta-desc-dir: $(tools-dir)/meta_descs
meta-desc-internal-path: $(meta-desc-dir)/meta_desc_internal.ps
meta-desc-path: $(meta-desc-dir)/meta_desc.ps
meta-desc-ssl-path: $(meta-desc-dir)/meta_desc_ssl.ps
meta-desc-ssl-user-path: $(meta-desc-dir)/meta_desc_ssl_user.ps
mods-dir: $(project-dir)/mods_source
paks-dir: $(project-dir)/paks
paks-server-dir: $(project-dir)/../../server_pc/root/paks
paks-list:
- $(paks-dir)\client\default\default.pak
- $(paks-dir)\client\default\default_other.pak
- $(paks-dir)\client\default\default_ssl.pak
- $(paks-dir)\client\resources.pak
- $(paks-server-dir)\server\default\default.pak
- $(paks-server-dir)\server\default\default_other.pak
- $(paks-server-dir)\server\default\default_ssl.pak
- $(paks-server-dir)\server\resources.pak
paks-with-descs: $(paks-list)
project-assets-dir: $(mods-dir)
project-name: Space Marine 2
project-resources-dir: $(mods-dir)
references:
- prjenv/editor.prjenv
resource-pak-layers:
- path: $(paks-dir)\client\ultra/ultra_pct_resources.pak
  type: pak
- path: $(paks-dir)\client\resources.pak
  type: pak
- path: $(paks-dir)\client\editor\editor_resources.pak
  type: pak
resource-tools:
  layers:
  - path: $(project-assets-dir)
    type: file-system
  - $insert: $(resource-pak-layers)
  packages-dirs: {}
  packages-list-file: $(build-dir)/packages.yaml
  paks-dir: $(paks-dir)
ssl-dirs: []
ssl-include-dirs:
- $(mods-dir)
tools-dir: $(build-dir)\tools
