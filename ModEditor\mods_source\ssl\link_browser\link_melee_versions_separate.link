__request   =   {
   __select   =   {
      __select   =   {
         __id   =   ""
         __select   =   {
            __id   =   "versionId"
            __showOnlyChildren   =   False
            __format   =   "{value}"
            __showValue   =   True
            __path   =   "versionUid"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __format   =   "{name}"
         __showValue   =   False
         __path   =   "/*"
         __type   =   "model"
      }
      __showOnlyChildren   =   False
      __format   =   "{name}"
      __showValue   =   False
      __path   =   "versions/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "MeleeVersionsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{versionId}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

