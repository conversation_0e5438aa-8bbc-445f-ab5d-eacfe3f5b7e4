properties   =   {
   geom   =   {
      nameTpl   =   "wpn_fumo_cirno"
   }
   sfx   =   {
      list   =   {
         trail_wwz_grenade_flame   =   {
            visuals   =   {
               color   =   [
                  0,
                  65,
                  177,
                  96,
                  21,
                  1,
                  0,
                  118,
                  53,
                  5
               ]
               colorIntensityMultiplier   =   50
            }
            __type   =   "trail_wwz_revolver_gl"
         }
      }
      __onlyOn   =   "visual"
   }
   sound_emitter   =   {
      enableObstruction   =   True
      emitterSize   =   10
      __onlyOn   =   "visual"
   }
   prop_equipment_client   =   {
      loadoutItemCategoryUid   =   "left hand item"
   }
   prop_tb_simple_explode_client   =   {
      sfxCreator   =   {
         sslModule   =   "damage.end_effects"
         end_effect   =   {
            default   =   {
               entities   =   [
                  {
                     nameClass   =   "sfx_mg_frag_grenade_expl"
                  }
               ]
               scorchmark   =   {
                  __type   =   "scr_molotov"
               }
               __type   =   "ee_gren_frag"
            }
            water   =   {
               entities   =   [
                  {
                     nameClass   =   "sfx_mg_frag_grenade_expl"
                  }
               ]
               scorchmark   =   {
                  __type   =   "null"
               }
               __type   =   "ee_gren_frag"
            }
            sand   =   {
               entities   =   [
                  {
                     nameClass   =   "sfx_mg_frag_grenade_expl_sand"
                  }
               ]
               scorchmark   =   {
                  __type   =   "scr_grenade_sand"
               }
               __type   =   "ee_gren_frag"
            }
         }
         __type   =   "DamageSfxCreator"
      }
      explosionSound   =   {
         sslModule   =   "sound.sound_game"
         type   =   {
            sslModule   =   "sound.sound_game"
            name   =   "wpn_expl_grnd"
            __type   =   "GameSound"
         }
         __type   =   "GameSoundDesc"
      }
   }
   prop_damager   =   {
      damagerLink   =   {
         damagerDescUid   =   "frag_grenade_damager"
         __type   =   "DamagerDescriptionLink"
      }
   }
   prop_phys_material_end_effects   =   {
      endEffectsCreator   =   {
         soundDesc   =   {
            type   =   {
               name   =   "phys_grenade_collision"
               __type   =   "GameSound"
            }
            __type   =   "GameSoundDesc"
         }
         __type   =   "EndEffectsCreator"
      }
      collisionDetectionIntervalMs   =   25
      massTresholdMin   =   -1
      collisionsCountMax   =   4
      velocityLengthTresholdMin   =   0.001
   }
}
__type   =   "simple_grenade_base_client"
