// Space Marine 2 - Flamethrower Status Effects
// This file defines the burning and other status effects for the flamethrower

StatusEffects = {
    statusEffects = {
        // Enhanced burning effect for flamethrower
        flamethrower_burning = {
            uid = "flamethrower_burning"
            name = "Burning"
            type = "damage_over_time"
            category = "debuff"
            
            // Basic properties
            duration = 5.0
            tickRate = 1.0              // Damage every second
            damagePerTick = 10.0
            damageType = "fire"
            
            // Stacking behavior
            stackable = true
            maxStacks = 3
            stackBehavior = "additive"   // Each stack adds full damage
            refreshOnReapply = true      // Resets duration when reapplied
            
            // Visual effects
            visual = {
                effect = "sfx_enemy_burning"
                color = [255, 100, 0]    // Orange flame color
                intensity = 1.5
                particleCount = 25
                
                // Different effects for different enemy types
                enemyEffects = {
                    tyranid = "sfx_tyranid_burning"
                    chaos_marine = "sfx_marine_burning"
                    cultist = "sfx_human_burning"
                }
                
                // Screen effects for player
                screenEffect = {
                    enabled = true
                    overlay = "ui_burning_overlay"
                    intensity = 0.3
                    pulseRate = 2.0
                }
            }
            
            // Audio effects
            audio = {
                applySound = "sfx_ignite"
                loopSound = "sfx_burning_loop"
                removeSound = "sfx_extinguish"
                
                volume = 0.6
                pitchVariation = 0.2
                fadeIn = 0.3
                fadeOut = 0.5
            }
            
            // Gameplay effects
            effects = {
                // Movement penalty while burning
                movementSpeed = {
                    enabled = true
                    multiplier = 0.9     // 10% slower movement
                    stackMultiplier = 0.95 // Additional 5% per stack
                }
                
                // Accuracy penalty from pain
                accuracy = {
                    enabled = true
                    multiplier = 0.85    // 15% accuracy penalty
                    stackMultiplier = 0.9  // Additional 10% per stack
                }
                
                // Panic behavior for some enemies
                panic = {
                    enabled = true
                    chance = 0.3         // 30% chance per tick
                    duration = 2.0       // 2 second panic
                    affectedTypes = [
                        "cultist",
                        "tyranid_basic"
                    ]
                }
            }
            
            // Spread mechanics
            spread = {
                enabled = true
                spreadChance = 0.2       // 20% chance per tick to spread
                spreadRadius = 2.0       // 2 meter spread radius
                maxTargets = 2           // Can spread to 2 nearby enemies
                spreadDuration = 3.0     // Spread burning lasts 3 seconds
                
                // Conditions for spreading
                conditions = {
                    requireLineOfSight = false
                    onlyToSameType = false
                    excludeAllies = true
                }
            }
            
            // Interaction with other effects
            interactions = {
                // Extinguished by water/ice effects
                counters = [
                    "water_effect",
                    "ice_effect",
                    "frost_effect"
                ]
                
                // Enhanced by explosive effects
                amplifiers = [
                    "explosive_effect"
                ]
                
                // Prevents certain effects
                prevents = [
                    "freeze_effect",
                    "slow_effect"
                ]
            }
            
            __type = "StatusEffect"
        }
        
        // Fear effect for flamethrower
        flamethrower_fear = {
            uid = "flamethrower_fear"
            name = "Terror"
            type = "behavioral"
            category = "debuff"
            
            // Basic properties
            duration = 2.0
            applyChance = 0.3
            
            // Affected enemy types
            affectedTypes = [
                "tyranid_basic",
                "tyranid_warrior",
                "cultist",
                "chaos_marine_basic"
            ]
            
            // Immunity list (bosses and elite enemies)
            immuneTypes = [
                "carnifex",
                "chaos_marine_heavy",
                "boss_enemy"
            ]
            
            // Behavioral changes
            behavior = {
                type = "flee"
                fleeSpeed = 1.5          // 50% faster movement when fleeing
                fleeDistance = 10.0      // Flee at least 10 meters away
                accuracyPenalty = 0.5    // 50% accuracy penalty
                meleePenalty = 0.3       // 70% melee damage penalty
                
                // Chance to break fear early
                breakChance = 0.1        // 10% chance per second to recover
                breakOnDamage = 0.3      // 30% chance to recover when damaged
            }
            
            // Visual effects
            visual = {
                effect = "sfx_fear_aura"
                enemyEffect = "sfx_enemy_fear"
                color = [255, 0, 0]      // Red fear aura
                intensity = 0.8
                
                // Enemy visual changes
                enemyVisual = {
                    shakingIntensity = 0.5
                    movementErratic = true
                    aimWobble = 2.0
                }
            }
            
            // Audio effects
            audio = {
                applySound = "sfx_fear_apply"
                loopSound = "sfx_fear_loop"
                removeSound = "sfx_fear_remove"
                
                // Enemy audio changes
                enemyAudio = {
                    panicSounds = [
                        "enemy_panic_01",
                        "enemy_panic_02",
                        "enemy_panic_03"
                    ]
                    breathingIntensity = 2.0
                }
            }
            
            __type = "StatusEffect"
        }
        
        // Armor melting effect for heavily armored enemies
        flamethrower_armor_melt = {
            uid = "flamethrower_armor_melt"
            name = "Armor Degradation"
            type = "armor_reduction"
            category = "debuff"
            
            // Basic properties
            duration = 8.0
            applyChance = 0.2
            armorReduction = 0.3         // 30% armor reduction
            
            // Only affects heavily armored enemies
            affectedTypes = [
                "chaos_marine_heavy",
                "tyranid_warrior",
                "carnifex",
                "terminator"
            ]
            
            // Progressive armor melting
            progression = {
                enabled = true
                stages = 3
                stageInterval = 2.0      // Every 2 seconds
                reductionPerStage = 0.1  // Additional 10% per stage
            }
            
            // Visual effects
            visual = {
                effect = "sfx_armor_melting"
                armorGlow = true
                glowColor = [255, 150, 0] // Orange glow on armor
                sparkEffect = "sfx_metal_sparks"
                
                // Progressive visual changes
                stageEffects = {
                    stage1 = "sfx_armor_heat_1"
                    stage2 = "sfx_armor_heat_2"
                    stage3 = "sfx_armor_melt_3"
                }
            }
            
            // Audio effects
            audio = {
                applySound = "sfx_armor_heat"
                loopSound = "sfx_metal_sizzle"
                stageSound = "sfx_armor_crack"
                removeSound = "sfx_armor_cool"
            }
            
            // Gameplay effects
            effects = {
                // Increased damage taken
                damageMultiplier = {
                    base = 1.3           // 30% more damage taken
                    perStage = 0.1       // Additional 10% per stage
                }
                
                // Reduced movement speed due to damaged armor
                movementSpeed = {
                    multiplier = 0.95    // 5% slower
                    perStage = 0.05      // Additional 5% per stage
                }
            }
            
            __type = "StatusEffect"
        }
    }
    
    __type = "StatusEffects"
}
