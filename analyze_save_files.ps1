# Space Marine 2 - Save File Analysis and Modification
# This script finds and analyzes save files for potential modification

Write-Host "Space Marine 2 Save File Analysis" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Steam userdata path
$SteamPath = "C:\Program Files (x86)\Steam\userdata"
$AppId = "2183900"

Write-Host "Searching for Space Marine 2 save files..." -ForegroundColor Yellow

# Find all user folders with Space Marine 2 data
$SavePaths = Get-ChildItem -Path $SteamPath -Recurse -Directory -Name "*$AppId*" -ErrorAction SilentlyContinue

if ($SavePaths.Count -eq 0) {
    Write-Host "No save files found in Steam userdata." -ForegroundColor Red
    Write-Host "Checking alternative locations..." -ForegroundColor Yellow
    
    # Check Documents folder
    $DocsPath = "$env:USERPROFILE\Documents"
    $DocsSaves = Get-ChildItem -Path $DocsPath -Recurse -Directory -Name "*Space Marine*" -ErrorAction SilentlyContinue
    
    if ($DocsSaves.Count -gt 0) {
        Write-Host "Found potential save locations in Documents:" -ForegroundColor Green
        $DocsSaves | ForEach-Object { Write-Host "  $DocsPath\$_" -ForegroundColor White }
    }
    
    # Check AppData
    $AppDataPaths = @(
        "$env:APPDATA",
        "$env:LOCALAPPDATA"
    )
    
    foreach ($AppDataPath in $AppDataPaths) {
        $AppDataSaves = Get-ChildItem -Path $AppDataPath -Recurse -Directory -Name "*Space Marine*" -ErrorAction SilentlyContinue
        if ($AppDataSaves.Count -gt 0) {
            Write-Host "Found potential save locations in ${AppDataPath}:" -ForegroundColor Green
            $AppDataSaves | ForEach-Object { Write-Host "  ${AppDataPath}\$_" -ForegroundColor White }
        }
    }
    
    Write-Host ""
    Write-Host "MANUAL SEARCH INSTRUCTIONS:" -ForegroundColor Cyan
    Write-Host "1. Open File Explorer" -ForegroundColor White
    Write-Host "2. Search for files containing 'Space Marine' or '2183900'" -ForegroundColor White
    Write-Host "3. Look for .sav, .dat, .json, .xml, or .bin files" -ForegroundColor White
    Write-Host "4. Common locations:" -ForegroundColor White
    Write-Host "   - Documents\My Games\" -ForegroundColor Gray
    Write-Host "   - AppData\Local\" -ForegroundColor Gray
    Write-Host "   - AppData\Roaming\" -ForegroundColor Gray
    Write-Host "   - Steam\userdata\[userid]\2183900\" -ForegroundColor Gray
    
} else {
    Write-Host "Found Space Marine 2 save locations:" -ForegroundColor Green
    
    foreach ($SavePath in $SavePaths) {
        $FullPath = Join-Path $SteamPath $SavePath
        Write-Host "  $FullPath" -ForegroundColor White
        
        # Analyze files in this location
        $Files = Get-ChildItem -Path $FullPath -Recurse -File -ErrorAction SilentlyContinue
        
        if ($Files.Count -gt 0) {
            Write-Host "    Files found:" -ForegroundColor Yellow
            foreach ($File in $Files) {
                $Size = [math]::Round($File.Length / 1KB, 2)
                Write-Host "      $($File.Name) ($Size KB)" -ForegroundColor Gray
                
                # Try to identify file type
                $Extension = $File.Extension.ToLower()
                switch ($Extension) {
                    ".vdf" { Write-Host "        Type: Valve Data File (Steam config)" -ForegroundColor Cyan }
                    ".sav" { Write-Host "        Type: Save game file" -ForegroundColor Green }
                    ".dat" { Write-Host "        Type: Data file (potential save)" -ForegroundColor Green }
                    ".json" { Write-Host "        Type: JSON data (readable)" -ForegroundColor Green }
                    ".xml" { Write-Host "        Type: XML data (readable)" -ForegroundColor Green }
                    ".bin" { Write-Host "        Type: Binary data (encrypted?)" -ForegroundColor Yellow }
                    default { Write-Host "        Type: Unknown ($Extension)" -ForegroundColor Gray }
                }
            }
        }
    }
}

Write-Host ""
Write-Host "SAVE FILE MODIFICATION APPROACHES:" -ForegroundColor Cyan
Write-Host ""

Write-Host "Method 1: JSON/XML Modification" -ForegroundColor Yellow
Write-Host "- Look for readable text files" -ForegroundColor White
Write-Host "- Search for weapon stats, damage values" -ForegroundColor White
Write-Host "- Modify values directly" -ForegroundColor White
Write-Host ""

Write-Host "Method 2: Hex Editor Modification" -ForegroundColor Yellow
Write-Host "- Use HxD or similar hex editor" -ForegroundColor White
Write-Host "- Search for damage values (try 25, 50, 100)" -ForegroundColor White
Write-Host "- Replace with higher values (999, 9999)" -ForegroundColor White
Write-Host ""

Write-Host "Method 3: Save Game Editor Tools" -ForegroundColor Yellow
Write-Host "- Search for 'Space Marine 2 save editor'" -ForegroundColor White
Write-Host "- Check modding communities" -ForegroundColor White
Write-Host "- Use Cheat Engine save file scanner" -ForegroundColor White
Write-Host ""

# Create backup script
$BackupScript = @"
# Space Marine 2 Save Backup Script
Write-Host "Creating save file backup..." -ForegroundColor Yellow

`$BackupDir = "space_marine_2_save_backup_`$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path `$BackupDir -Force | Out-Null

# Backup Steam saves
`$SteamSaves = Get-ChildItem -Path "C:\Program Files (x86)\Steam\userdata" -Recurse -Directory -Name "*2183900*" -ErrorAction SilentlyContinue
foreach (`$SavePath in `$SteamSaves) {
    `$Source = "C:\Program Files (x86)\Steam\userdata\`$SavePath"
    `$Dest = "`$BackupDir\steam_`$SavePath"
    Copy-Item `$Source `$Dest -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Backup created: `$BackupDir" -ForegroundColor Green
Write-Host "Always backup before modifying saves!" -ForegroundColor Red
"@

$BackupScript | Out-File "backup_saves.ps1" -Encoding UTF8

Write-Host "NEXT STEPS:" -ForegroundColor Red
Write-Host "1. Run backup_saves.ps1 to backup your saves" -ForegroundColor White
Write-Host "2. Try the console command methods first" -ForegroundColor White
Write-Host "3. If no console, analyze save files manually" -ForegroundColor White
Write-Host "4. Use hex editor to modify damage values" -ForegroundColor White
Write-Host ""
Write-Host "Created: backup_saves.ps1 (run this first!)" -ForegroundColor Green
