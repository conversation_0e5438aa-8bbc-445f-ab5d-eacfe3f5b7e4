mtl_fill {
   __caption = "mtl_fill"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   zTest {
      __type = "bool"
   }
   
   writeDepth {
      __type = "bool"
   }
   
   isTransparent {
      __type = "bool"
   }
   
   noBackfaceCulling {
      __type = "bool"
   }
   
   isUseForPortals {
      __type = "bool"
   }
   
   isUseForOpticalSights {
      __type = "bool"
   }
   
   isGrassCutter {
      __type = "bool"
   }
   
   action {
      __type = "enum"
      __list = ["none", "clear_alpha", "clear_lightshafts_mask", "write_imposter_deep", "part_overdraw"]
   }
   
}