base {
   __caption = "base"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   enableRightProjSpace {
      __type = "bool"
   }
   
   rgbSrc {
      __type = "enum"
      __list = ["tex", "tex_octahedron", "tex_b", "tex_mul_vert", "tex_coord"]
   }
   
   useConstColor {
      __type = "bool"
   }
   
   alphaSrc {
      __type = "enum"
      __list = ["tex", "tex_mul_vert", "tex_add_vert"]
   }
   
   colorConst {
      __type = "color"
   }
   
   akillRef {
      __type = float
   }
   
   blendMode {
      __type = "enum"
      __list = ["none", "blend", "add", "add_no_alpha", "lighten", "darken", "subtract", "mul", "invert", "premul_a"]
   }
   
   blendModeAlpha {
      __type = "enum"
      __list = ["none", "add", "opaque"]
   }
   
   zWrite {
      __type = "bool"
   }
   
   zTest {
      __type = "bool"
   }
   
   zInvert {
      __type = "bool"
   }
   
   noCull {
      __type = "bool"
   }
   
   rendInsteadNonComp {
      __type = "bool"
   }
   
   noJitter {
      __type = "bool"
   }
   
   srgbRead {
      __type = "bool"
   }
   
   srgbWrite {
      __type = "bool"
   }
   
   swizzleTexInShader {
      __type = "bool"
   }
   
   uint4Tex {
      __type = "bool"
   }
   
   hsvTex {
      __type = "bool"
   }
   
   applyNormalizationToUniformRange {
      __type = "bool"
   }
   
   thermoGrad {
      __type = "bool"
   }
   
   lumThreshold {
      __type = "bool"
   }
   
   lightTextureType {
      __type = "enum"
      __list = ["none", "spot", "point"]
   }
   
   tex = {
      __type = "texture"
      __callback = ""
   }
   
   texWrapMode {
      __type = "enum"
      __list = ["clamp", "repeat"]
   }
   
   facetedCameraLighting {
      __type = "bool"
   }
   
}