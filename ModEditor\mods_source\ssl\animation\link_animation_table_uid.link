__request   =   {
   __select   =   {
      __id   =   "act"
      __select   =   {
         __id   =   "cat"
         __select   =   {
            __id   =   "anim"
            __path   =   "animations/*"
            __type   =   "model"
         }
         __path   =   "categories/*"
         __type   =   "model"
      }
      __path   =   "actorCategories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "AnimationTable"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{act}.{cat}.{anim}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"


