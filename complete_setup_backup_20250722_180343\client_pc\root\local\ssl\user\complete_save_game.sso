// Space Marine 2 - Complete Save Game with Everything Unlocked
// This file creates a save game with all content unlocked for testing

UserProgressionData = {
    // Player level and experience
    playerLevel = 25                    // Max level
    totalExperience = 500000           // Massive XP
    availableSkillPoints = 100         // Lots of skill points
    
    // Campaign progress
    campaignProgress = {
        completed = true
        difficulty = "veteran"
        allMissionsCompleted = true
        allSecretsFound = true
        allDataslatesCollected = true
        
        // Individual mission completion
        missions = {
            mission_01_skyfire = { completed = true, difficulty = "veteran", score = 100000 }
            mission_02_severance = { completed = true, difficulty = "veteran", score = 100000 }
            mission_03_machinus_divinitus = { completed = true, difficulty = "veteran", score = 100000 }
            mission_04_servant_of_the_machine = { completed = true, difficulty = "veteran", score = 100000 }
            mission_05_vox_liberatis = { completed = true, difficulty = "veteran", score = 100000 }
            mission_06_dawn_of_war = { completed = true, difficulty = "veteran", score = 100000 }
        }
        
        // Collectibles
        collectibles = {
            dataslates = 100               // All dataslates found
            secrets = 50                   // All secrets found
            lore_items = 75               // All lore items
        }
    }
    
    // Weapon mastery progression
    weaponMastery = {
        // All weapons at max mastery
        bolt_rifle = { level = 5, experience = 10000, allPerksUnlocked = true }
        heavy_bolter = { level = 5, experience = 10000, allPerksUnlocked = true }
        plasma_gun = { level = 5, experience = 10000, allPerksUnlocked = true }
        melta_gun = { level = 5, experience = 10000, allPerksUnlocked = true }
        las_fusil = { level = 5, experience = 10000, allPerksUnlocked = true }
        stalker_bolter = { level = 5, experience = 10000, allPerksUnlocked = true }
        bolt_pistol = { level = 5, experience = 10000, allPerksUnlocked = true }
        chainsword = { level = 5, experience = 10000, allPerksUnlocked = true }
        power_sword = { level = 5, experience = 10000, allPerksUnlocked = true }
        thunder_hammer = { level = 5, experience = 10000, allPerksUnlocked = true }
        
        // Our flamethrower at max level
        flamethrower_heavy = { level = 5, experience = 10000, allPerksUnlocked = true }
    }
    
    // Class progression
    classProgression = {
        tactical = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
        
        assault = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
        
        heavy = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
        
        vanguard = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
        
        bulwark = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
        
        sniper = {
            level = 25
            experience = 100000
            allPerksUnlocked = true
            allAbilitiesUnlocked = true
        }
    }
    
    // Unlocked content
    unlockedContent = {
        // All weapons unlocked
        weapons = [
            "bolt_rifle", "heavy_bolter", "plasma_gun", "melta_gun",
            "las_fusil", "stalker_bolter", "bolt_pistol", "chainsword",
            "power_sword", "thunder_hammer", "flamethrower_heavy"
        ]
        
        // All armor sets unlocked
        armor = [
            "mk_vii_aquila", "mk_viii_errant", "mk_x_tacticus",
            "mk_x_phobos", "mk_x_gravis", "artificer_armor",
            "terminator_armor", "veteran_armor"
        ]
        
        // All helmets unlocked
        helmets = [
            "mk_vii_helmet", "mk_viii_helmet", "mk_x_helmet",
            "phobos_helmet", "gravis_helmet", "artificer_helmet",
            "terminator_helmet", "veteran_helmet"
        ]
        
        // All weapon skins unlocked
        weaponSkins = [
            "default", "veteran", "artificer", "relic", "master_crafted",
            "chapter_specific", "campaign_reward", "special_edition"
        ]
        
        // All armor colors/heraldry unlocked
        heraldry = [
            "ultramarines", "blood_angels", "dark_angels", "space_wolves",
            "imperial_fists", "iron_hands", "salamanders", "raven_guard",
            "white_scars", "custom_chapter"
        ]
    }
    
    // Operations progress
    operationsProgress = {
        completedOperations = 50
        highestDifficulty = "ruthless"
        totalPlaytime = 100.0
        
        // Individual operation records
        operations = {
            inferno = { completed = true, difficulty = "ruthless", bestTime = 15.5 }
            decapitation = { completed = true, difficulty = "ruthless", bestTime = 18.2 }
            vanguard = { completed = true, difficulty = "ruthless", bestTime = 20.1 }
            reliquary = { completed = true, difficulty = "ruthless", bestTime = 22.3 }
            fall_of_atreus = { completed = true, difficulty = "ruthless", bestTime = 25.7 }
            ballistic_engine = { completed = true, difficulty = "ruthless", bestTime = 19.8 }
        }
    }
    
    // Eternal War progress
    eternalWarProgress = {
        totalMatches = 100
        wins = 75
        losses = 25
        winRate = 0.75
        highestRank = "diamond"
        currentRank = "diamond"
        
        // Per-mode stats
        modes = {
            annihilation = { matches = 30, wins = 22, losses = 8 }
            seize_ground = { matches = 35, wins = 26, losses = 9 }
            capture_and_control = { matches = 35, wins = 27, losses = 8 }
        }
    }
    
    // Achievements/Challenges
    achievements = {
        campaignAchievements = [
            "campaign_complete", "veteran_complete", "all_secrets_found",
            "all_dataslates_collected", "perfect_missions", "speed_runner"
        ]
        
        weaponAchievements = [
            "weapon_master", "all_weapons_mastered", "flamethrower_specialist",
            "heavy_weapon_expert", "melee_master", "marksman"
        ]
        
        operationsAchievements = [
            "operations_veteran", "ruthless_survivor", "team_player",
            "solo_hero", "speed_demon", "perfectionist"
        ]
        
        eternalWarAchievements = [
            "pvp_veteran", "ranked_warrior", "diamond_rank",
            "win_streak", "clutch_master", "objective_specialist"
        ]
    }
    
    // Currency and resources
    resources = {
        requisition = 999999           // Max currency
        armory_data = 999999          // Max armory currency
        chapter_coins = 999999        // Max chapter currency
        
        // Crafting materials
        materials = {
            adamantium = 9999
            ceramite = 9999
            plasteel = 9999
            promethium = 9999
            sacred_oils = 9999
        }
    }
    
    // Settings and preferences
    gameSettings = {
        difficulty = "veteran"
        autoSave = true
        subtitles = true
        language = "english"
        
        // Accessibility options
        accessibility = {
            colorBlindSupport = false
            largeText = false
            simplifiedUI = false
        }
    }
    
    // Statistics
    statistics = {
        totalPlayTime = 200.0         // 200 hours played
        enemiesKilled = 50000         // Lots of dead xenos
        damageDealt = 10000000        // 10 million damage
        damageTaken = 500000          // 500k damage taken
        revivesPerformed = 1000       // Team player
        objectivesCompleted = 500     // Objective focused
        
        // Weapon-specific stats
        weaponStats = {
            flamethrower_heavy = {
                kills = 5000
                damageDealt = 1000000
                timeUsed = 50.0
                accuracy = 0.95
                headshotPercentage = 0.15
            }
        }
    }
    
    __type = "UserProgressionData"
}

// Auto-unlock everything on game start
GameStartUnlocks = {
    unlockAll = true
    maxLevel = true
    allWeapons = true
    allArmor = true
    allSkins = true
    allAchievements = true
    maxCurrency = true
    
    __type = "GameStartUnlocks"
}
