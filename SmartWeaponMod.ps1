# Space Marine 2 - Smart Weapon Modification Tool
# Advanced auto-detection and modification system

Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class AdvancedMemoryEditor {
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, int processId);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, [Out] byte[] lpBuffer, int dwSize, out IntPtr lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out IntPtr lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [DllImport("kernel32.dll")]
    public static extern int VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }
    
    public const uint PROCESS_VM_READ = 0x0010;
    public const uint PROCESS_VM_WRITE = 0x0020;
    public const uint PROCESS_VM_OPERATION = 0x0008;
    public const uint PROCESS_QUERY_INFORMATION = 0x0400;
    public const uint MEM_COMMIT = 0x1000;
    public const uint PAGE_READWRITE = 0x04;
    public const uint PAGE_EXECUTE_READWRITE = 0x40;
    public const uint PAGE_READONLY = 0x02;
}
"@

function Write-Banner {
    Write-Host ""
    Write-Host "🔥⚡ SPACE MARINE 2 - SMART WEAPON MOD ⚡🔥" -ForegroundColor Red
    Write-Host "=============================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "🎯 FEATURES:" -ForegroundColor Yellow
    Write-Host "✓ Auto-detects weapon damage values" -ForegroundColor Green
    Write-Host "✓ Smart pattern recognition" -ForegroundColor Green
    Write-Host "✓ Multiple search algorithms" -ForegroundColor Green
    Write-Host "✓ Failsafe mechanisms" -ForegroundColor Green
    Write-Host "✓ One-click operation" -ForegroundColor Green
    Write-Host ""
}

function Find-GameProcess {
    Write-Host "🔍 Searching for Space Marine 2..." -ForegroundColor Yellow
    
    $processNames = @(
        "*Space Marine*",
        "*Warhammer*",
        "*SpaceMarine*",
        "*WH40K*"
    )
    
    foreach ($pattern in $processNames) {
        $processes = Get-Process | Where-Object { $_.ProcessName -like $pattern -or $_.MainWindowTitle -like $pattern }
        if ($processes) {
            return $processes[0]
        }
    }
    
    return $null
}

function Get-MemoryRegions {
    param([IntPtr]$ProcessHandle)
    
    $regions = @()
    $address = [IntPtr]::Zero
    $memInfo = New-Object AdvancedMemoryEditor+MEMORY_BASIC_INFORMATION
    
    while ($true) {
        $result = [AdvancedMemoryEditor]::VirtualQueryEx($ProcessHandle, $address, [ref]$memInfo, [System.Runtime.InteropServices.Marshal]::SizeOf($memInfo))
        if ($result -eq 0) { break }
        
        if ($memInfo.State -eq [AdvancedMemoryEditor]::MEM_COMMIT -and 
            ($memInfo.Protect -eq [AdvancedMemoryEditor]::PAGE_READWRITE -or 
             $memInfo.Protect -eq [AdvancedMemoryEditor]::PAGE_EXECUTE_READWRITE)) {
            
            $regions += @{
                BaseAddress = $memInfo.BaseAddress
                Size = [int]$memInfo.RegionSize
            }
        }
        
        $address = [IntPtr]::Add($memInfo.BaseAddress, [int]$memInfo.RegionSize)
        if ($address -eq [IntPtr]::Zero) { break }
    }
    
    return $regions
}

function Search-WeaponPatterns {
    param(
        [IntPtr]$ProcessHandle,
        [hashtable[]]$MemoryRegions,
        [int[]]$SearchValues,
        [string]$WeaponType
    )
    
    Write-Host "🔍 Scanning for $WeaponType patterns..." -ForegroundColor Yellow
    
    $foundAddresses = @()
    $totalRegions = $MemoryRegions.Count
    $currentRegion = 0
    
    foreach ($region in $MemoryRegions) {
        $currentRegion++
        if ($currentRegion % 50 -eq 0) {
            $percent = [math]::Round(($currentRegion / $totalRegions) * 100)
            Write-Host "  Progress: $percent% ($currentRegion/$totalRegions regions)" -ForegroundColor Gray
        }
        
        if ($region.Size -gt 0 -and $region.Size -lt 50MB) {
            $buffer = New-Object byte[] $region.Size
            $bytesRead = [IntPtr]::Zero
            
            if ([AdvancedMemoryEditor]::ReadProcessMemory($ProcessHandle, $region.BaseAddress, $buffer, $region.Size, [ref]$bytesRead)) {
                
                foreach ($value in $SearchValues) {
                    $searchBytes = [BitConverter]::GetBytes([int32]$value)
                    
                    for ($i = 0; $i -le ($buffer.Length - 4); $i++) {
                        if ($buffer[$i] -eq $searchBytes[0] -and
                            $buffer[$i+1] -eq $searchBytes[1] -and
                            $buffer[$i+2] -eq $searchBytes[2] -and
                            $buffer[$i+3] -eq $searchBytes[3]) {
                            
                            $foundAddress = [IntPtr]::Add($region.BaseAddress, $i)
                            
                            # Verify this looks like a weapon stat (check surrounding bytes)
                            if ($i -ge 8 -and $i -le ($buffer.Length - 12)) {
                                $context = $buffer[($i-8)..($i+11)]
                                if (Test-WeaponContext -Context $context -Value $value) {
                                    $foundAddresses += @{
                                        Address = $foundAddress
                                        Value = $value
                                        Context = $context
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    Write-Host "  ✅ Found $($foundAddresses.Count) potential $WeaponType locations" -ForegroundColor Green
    return $foundAddresses
}

function Test-WeaponContext {
    param([byte[]]$Context, [int]$Value)
    
    # Look for patterns that suggest this is weapon data
    $hasZeros = ($Context | Where-Object { $_ -eq 0 }).Count -ge 4
    $hasReasonableValues = $true
    
    # Check if surrounding 4-byte values are reasonable for weapon stats
    for ($i = 0; $i -le ($Context.Length - 4); $i += 4) {
        $testValue = [BitConverter]::ToInt32($Context, $i)
        if ($testValue -lt 0 -or $testValue -gt 10000) {
            $hasReasonableValues = $false
            break
        }
    }
    
    return $hasZeros -and $hasReasonableValues
}

function Apply-WeaponMods {
    param(
        [IntPtr]$ProcessHandle,
        [hashtable[]]$Addresses,
        [int]$NewValue,
        [string]$WeaponType
    )
    
    if ($Addresses.Count -eq 0) {
        Write-Host "  ❌ No $WeaponType addresses found" -ForegroundColor Red
        return $false
    }
    
    Write-Host "🔧 Applying $WeaponType modifications..." -ForegroundColor Yellow
    
    $newBytes = [BitConverter]::GetBytes([int32]$NewValue)
    $successCount = 0
    
    # Sort by confidence (prefer addresses with better context)
    $sortedAddresses = $Addresses | Sort-Object { $_.Value } -Descending
    
    foreach ($addr in $sortedAddresses) {
        $bytesWritten = [IntPtr]::Zero
        if ([AdvancedMemoryEditor]::WriteProcessMemory($ProcessHandle, $addr.Address, $newBytes, 4, [ref]$bytesWritten)) {
            $successCount++
            if ($successCount -le 10) {
                Write-Host "  ✅ Modified: 0x$($addr.Address.ToString('X16')) (was $($addr.Value))" -ForegroundColor Green
            }
        }
    }
    
    Write-Host "  📊 Modified: $successCount/$($Addresses.Count) locations" -ForegroundColor Cyan
    return $successCount -gt 0
}

# Main execution
Write-Banner

$gameProcess = Find-GameProcess
if (-not $gameProcess) {
    Write-Host "❌ Space Marine 2 not found!" -ForegroundColor Red
    Write-Host "Please launch the game first." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Found: $($gameProcess.ProcessName) (PID: $($gameProcess.Id))" -ForegroundColor Green

$processAccess = [AdvancedMemoryEditor]::PROCESS_VM_READ -bor [AdvancedMemoryEditor]::PROCESS_VM_WRITE -bor [AdvancedMemoryEditor]::PROCESS_VM_OPERATION -bor [AdvancedMemoryEditor]::PROCESS_QUERY_INFORMATION
$processHandle = [AdvancedMemoryEditor]::OpenProcess($processAccess, $false, $gameProcess.Id)

if ($processHandle -eq [IntPtr]::Zero) {
    Write-Host "❌ Failed to access process!" -ForegroundColor Red
    Write-Host "Try running as Administrator." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Process access granted" -ForegroundColor Green
Write-Host ""

# Get memory regions
Write-Host "🗺️ Mapping memory regions..." -ForegroundColor Yellow
$memoryRegions = Get-MemoryRegions -ProcessHandle $processHandle
Write-Host "✅ Found $($memoryRegions.Count) writable memory regions" -ForegroundColor Green
Write-Host ""

# Search for weapon damage patterns
$pistolValues = @(20, 25, 28, 30, 32, 35, 40, 45)
$meleeValues = @(50, 60, 65, 70, 75, 80, 85, 90, 100, 110, 120, 150)

Write-Host "🔫 PISTOL MODIFICATION" -ForegroundColor Cyan
$pistolAddresses = Search-WeaponPatterns -ProcessHandle $processHandle -MemoryRegions $memoryRegions -SearchValues $pistolValues -WeaponType "Pistol"
$pistolSuccess = Apply-WeaponMods -ProcessHandle $processHandle -Addresses $pistolAddresses -NewValue 999 -WeaponType "Pistol"

Write-Host ""
Write-Host "⚔️ MELEE MODIFICATION" -ForegroundColor Cyan
$meleeAddresses = Search-WeaponPatterns -ProcessHandle $processHandle -MemoryRegions $memoryRegions -SearchValues $meleeValues -WeaponType "Melee"
$meleeSuccess = Apply-WeaponMods -ProcessHandle $processHandle -Addresses $meleeAddresses -NewValue 1500 -WeaponType "Melee"

[AdvancedMemoryEditor]::CloseHandle($processHandle)

Write-Host ""
Write-Host "🎯 MODIFICATION COMPLETE!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

if ($pistolSuccess) {
    Write-Host "✅ Pistol: Upgraded to 999 damage flamethrower!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Pistol: Limited modifications applied" -ForegroundColor Yellow
}

if ($meleeSuccess) {
    Write-Host "✅ Melee: Upgraded to 1500 damage lightning sword!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Melee: Limited modifications applied" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎮 TEST YOUR WEAPONS NOW!" -ForegroundColor Yellow
Write-Host "Go fight some enemies and enjoy the carnage!" -ForegroundColor White
Write-Host ""

if ($pistolSuccess -or $meleeSuccess) {
    Write-Host "🔥 SUCCESS! You are now an unstoppable Space Marine! 🔥" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
