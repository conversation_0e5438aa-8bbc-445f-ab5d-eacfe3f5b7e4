__request   =   {
   __select   =   {
      __id   =   "root"
      __select   =   {
         __id   =   "tag"
         __where   =   {
            __parentType   =   "^gameplay_tags_base$"
         }
         __onlyChanged   =   False
         __groupByTag   =   "gtag"
         __type   =   "modelRecursive"
      }
      __showOnlyChildren   =   True
      __path   =   "tags"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __family   =   ".gtags"
   __ptr   =   "gtags_project"
   __groupByParents   =   False
   __type   =   "brand"
}
__resultFormatter   =   {
   __targetId   =   "tag"
   __relativeTo   =   "root"
   __tagsToSkip   =   [
      "gtags_list"
   ]
   __separator   =   "."
   __type   =   "rf_model"
}
__type   =   "linkBrowser"

