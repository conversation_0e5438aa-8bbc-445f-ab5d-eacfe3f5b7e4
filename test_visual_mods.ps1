# Space Marine 2 - Test Visual Modifications
# This script verifies all modifications are in place

Write-Host "Testing Visual Modifications..." -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Check hex modifications
$gameFile = "client_pc\root\paks\client\default\default.pak"
if (Test-Path $gameFile) {
    $fileSize = (Get-Item $gameFile).Length
    Write-Host "Game file: $([math]::Round($fileSize / 1GB, 2)) GB" -ForegroundColor Cyan
    
    # Check for backup files
    $backups = Get-ChildItem -Path "client_pc\root\paks\client\default" -Filter "*.backup"
    if ($backups.Count -gt 0) {
        Write-Host "Backup files found: $($backups.Count)" -ForegroundColor Green
        foreach ($backup in $backups) {
            Write-Host "  - $($backup.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "No backup files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "Game file not found!" -ForegroundColor Red
}

Write-Host ""

# Check SSL files
$sslFiles = @(
    "client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso",
    "client_pc\root\local\ssl\weapons\lightning_sword_complete.sso"
)

Write-Host "SSL Visual Files:" -ForegroundColor Cyan
foreach ($file in $sslFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "  OK $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Red
    }
}

Write-Host ""

# Check mod files
Write-Host "Mod Directory:" -ForegroundColor Cyan
$modFiles = Get-ChildItem -Path "client_pc\root\mods" -ErrorAction SilentlyContinue
if ($modFiles) {
    foreach ($file in $modFiles) {
        Write-Host "  - $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "  No mod files found" -ForegroundColor Yellow
}

Write-Host ""

# Check ModEditor files
Write-Host "ModEditor Status:" -ForegroundColor Cyan
if (Test-Path "ModEditor") {
    $modEditorFiles = Get-ChildItem -Path "ModEditor\mods_source\ssl\weapons" -ErrorAction SilentlyContinue
    if ($modEditorFiles) {
        foreach ($file in $modEditorFiles) {
            Write-Host "  OK $($file.Name)" -ForegroundColor Green
        }
    } else {
        Write-Host "  No ModEditor SSL files" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ModEditor not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "TESTING INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. Start any mission (Campaign/Operations)" -ForegroundColor White
Write-Host "3. Test Pistol:" -ForegroundColor Cyan
Write-Host "   - Equip pistol (key 2 or Q)" -ForegroundColor Gray
Write-Host "   - Fire at enemies" -ForegroundColor Gray
Write-Host "   - Look for: High damage numbers (999+)" -ForegroundColor Gray
Write-Host "   - Look for: Flame effects, burning enemies" -ForegroundColor Gray
Write-Host "4. Test Melee:" -ForegroundColor Cyan
Write-Host "   - Equip chainsword/melee weapon" -ForegroundColor Gray
Write-Host "   - Attack enemies" -ForegroundColor Gray
Write-Host "   - Look for: High damage numbers (1500+)" -ForegroundColor Gray
Write-Host "   - Look for: Lightning effects, electrical arcs" -ForegroundColor Gray
Write-Host ""
Write-Host "EXPECTED RESULTS:" -ForegroundColor Green
Write-Host "- Pistol does 999+ damage with flame visuals" -ForegroundColor White
Write-Host "- Melee does 1500+ damage with lightning visuals" -ForegroundColor White
Write-Host "- Enemies die in 1-2 hits" -ForegroundColor White
Write-Host "- Visual effects appear during combat" -ForegroundColor White
Write-Host ""
Write-Host "If damage is high but no visual effects:" -ForegroundColor Yellow
Write-Host "- Hex modifications worked (damage boost)" -ForegroundColor White
Write-Host "- SSL modifications need different approach" -ForegroundColor White
Write-Host ""
Write-Host "Ready to test! Launch Space Marine 2 now." -ForegroundColor Green
