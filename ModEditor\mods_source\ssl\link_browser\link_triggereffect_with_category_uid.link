__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "triggereffect"
         __where   =   {
            __type   =   ""
         }
         __path   =   "/*"
         __type   =   "model"
      }
      __path   =   "catalogTriggerEffects/*"
      __type   =   "model"
   }
   __where   =   {
      __name   =   "TriggerEffectPresetProjectList"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{triggereffect}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

