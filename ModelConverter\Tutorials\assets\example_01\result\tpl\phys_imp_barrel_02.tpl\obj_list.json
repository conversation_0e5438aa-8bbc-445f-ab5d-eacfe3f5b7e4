{"name": "phys_imp_barrel_02", "engine_name": "", "geom": false, "anim": false, "local_transform": [0.0, 0.0, 0.0], "world_transform": [0.0, 0.0, 0.0], "children": [{"name": "barrel_body", "engine_name": "barrel_body", "geom": false, "anim": false, "local_transform": [0.0, 0.5470287203788757, 0.0], "world_transform": [0.0, 0.5470287203788757, 0.0], "children": [{"name": "barrel_glr", "engine_name": "barrel_glr", "geom": true, "anim": false, "local_transform": [0.0, -0.012007713317871094, 0.0], "world_transform": [0.0, 0.5350210070610046, 0.0], "children": []}, {"name": "barrel_mesh", "engine_name": "barrel_mesh", "geom": true, "anim": false, "local_transform": [0.0, 0.0, 0.0], "world_transform": [0.0, 0.5470287203788757, 0.0], "children": [{"name": "barrel_mesh_lod1", "engine_name": "barrel_mesh_lod1", "geom": true, "anim": false, "local_transform": [0.0, 0.0, 0.0], "world_transform": [0.0, 0.5470287203788757, 0.0], "children": []}, {"name": "barrel_mesh_lod2", "engine_name": "barrel_mesh_lod2", "geom": true, "anim": false, "local_transform": [0.0, 0.0, 0.0], "world_transform": [0.0, 0.5470287203788757, 0.0], "children": []}]}, {"name": "squarrel", "engine_name": "squarrel", "geom": true, "anim": false, "local_transform": [0.0, -0.5470287203788757, 0.0], "world_transform": [0.0, 0.0, 0.0], "children": []}]}], "engine_objects": {"id": 0, "name": "", "internal_name": ".phys_imp_barrel_02.h", "usf_node_path": "|phys_imp_barrel_02", "affixes": "", "geom": false, "splits": 0, "vertices": 0, "faces": 0, "materials": 0, "split_info": [], "material_info": [], "axial_bounding_box": "()", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": false, "lod_level": -1, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": [{"id": 1, "name": "barrel_body", "internal_name": "barrel_body", "usf_node_path": "|phys_imp_barrel_02|barrel_body", "affixes": "cdt_off_all\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nvisibility_hidden\npreserveMaterial\n0\n\n", "geom": false, "splits": 0, "vertices": 0, "faces": 0, "materials": 0, "split_info": [], "material_info": [], "axial_bounding_box": "()", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": false, "lod_level": -1, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": [{"id": 2, "name": "barrel_glr", "internal_name": "barrel_glr", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_glr", "affixes": "export_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\n", "geom": true, "splits": 1, "vertices": 22, "faces": 40, "materials": 1, "split_info": [{"vertices": 22, "faces": 40, "material": "Material_0", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "Material_0", "maya_name": "Material_0", "uv_sets": 1}], "axial_bounding_box": "((-0.371, -0.539, -0.353), (0.371, 0.539, 0.353))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": false, "lod_level": -1, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}, {"id": 3, "name": "barrel_mesh", "internal_name": "barrel_mesh", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_mesh", "affixes": "ai_nav_mesh_off\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nlod_compex_level\n\n", "geom": true, "splits": 1, "vertices": 1809, "faces": 2974, "materials": 1, "split_info": [{"vertices": 1809, "faces": 2974, "material": "imp_barrel_02", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "imp_barrel_02", "maya_name": "imp_barrel_02_mat0", "uv_sets": 1}], "axial_bounding_box": "((-0.381, -0.551, -0.381), (0.381, 0.551, 0.381))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": true, "lod_level": 0, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": [{"id": 4, "name": "barrel_mesh_lod1", "internal_name": "barrel_mesh_lod1", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_mesh|barrel_mesh_lod1", "affixes": "ai_nav_mesh_off\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nlod_compex_level\n\n", "geom": true, "splits": 1, "vertices": 1007, "faces": 1484, "materials": 1, "split_info": [{"vertices": 1007, "faces": 1484, "material": "imp_barrel_02", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "imp_barrel_02", "maya_name": "imp_barrel_02_mat0", "uv_sets": 1}], "axial_bounding_box": "((-0.381, -0.552, -0.382), (0.382, 0.551, 0.382))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": true, "lod_level": 1, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}, {"id": 5, "name": "barrel_mesh_lod2", "internal_name": "barrel_mesh_lod2", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_mesh|barrel_mesh_lod2", "affixes": "ai_nav_mesh_off\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\nlod_compex_level\n\n", "geom": true, "splits": 1, "vertices": 571, "faces": 742, "materials": 1, "split_info": [{"vertices": 571, "faces": 742, "material": "imp_barrel_02", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "imp_barrel_02", "maya_name": "imp_barrel_02_mat0", "uv_sets": 1}], "axial_bounding_box": "((-0.387, -0.555, -0.383), (0.387, 0.552, 0.383))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": true, "lod_level": 2, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}, {"id": 6, "name": "", "internal_name": ".barrel_mesh_lod3.b", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_mesh|barrel_mesh_lod3", "affixes": "cdt_off_all\n\nlod_compex_level\n\n", "geom": true, "splits": 1, "vertices": 267, "faces": 336, "materials": 1, "split_info": [{"vertices": 267, "faces": 336, "material": "imp_barrel_02", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "imp_barrel_02", "maya_name": "imp_barrel_02_mat0", "uv_sets": 1}], "axial_bounding_box": "((-0.375, -0.551, -0.375), (0.375, 0.541, 0.375))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": true, "lod_level": 3, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}, {"id": 7, "name": "", "internal_name": ".barrel_mesh_lod4.j", "usf_node_path": "|phys_imp_barrel_02|barrel_body|barrel_mesh|barrel_mesh_lod4", "affixes": "cdt_off_all\n\nlod_compex_level\n\n", "geom": true, "splits": 1, "vertices": 70, "faces": 60, "materials": 1, "split_info": [{"vertices": 70, "faces": 60, "material": "imp_barrel_02", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "imp_barrel_02", "maya_name": "imp_barrel_02_mat0", "uv_sets": 1}], "axial_bounding_box": "((-0.379, 0.000, -0.379), (0.379, 1.074, 0.379))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": true, "lod_level": 4, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}]}, {"id": 8, "name": "squarrel", "internal_name": "squarrel", "usf_node_path": "|phys_imp_barrel_02|barrel_body|squarrel", "affixes": "ai_nav_mesh_off\n\nexport_preserve_geometry\nuse_hierarchy_name\n0\n\nexport_preserve_position\n\n", "geom": true, "splits": 1, "vertices": 24, "faces": 12, "materials": 1, "split_info": [{"vertices": 24, "faces": 12, "material": "test_squarrel", "skin_compound_id": -1}], "material_info": [{"name": "default", "texture_name": "test_squarrel", "maya_name": "test_squarrel", "uv_sets": 1}], "axial_bounding_box": "((-0.396, 0.003, -0.396), (0.396, 1.131, 0.396))", "skin_compound": false, "bone": false, "skin_compound_bone": false, "lod": false, "lod_level": -1, "anim": false, "related_skin_compound_id": -1, "skin_compound_sources": [], "skin_compound_bones": [], "children": []}]}]}}