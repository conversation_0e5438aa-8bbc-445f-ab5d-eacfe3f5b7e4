1) Put the contents of this archive to "client_pc/root/"
2) Extract default_other.pak from client and server to mods_source folder. Otherwise resournces won't generate correctly. "Replace" or "Skip" all duplicate conflicts.
3) Launch tools\ModEditor\IntegrationStudio.exe
4) You can edit any cls/sso file in the mods_source folder.
5) IntegrationStudio will automatically generate any required resource files
6) After editing your files, bring them over from "mods_source" to "local" to test them out. Keep the folder hierarchy the same, e.g. "mods_source\ssl\game_mode\missions_lib\missions_library.sso" to "local\ssl\game_mode\missions_lib\missions_library.sso". Also copy autogenerated resources