__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __id   =   ""
               __select   =   {
                  __id   =   "perkName"
                  __format   =   "{value}"
                  __showValue   =   True
                  __path   =   "perkUid"
                  __type   =   "model"
               }
               __showOnlyChildren   =   True
               __format   =   "{value}"
               __showValue   =   True
               __path   =   "/*"
               __type   =   "model"
            }
            __showOnlyChildren   =   True
            __format   =   "{name}"
            __path   =   "perkLayers/*"
            __type   =   "model"
         }
         __format   =   "{name}"
         __path   =   "tiers/*"
         __type   =   "model"
      }
      __showOnlyChildren   =   False
      __path   =   "weapons/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "WeaponPerksProgressionLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{perkName}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

