scope:
  usfexport:
    res3-enable: true
    project-name: 'Space Marine II'
    ps-dir:
      - $(project-dir)/ps
      - $(project-dir)/ps
    use-pses-file: false
    database-dir: models
    affixes-dir: $(project-dir)/ps
    common-maya-saber-plugins: $(project-dir)/ps
    meta-desc-dir: $(project-dir)/ps
  resource-converter:
    res3-enable: true
    project-name: 'Space Marine II'
    ps-dir:
      - $(project-dir)/ps
      - $(project-dir)/ps
    meta-desc-dir: $(project-dir)/ps

    converters-config-name: convert.cfg
    cdt_triangle_warning_cutoff: 20000
    ai_triangle_warning_cutoff: 5000
    resource-layers:
      - $(project-dir)/resources
      - $(project-dir)/assets
  pct-resource-gen:
    res3-enable: true
    project-name: 'Space Marine II'
    shader-descs-dir: $(project-dir)/ps/shader_descs
  
    td-defaults-tag: res://td_defaults/td_defaults.resource
    material-templates-tag: res://material_templates/material_templates.resource
    
    dir-groups:
      - pct-dir:  $(project-dir)/resources/pct
        pct-res-dir: $(project-dir)/resources/pct
        td-dir: $(project-dir)/assets/td

    layers:
      - $(project-dir)/resources
      - $(project-dir)/assets

    texture-types:
      - diff
      - nm
      - det
      - spec
      - hdetm
      - br
      - ref
      - lmdir
      - lmdif
      - lmdifdir
      - rnm0
      - rnm1
      - rnm2
      - ao
      - dir
      - dircolor
    
    td-extra-pct-links-suffixes:
      - nm
      - spec
      - det
      - cube
      - em
      - hdetm

  