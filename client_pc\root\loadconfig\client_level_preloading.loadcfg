steps:
  - RES3_SCENE_SOUND_BANKS
  - RES3_SCENE_MML_CFG
  - RES3_SCENE_SDR_PRESETS
  - RES3_SCENE_ADAPTER_LOADING
  - RES3_SCENE_TPL
  - RES3_SCENE_TPL_MARKUP
  - RES3_SCENE_SOUND_EVENTS
  - RES3_SCENE_ALL
  

functions:
  - name: finalize_sound_events_loading
    on:
      - step: RES3_SCENE_SOUND_EVENTS

  - name: adapter_loading
    on:
      - step: RES3_SCENE_ADAPTER_LOADING

rules:
  - name: load scene sound banks
    res_type: res_desc_sound_bank
    step: RES3_SCENE_SOUND_BANKS

  #
  # tpl descs
  #
  - name: process scene tpl descs
    res_type: res_desc_tpl
    step: RES3_SCENE_TPL

  #
  # tpl_markup descs
  #
  - name: process scene tpl_markup descs
    res_type: res_desc_tpl_markup
    step: RES3_SCENE_TPL_MARKUP

  #
  # ssl descs
  #
  - name: disable scene ssl
    res_type: res_desc_ssl
    exclude: true

  - name: disable scene ssolib
    res_type: res_desc_ssolib
    exclude: true

  - name: disable scene sso
    res_type: res_desc_sso
    exclude: true

  - name: disable scene cls
    res_type: res_desc_cls
    exclude: true

  - name: disable scene prop
    res_type: res_desc_prop
    exclude: true

  - name: disable scene prefab
    res_type: res_prefab_desc
    exclude: true

  - name: disable scene prefab asset
    res_type: res_desc_prefab_asset
    exclude: true

  #
  # basic descs
  #
  - name: disable scene basic descs
    res_type: res_basic_ssl_desc
    exclude: true

  # scene mml cfg
  - name: process scene mml cfg
    res_type: res_desc_mml_cfg
    step: RES3_SCENE_MML_CFG

  # steps RES3_SCENE_MATERIALS_TEMPLATES, RES3_SCENE_SDR_PRESETS, RES3_SCENE_TD_DEFAULTS
  # and RES3_SCENE_PCT later must be added to RES3_SCENE_ALL

  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: RES3_SCENE_SDR_PRESETS

  # step RES3_SCENE_SOUND_EVENTS should be executed after all sound banks load
  # in case of async bank loading actual bank will be actually loaded with some delay after RES3_SCENE_SOUND_BANKS step

  - name: process sound media bundles
    res_type: res_desc_sound_media_bundle
    step: RES3_SCENE_SOUND_EVENTS

  - name: process sound events
    res_type: res_desc_sound_event
    step: RES3_SCENE_SOUND_EVENTS

  #
  # other
  #
  - name: load all other RES3_SCENE resources
    use_default_loader: true
    step: RES3_SCENE_ALL
