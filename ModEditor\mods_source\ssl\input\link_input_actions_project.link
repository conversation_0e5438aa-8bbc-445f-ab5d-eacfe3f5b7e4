__request   =   {
   __select   =   {
      __id   =   "layer"
      __select   =   {
         __id   =   "final"
         __format   =   "{name}"
         __where   =   {
            __parentType   =   "^InputActionBase$"
         }
         __onlyChanged   =   False
         __groupByParent   =   2
         __type   =   "modelRecursive"
      }
      __format   =   "{name}"
      __path   =   "layers/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "Keymap"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{layer}.{final}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

