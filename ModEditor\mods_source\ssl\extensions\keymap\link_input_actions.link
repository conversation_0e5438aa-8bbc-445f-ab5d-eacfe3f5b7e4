__request   =   {
   __select   =   {
      __id   =   "layer"
      __select   =   {
         __id   =   "final"
         __format   =   "{name}"
         __where   =   {
            __parentType   =   "^InputActionBase$"
         }
         __onlyChanged   =   False
         __groupByParent   =   2
         __type   =   "modelRecursive"
      }
      __format   =   "{name}"
      __path   =   "layers/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "(keymap|keymapCommon|keymapWwz)"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{layer}.{final}"
   __type   =   "rf_text"
}
__addEmpty   =   {
   __value   =   False
}
__type   =   "linkBrowser"

