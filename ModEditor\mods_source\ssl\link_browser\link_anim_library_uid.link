__request   =   {
   __select   =   {
      __id   =   ""
      __select   =   {
         __id   =   "cat"
         __select   =   {
            __id   =   "anim"
            __where   =   {
               __type   =   ""
            }
            __path   =   "animations/*"
            __type   =   "model"
         }
         __type   =   "modelRecursive"
      }
      __format   =   "{name}"
      __where   =   {
         __name   =   "Categories"
         __type   =   ""
         __parentType   =   ""
      }
      __type   =   "modelRecursive"
   }
   __showOnlyChildren   =   False
   __family   =   ".ssl"
   __where   =   {
      __name   =   "AnimationLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{cat}.{anim}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

