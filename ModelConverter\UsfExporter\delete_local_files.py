import os
import glob
import shutil

CWD = os.path.dirname(os.path.realpath(__file__))

PROJECT_DIR = os.path.join(CWD, 'project')

ASSETS_DIR = os.path.join(PROJECT_DIR, 'assets')
PCT_DIR = os.path.join(PROJECT_DIR, 'resources', 'pct')
TPL_DIR = os.path.join(PROJECT_DIR, 'resources', 'tpl')
SANDBOX_DIR = os.path.join(PROJECT_DIR, 'sandbox')
REFRT_MASK = os.path.join(CWD, 'examples', 'models', '*.s3drefrt')


if __name__ == '__main__':
    shutil.rmtree(ASSETS_DIR, ignore_errors=True)
    shutil.rmtree(PCT_DIR, ignore_errors=True)
    shutil.rmtree(TPL_DIR, ignore_errors=True)
    shutil.rmtree(SANDBOX_DIR, ignore_errors=True)

    for file in glob.glob(REFRT_MASK):
        os.remove(file)
