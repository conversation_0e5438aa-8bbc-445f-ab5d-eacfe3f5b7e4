# Space Marine 2 - Weapon Data Search Script
# This script searches for weapon damage values in game files

Write-Host "Space Marine 2 Weapon Data Search" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Function to search binary data for patterns
function Search-BinaryData {
    param(
        [string]$FilePath,
        [string[]]$SearchPatterns,
        [int]$MaxBytes = 1000000
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return
    }
    
    Write-Host "Searching: $FilePath" -ForegroundColor Yellow
    
    try {
        # Read binary data
        $bytes = Get-Content $FilePath -Encoding Byte -TotalCount $MaxBytes
        $text = [System.Text.Encoding]::ASCII.GetString($bytes)
        
        # Search for patterns
        foreach ($pattern in $SearchPatterns) {
            $matches = $text | Select-String -Pattern $pattern -AllMatches
            if ($matches) {
                Write-Host "  Found '$pattern':" -ForegroundColor Green
                foreach ($match in $matches.Matches) {
                    $start = [Math]::Max(0, $match.Index - 50)
                    $length = [Math]::Min(100, $text.Length - $start)
                    $context = $text.Substring($start, $length)
                    Write-Host "    Context: $context" -ForegroundColor Gray
                }
            }
        }
        
        # Search for common damage values (as hex)
        $damageValues = @(25, 30, 35, 40, 45, 50, 75, 100)
        foreach ($damage in $damageValues) {
            # Little-endian 32-bit integer
            $hexPattern = [BitConverter]::GetBytes([int32]$damage)
            $hexString = [BitConverter]::ToString($hexPattern).Replace("-", "")
            
            # Search for this hex pattern in the bytes
            for ($i = 0; $i -lt ($bytes.Length - 3); $i++) {
                if ($bytes[$i] -eq $hexPattern[0] -and 
                    $bytes[$i+1] -eq $hexPattern[1] -and 
                    $bytes[$i+2] -eq $hexPattern[2] -and 
                    $bytes[$i+3] -eq $hexPattern[3]) {
                    Write-Host "  Found damage value $damage at offset 0x$($i.ToString('X8'))" -ForegroundColor Cyan
                }
            }
        }
        
    } catch {
        Write-Warning "Error reading file: $_"
    }
}

# Search patterns
$weaponPatterns = @(
    "bolt.*pistol",
    "pistol.*damage",
    "weapon.*damage",
    "damage.*base",
    "firearm",
    "ammunition",
    "bolt_pistol",
    "heavy_bolter"
)

Write-Host "Searching for weapon data..." -ForegroundColor Yellow

# Search PAK files
$pakFiles = Get-ChildItem -Path "client_pc\root\paks" -Recurse -Filter "*.pak" | Select-Object -First 3
foreach ($pak in $pakFiles) {
    Search-BinaryData -FilePath $pak.FullName -SearchPatterns $weaponPatterns -MaxBytes 500000
}

# Search SSL files in ModEditor
$sslFiles = Get-ChildItem -Path "ModEditor\mods_source\ssl" -Recurse -Filter "*.sso" -ErrorAction SilentlyContinue
foreach ($ssl in $sslFiles) {
    Search-BinaryData -FilePath $ssl.FullName -SearchPatterns $weaponPatterns -MaxBytes 100000
}

# Search local mod files
$localFiles = Get-ChildItem -Path "client_pc\root\local" -Recurse -Filter "*.sso" -ErrorAction SilentlyContinue
foreach ($local in $localFiles) {
    Search-BinaryData -FilePath $local.FullName -SearchPatterns $weaponPatterns -MaxBytes 100000
}

Write-Host ""
Write-Host "MANUAL HEX SEARCH INSTRUCTIONS:" -ForegroundColor Cyan
Write-Host "1. Download HxD hex editor (free)" -ForegroundColor White
Write-Host "2. Open these files in HxD:" -ForegroundColor White
Write-Host "   - client_pc\root\paks\client\resources.pak" -ForegroundColor Gray
Write-Host "   - C:\Program Files (x86)\Steam\userdata\1758870712\2183900\remotecache.vdf" -ForegroundColor Gray
Write-Host "3. Search for these hex values (little-endian):" -ForegroundColor White
Write-Host "   - 19 00 00 00 (decimal 25)" -ForegroundColor Gray
Write-Host "   - 1E 00 00 00 (decimal 30)" -ForegroundColor Gray
Write-Host "   - 23 00 00 00 (decimal 35)" -ForegroundColor Gray
Write-Host "   - 32 00 00 00 (decimal 50)" -ForegroundColor Gray
Write-Host "4. Search for text strings:" -ForegroundColor White
Write-Host "   - 'bolt_pistol'" -ForegroundColor Gray
Write-Host "   - 'damage'" -ForegroundColor Gray
Write-Host "   - 'weapon'" -ForegroundColor Gray
Write-Host "5. When you find damage values, change them to:" -ForegroundColor White
Write-Host "   - E7 03 00 00 (decimal 999)" -ForegroundColor Gray
Write-Host "   - 0F 27 00 00 (decimal 9999)" -ForegroundColor Gray
Write-Host ""
Write-Host "COMMON DAMAGE VALUE LOCATIONS:" -ForegroundColor Yellow
Write-Host "- Weapon definition files (.sso, .ssl)" -ForegroundColor White
Write-Host "- Configuration files (.cfg, .ini)" -ForegroundColor White
Write-Host "- Save game data (Steam userdata)" -ForegroundColor White
Write-Host "- Memory dumps (while game is running)" -ForegroundColor White
Write-Host ""
Write-Host "Search completed!" -ForegroundColor Green
