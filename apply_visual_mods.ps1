# Space Marine 2 - Apply Visual Mods
# This script applies the SSL visual modifications

Write-Host "Applying Visual Weapon Modifications..." -ForegroundColor Green

# Ensure SSL directories exist
$sslDir = "client_pc\root\local\ssl\weapons"
if (-not (Test-Path $sslDir)) {
    New-Item -ItemType Directory -Path $sslDir -Force | Out-Null
    Write-Host "Created SSL directory: $sslDir" -ForegroundColor Yellow
}

# Copy SSL files to proper locations
$sourceFiles = @(
    "client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso",
    "client_pc\root\local\ssl\weapons\lightning_sword_complete.sso"
)

foreach ($file in $sourceFiles) {
    if (Test-Path $file) {
        Write-Host "SSL file ready: $file" -ForegroundColor Green
    } else {
        Write-Warning "SSL file missing: $file"
    }
}

# Also copy to ModEditor if it exists
$modEditorDir = "ModEditor\mods_source\ssl\weapons"
if (Test-Path "ModEditor") {
    if (-not (Test-Path $modEditorDir)) {
        New-Item -ItemType Directory -Path $modEditorDir -Force | Out-Null
    }
    
    foreach ($file in $sourceFiles) {
        if (Test-Path $file) {
            $fileName = Split-Path $file -Leaf
            Copy-Item $file "$modEditorDir\$fileName" -Force
            Write-Host "Copied to ModEditor: $fileName" -ForegroundColor Cyan
        }
    }
}

Write-Host ""
Write-Host "Visual modifications applied!" -ForegroundColor Green
Write-Host "Launch Space Marine 2 to test the weapons." -ForegroundColor Yellow
