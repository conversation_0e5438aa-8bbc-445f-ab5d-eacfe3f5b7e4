# for loading all needed in ShaderDataConverter except tpl (they will be loaded using default loadcfg)

steps:
  - SDR_PRESETS
  - MATERIAL_TEMPLATES
  - TD_DEFAULTS
  - TD
  - PCT
  - ADAPTER_LOADING
  - LOAD
  - EXCLUDE

functions:
  - name: adapter_loading
    on:
      - step: ADAPTER_LOADING      

rules:
  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: SDR_PRESETS

  - name: load material_templates
    res_type: res_desc_material_templates
    step: MATERIAL_TEMPLATES

  - name: load td_defaults
    res_type: res_desc_td_defaults
    step: TD_DEFAULTS

  - name: load td
    res_type: res_desc_td
    step: TD

  - name: load pct
    res_type: res_desc_pct
    on:
      - mask: 'res://pct/*'
    step: PCT

  - name: load scene
    res_type: res_desc_scene
    step: LOAD

  - name: exclude all other
    exclude: true