albedo_tint = [
 255,
 255,
 255,
 255
]
blending = {
 heightmap = {
    colorSetIdx = -1
    invert = 0
    softnessMultiplier = 1.0
    useHeightmapBlending = 0
 }
 normalAdditiveBlend = 0
 upVector = {
    angle = 90.0
    enabled = 0
    falloff = 0.0
 }
 weights = {
    colorSetIdx = -1
    multiplier = 1.0
    useFromLayerAlpha = 0
    useFromMaskChannel = -1
 }
}
heightmapOverride = ""
heightmapUVOverride = {
 enabled = 0
 tilingU = 1.0
 tilingV = 1.0
 uvSetIdx = -1
}
isVisible = 1
subMaterial = ""
textureName = ""
tilingU = 1.0
tilingV = 1.0
type = 0
uvSetIdx = 0