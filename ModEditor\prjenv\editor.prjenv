integration-studio-2-dir: $(tools-dir)/ModEditor
integration-studio-2-exe: $(integration-studio-2-dir)/IntegrationStudio.exe

meta-desc-ssl-override-path: $(meta-desc-dir)/meta_desc_ssl_overrides.ps
meta-desc-ssl-override-common-path: $(integration-studio-2-dir)/meta_desc_ssl_overrides.ps

integration-studio-roots:
   - $(mods-dir)

scope:
  integration-studio:  
    cls-paks: $(paks-with-descs)
  
    res3-disable: false # for tests
    packages-dirs: $(resource-tools/packages-dirs)
    packages-list-file: $(resource-tools/packages-list-file)
    ssl-dirs: $(ssl-dirs)
    project-name: $(project-name)
    integration-studio-2-dir: $(integration-studio-2-dir)
    project-dir: $(project-dir)
    tools-dir: $(tools-dir)
    roots: $(integration-studio-roots)
    assets-roots: []
    ssl-include-dirs: $(ssl-include-dirs)
    project-assets-dir: $(project-assets-dir)

    resource-meta-desc-path: $(meta-desc-path)
    meta-desc-dir: $(meta-desc-dir)
    meta-desc-path: $(meta-desc-path)
    meta-desc-ssl-path: $(meta-desc-ssl-path)
    meta-desc-ssl-user-path: $(meta-desc-ssl-user-path)
    meta-desc-ssl-override-path: $(meta-desc-ssl-override-path)
    meta-desc-ssl-override-common-path: $(meta-desc-ssl-override-common-path)

    resource-layers: $(resource-tools/layers)
    typemask-ps: $(meta-desc-dir)/typemask.ps
    logs-default-target: ~profile-dir/logs/all.txt
    ps-files: []
    ps-file-formats: {}
    content-tags-files: []
    icon-set-names:
      flat: icon_sets_flat.json