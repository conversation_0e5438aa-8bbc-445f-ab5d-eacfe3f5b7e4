mapping = {
   lod_bias = 0.000000
   anisotropy = 0.000000
   address_u = "wrap"
   address_v = "wrap"
}
rendering = {
   use_akill = false
   akill_ref = 127
   linear_rgb = false
   sm_hi = false
   detail_density = 0.000000
   detail_scale = 1.000000
   hdr_scale = -1.000000
}
isUltraHiRes = false
version = 1
usage = "MSCRGHAO"
convert_settings = {
   resample = 0
   mipmap_level = 12
   format_name = "MSCRGHAO"
   format_descr = "Metalness(r)+Roughness(g)+AO(b)"
   uncompressed_flag = false
   force_use_oxt1_flag = false
   fp16 = false
   fade_flag = false
   fade_begin = 0
   fade_end = 0
   fade_mask = {
      R = true
      G = true
      B = true
      A = false
   }
   color = {
      R = 127.000000
      G = 127.000000
      B = 127.000000
      A = 0.000000
   }
   fade_rough_flag = false
   fade_rough_mip_bot = 0.000000
   fade_rough_mip_top = 1.000000
   fade_rough_top_min = 0.500000
   filter = 0
   sharpen = 1
   compressionQuality = 3
   m_akill_ref = 127
   m_akill_thick = 0
   auto_mipmap_brightness_param = 127
}
