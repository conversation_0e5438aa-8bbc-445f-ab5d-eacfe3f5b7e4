__request   =   {
   __select   =   {
      __select   =   {
         __where   =   {
            __type   =   "mr_control_param(?:_ref)?_bool"
         }
         __onlyChanged   =   False
         __type   =   "modelRecursive"
      }
      __showOnlyChildren   =   True
      __path   =   "controlParams/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __modelType   =   "mr_network"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

