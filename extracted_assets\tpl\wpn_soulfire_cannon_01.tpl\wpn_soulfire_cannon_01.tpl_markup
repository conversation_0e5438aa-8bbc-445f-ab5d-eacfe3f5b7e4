objectInfos   =   [
   {
      entities   =   [
         {
            name   =   "muzzle"
            localTransform   =   {
               position   =   [
                  0,
                  0.0466172,
                  1.004195
               ]
            }
            __type   =   "tpl_markup_locator"
         }
      ]
      objectId   =   {
         name   =   "base_01"
      }
   },
   {
      properties   =   [
         {
            massFromDensity   =   False
            mass   =   200
            friction   =   0.99
            __type   =   "tpl_markup_object_property_physics"
         },
         {
            __type   =   "tpl_markup_object_shape_property_physics"
         }
      ]
      objectId   =   {
         name   =   "rb_wpn"
      }
   }
]
version   =   1
lodInfo   =   {
   hideDistance   =   150
   maxLodDist   =   [
      5,
      10,
      15,
      20
   ]
}
rendParams   =   {
   allowDissolve   =   True
   allowFresnelHighlight   =   True
   allowCovering   =   True
   allowTransparencyMaskModificationForSmearing   =   True
}
mtlMutators   =   [
   "camo"
]
__type   =   "tpl_markup"
