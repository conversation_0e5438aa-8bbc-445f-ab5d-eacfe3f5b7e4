#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	# Use the embedded font.
};
bugreport:
{
	btn_continue = "Onine gaan om een oplossing te zoeken en het spel afsluiten.";
	btn_exit = "Het spel afsluiten.";
	btn_hidedetails = "Details verbergen";
	btn_showdetails = "Details tonen";
	chk_sendreport = "Foutrapport versturen";
	error_code = "Foutcode:";
	lbl_body1 = "Het spijt ons, we konden je spel niet opstarten";
	lbl_body2 = "Help ons door dit probleem te rapporteren.";
	lbl_body3 = "Easy Anti-Cheat kan online controleren of er een oplossing voor het probleem bestaat en proberen het probleem te verhelpen.";
	lbl_header = "Het spel kon niet worden opgestart";
	title = "Opstartfout";
};
game_error:
{
	error_catalogue_corrupted = "Corrupte Easy Anti-Cheat Hash Catalogue";
	error_catalogue_not_found = "EAC-index niet gevonden";
	error_certificate_revoked = "Certificaat voor EAC-index ingetrokken";
	error_corrupted_memory = "Beschadigd geheugen";
	error_corrupted_network = "Beschadigde pakketstroom";
	error_file_forbidden = "Onbekend spelbestand";
	error_file_not_found = "Vereist bestand ontbreekt";
	error_file_version = "Onbekende bestandsversie";
	error_module_forbidden = "Verboden module";
	error_system_configuration = "Verboden systeemconfiguratie";
	error_system_version = "Niet-vertrouwd spelbestand";
	error_tool_forbidden = "Verboden tool";
	error_violation = "Interne anti-cheatfout";
	error_virtual = "Kan niet worden uitgevoerd op een virtuele machine.";
	peer_client_banned = "Anti-cheatpeer geblokkeerd.";
	peer_heartbeat_rejected = "Anti-cheatpeer afgewezen.";
	peer_validated = "Anti-cheatpeervalidatie voltooid.";
	peer_validation_failed = "Anti-cheatpeervalidatie mislukt.";
	executable_not_hashed = "Het was niet mogelijk om het uitvoerbare bestand voor het spel in de catalogus te vinden.";
};
launcher:
{
	btn_cancel = "Annuleren";
	btn_exit = "Afsluiten";
	error_cancel = "Opstarten geannuleerd";
	error_filenotfound = "Bestand niet gevonden";
	error_init = "Initialisatiefout";
	error_install = "Installatiefout";
	error_launch = "Opstartfout";
	error_nolib = "De Easy Anti-Cheat-bibliotheek kon niet worden geladen";
	loading = "LADEN";
	wait = "Een ogenblik geduld";
	initializing = "OPSTARTEN";
	success_waiting_for_game = "AAN HET WACHTEN OP SPEL";
	success_closing = "Geslaagd";
	network_error = "Netwerkfout";
	error_no_settings_file = "{0} niet gevonden";
	error_invalid_settings_format = "{0} heeft geen geldig JSON-formaat";
	error_missing_required_field = "{0} mist een verplicht veld ({1})";
	error_invalid_eos_identifier = "{0} bevat een ongeldige EOS-ID: ({1})";
	download_progress = "Downloadstatus: {0}";
};
launcher_error:
{
	error_already_running = "Er wordt al een applicatie uitgevoerd die Easy Anti-Cheat gebruikt! {0}";
	error_application = "Bij spelclient is een applicatiefout opgetreden. Foutcode: {0}";
	error_bad_exe_format = "64-bits besturingssysteem vereist";
	error_bitset_32 = "Gebruik de 32-bitsversie van het spel";
	error_bitset_64 = "Gebruik de 64-bitsversie van het spel";
	error_cancelled = "Operatie geannuleerd door gebruiker";
	error_certificate_validation = "Fout bij het valideren van het codesigning-certificaat van Easy Anti-Cheat ";
	error_connection = "Verbinding met het Content Distribution Network mislukt!";
	error_debugger = "Debugger gedetecteerd. Sluit deze af en probeer het opnieuw.";
	error_disk_space = "Onvoldoende schijfruimte.";
	error_dns = "DNS-oplossing van het Content Distribution Network mislukt!";
	error_dotlocal = "DotLocal DLL-verwijzing gedetecteerd.";
	error_dotlocal_instructions = "Verwijder het volgende bestand:";
	error_file_not_found = "Bestand niet gevonden:";
	error_forbidden_tool = "Sluit eerst {0} voordat je het spel start";
	error_forbidden_driver = "Schakel {0} uit voordat je het spel opstart";
	error_generic = "Onverwachte fout.";
	error_kernel_debug = "Easy Anti-Cheat kan niet worden uitgevoerd als Kernel Debugging is ingeschakeld";
	error_kernel_dse = "Easy Anti-Cheat kan niet worden uitgevoerd als Driver Signature Enforcement is uitgeschakeld";
	error_kernel_modified = "Verboden Windows-kernelmodificatie gedetecteerd";
	error_library_load = "De Easy Anti-Cheat-library kon niet worden geladen";
	error_memory = "Niet genoeg geheugen om het spel te starten";
	error_module_load = "Anti-cheatmodule kon niet worden geladen";
	error_patched = "Gepatchte Windows bootloader gedetecteerd";
	error_process = "Kan proces niet aanmaken";
	error_process_crash = "Het proces is plotseling afgebroken";
	error_safe_mode = "Easy Anti-Cheat kan niet worden uitgevoerd in Windows Veilige Modus";
	error_socket = "Iets blokkeert de toegang van de applicatie tot het internet!";
	error_ssl = "Fout bij het maken van een SSL-verbinding met de CDN-service!";
	error_start = "Spel kon niet worden opgestart";
	error_uncpath_forbidden = "Het is niet mogelijk het spel uit te voeren via een netwerkshare. (UNC-pad)";
	error_connection_failed = "Verbinding mislukt: ";
	error_missing_game_id = "Game-ID ontbreekt";
	error_dns_resolve_failed = "DNS resolve naar proxy mislukt";
	error_dns_connection_failed = "Verbinding met Inhouddistributienetwerk mislukt! cURL-code: {0}!";
	error_http_response = "HTTP-responscode: {0} cURL-code: {1}";
	error_driver_handle = "Onverwachte fout. (Het was niet mogelijk de driver handle te openen)";
	error_incompatible_service = "Er is al een incompatibele Easy Anti-Cheat-service actief. Verlaat andere actieve spellen of start opnieuw op";
	error_incompatible_driver_version = "Er is al een incompatibele versie van de Easy Anti-Cheat-driver actief. Verlaat andere actieve spellen of start opnieuw op";
	error_another_launcher = "Onverwachte fout. (Er is al een andere launcher actief.)";
	error_game_running = "Onverwachte fout. (De game is al opgestart)";
	error_patched_boot_loader = "Gepatchte Windows-opstartlader gedetecteerd. (Kernel Patch Protection uitgeschakeld)";
	error_unknown_process = "Spelclient wordt niet herkend. Doorgaan niet mogelijk.";
	error_unknown_game = "Spel niet geconfigureerd. Laden wordt afgebroken.";
	error_win7_required = "Windows 7 of later is vereist.";
	success_initialized = "Easy Anti-Cheat gestart";
	success_loaded = "Easy Anti-Cheat in het spel geladen";
	error_create_process = "Aanmaken van het spelproces is mislukt: {0}";
	error_create_thread = "Aanmaken van de achtergrondthread is mislukt!";
	error_disallowed_cdn_path = "Onverwachte fout. (Foute CDN-url)";
	error_empty_executable_field = "Het pad naar het binaire spelbestand werd niet opgegeven.";
	error_failed_path_query = "Het pad van het proces kan niet worden opgehaald";
	error_failed_to_execute = "Uitvoering van het spelproces is mislukt.";
	error_game_binary_is_directory = "Het uitvoerbare doelbestand is een werkmap!";
	error_game_binary_is_dot_app = "Het uitvoerbare doelbestand is een werkmap, zoek in plaats daarvan het binaire bestand in de .app!";
	error_game_binary_not_found = "Kan het binaire bestand van het spel niet vinden: '{0}'";
	error_game_binary_not_found_wine = "Kan het binaire bestand van de game (Wine) niet vinden";
	error_game_security_violation = "Schending van de beveiliging van de game {0}";
	error_generic_ex = "Onverwachte fout. {0}";
	error_instance_count_limit = "Maximum aantal gelijktijdige spelinstanties bereikt!";
	error_internal = "Interne fout!";
	error_invalid_executable_path = "Ongeldig uitvoerbaar pad voor het spel!";
	error_memory_ex = "Onvoldoende geheugen om het spel op te starten {0}";
	error_missing_binary_path = "Ontbrekend uitvoerbaar pad voor het spel.";
	error_missing_directory_path = "Ontbrekend pad naar de werkmap.";
	error_module_initialize = "Module-initialisatie mislukt bij {0}";
	error_module_loading = "Laden van de anti-cheatmodule is mislukt.";
	error_set_environment_variables = "Instellen van omgevingsvariabelen voor het spelproces is mislukt.";
	error_unrecognized_blacklisted_driver = "N/A werd gedetecteerd. Verwijder het en probeer het opnieuw.";
	error_unsupported_machine_arch = "Hostmachine-architectuur wordt niet ondersteund. ({0})";
	error_working_directory_not_found = "Werkmap bestaat niet.";
	error_x8664_required = "Besturingssysteem wordt niet ondersteund. 64-bits (x86-64) versie van Windows is vereist.";
	warn_module_download_size = "Grootte van HTTP-respons: {0}. Starten in null-clientmodus.";
	warn_vista_deprecation = "Easy Anti-Cheat moet de ondersteuning voor Windows Vista in oktober 2020 beëindigen omdat er geen compatibele codehandtekeningen meer kunnen worden aangemaakt. Raadpleeg https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Kan de anti-cheat-configuratie niet valideren.";
	warn_win7_update_required = "Voer Windows-updates uit, je systeem beschikt niet over de essentiële SHA-2-ondersteuning voor de ondertekening van code, die vereist is tegen oktober 2020. Raadpleeg https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Er is een time-out opgetreden voor de Easy Anti-Cheat service-update."
	error_launch_ex = "Opstartfout: {0}";
};
setup:
{
	btn_finish = "Afronden";
	btn_install = "Nu installeren";
	btn_repair = "Service repareren";
	btn_uninstall = "Verwijderen";
	epic_link = "© Epic Games, Inc";
	install_progress = "Installeren...";
	install_success = "Installatie geslaagd";
	licenses_link = "Licenties";
	privacy_link = "Privacy";
	repair_progress = "Repareren...";
	title = "Installatie van de Easy Anti-Cheat-service";
	uninstall_progress = "Verwijderen...";
	uninstall_success = "Verwijderen geslaagd";
};
setup_error:
{
	error_cancelled = "Operatie geannuleerd door gebruiker";
	error_encrypted = "De Easy Anti-Cheat-installatiemap is versleuteld";
	error_intro = "Installatie van Easy Anti-Cheat mislukt";
	error_not_installed = "Easy Anti-Cheat is niet geïnstalleerd.";
	error_registry = "Toegang tot register ontzegd";
	error_rights = "Onvoldoende rechten";
	error_service = "Kan service niet aanmaken";
	error_service_ex = "Kan service niet aanmaken {0}";
	error_system = "Toegang tot System32 ontzegd";
};