__request   =   {
   __select   =   {
      __select   =   {
         __showOnlyChildren   =   False
         __format   =   "{name}"
         __path   =   "<project_dir>/local/animations/morpheme/{arg}/mr_link_browser_data.json|<project_dir>/resources/morpheme/{arg}/mr_link_browser_data.json"
         __root   =   "{arg}.paramsBool"
         __type   =   "jsonLink"
      }
      __showOnlyChildren   =   True
      __format   =   ""
      __path   =   "nameBundle"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __format   =   ""
   __showValue   =   False
   __path   =   "properties/prop_morpheme_controller/morphemeController/"
   __type   =   "model"
}
__type   =   "linkBrowser"

