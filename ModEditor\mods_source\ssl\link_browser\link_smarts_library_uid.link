__request   =   {
   __select   =   {
      __id   =   "cat"
      __select   =   {
         __id   =   "type"
         __select   =   {
            __id   =   "smart"
            __path   =   "smartDescs/*"
            __type   =   "model"
         }
         __path   =   "actorTypes/*"
         __type   =   "model"
      }
      __path   =   "actorCategories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "SmartsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{cat}.{type}.{smart}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

