__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __select   =   {
                     __format   =   "{value}"
                     __selectValue   =   True
                     __path   =   "name"
                     __type   =   "json"
                  }
                  __showOnlyChildren   =   True
                  __where   =   {
                     __fields   =   [
                        {
                           __name   =   "name"
                           __check   =   {
                              __value   =   ".+"
                              __isValueRegex   =   True
                              __type   =   "check_string"
                           }
                        }
                     ]
                  }
                  __recursive   =   True
                  __recursivePath   =   "children"
                  __selectValue   =   True
                  __selectField   =   "name"
                  __path   =   "engine_objects"
                  __type   =   "json"
               }
               __showOnlyChildren   =   True
               __baseDirs   =   [
                  "{arg}"
               ]
               __searchPattern   =   "obj_list.json"
               __recursive   =   False
               __type   =   "file"
            }
            __showOnlyChildren   =   True
            __baseDir   =   "<common-database_dir>|<database_dir>"
            __searchPattern   =   "*\\{arg}.tpl"
            __type   =   "directory"
         }
         __showOnlyChildren   =   True
         __format   =   "{value}"
         __showValue   =   True
         __path   =   "nameTpl"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __where   =   {
         __type   =   "^(geom|lwi_type_base|lwi_type_distr_item)$"
      }
      __onlyChanged   =   False
      __type   =   "modelRecursive"
   }
   __showOnlyChildren   =   True
   __modelType   =   "^(actor|iactor|static_geometry)$"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

