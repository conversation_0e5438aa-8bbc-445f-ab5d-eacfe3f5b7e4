# Space Marine 2 - Simple Automated Weapon Mod
# Fixed version without syntax errors

Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class SimpleMemoryEditor {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);
    
    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
}
"@

Write-Host "Space Marine 2 - Automated Weapon Modification" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Find Space Marine 2 process
Write-Host "Searching for Space Marine 2 process..." -ForegroundColor Yellow
$gameProcess = Get-Process | Where-Object { 
    $_.ProcessName -like "*Space*" -or 
    $_.ProcessName -like "*Warhammer*" -or
    $_.MainWindowTitle -like "*Space Marine*"
}

if (-not $gameProcess) {
    Write-Host "ERROR: Space Marine 2 not found!" -ForegroundColor Red
    Write-Host "Please launch Space Marine 2 first." -ForegroundColor Yellow
    exit 1
}

$processId = $gameProcess[0].Id
$processName = $gameProcess[0].ProcessName
Write-Host "Found: $processName (PID: $processId)" -ForegroundColor Green

# Open process
$processHandle = [SimpleMemoryEditor]::OpenProcess([SimpleMemoryEditor]::PROCESS_ALL_ACCESS, $false, $processId)
if ($processHandle -eq [IntPtr]::Zero) {
    Write-Host "ERROR: Failed to access game process!" -ForegroundColor Red
    Write-Host "Try running as Administrator." -ForegroundColor Yellow
    exit 1
}

Write-Host "Process access granted" -ForegroundColor Green
Write-Host ""

# Function to search for values in process memory
function Search-ProcessMemory {
    param(
        [IntPtr]$ProcessHandle,
        [int]$SearchValue,
        [string]$ValueName
    )
    
    Write-Host "Searching for $ValueName ($SearchValue)..." -ForegroundColor Yellow
    
    $foundAddresses = @()
    $searchBytes = [BitConverter]::GetBytes([int32]$SearchValue)
    
    # Search in common memory ranges
    $memoryRanges = @(
        @{ Start = 0x10000000; End = 0x20000000 },
        @{ Start = 0x20000000; End = 0x30000000 },
        @{ Start = 0x30000000; End = 0x40000000 },
        @{ Start = 0x40000000; End = 0x50000000 }
    )
    
    foreach ($range in $memoryRanges) {
        $currentAddress = [IntPtr]$range.Start
        $endAddress = [IntPtr]$range.End
        
        while ($currentAddress.ToInt64() -lt $endAddress.ToInt64()) {
            try {
                $buffer = New-Object byte[] 4096
                $bytesRead = 0
                
                if ([SimpleMemoryEditor]::ReadProcessMemory($ProcessHandle, $currentAddress, $buffer, 4096, [ref]$bytesRead)) {
                    for ($i = 0; $i -le ($buffer.Length - 4); $i++) {
                        if ($buffer[$i] -eq $searchBytes[0] -and
                            $buffer[$i+1] -eq $searchBytes[1] -and
                            $buffer[$i+2] -eq $searchBytes[2] -and
                            $buffer[$i+3] -eq $searchBytes[3]) {
                            
                            $foundAddress = [IntPtr]::Add($currentAddress, $i)
                            $foundAddresses += $foundAddress
                            
                            if ($foundAddresses.Count -le 5) {
                                Write-Host "  Found at: 0x$($foundAddress.ToString('X16'))" -ForegroundColor Green
                            }
                        }
                    }
                }
            } catch {
                # Skip inaccessible memory regions
            }
            
            $currentAddress = [IntPtr]::Add($currentAddress, 4096)
        }
    }
    
    Write-Host "  Total found: $($foundAddresses.Count) locations" -ForegroundColor Cyan
    return $foundAddresses
}

# Function to modify memory values
function Set-MemoryValue {
    param(
        [IntPtr]$ProcessHandle,
        [IntPtr[]]$Addresses,
        [int]$NewValue,
        [string]$ValueName
    )
    
    if ($Addresses.Count -eq 0) {
        Write-Host "  No addresses to modify for $ValueName" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Modifying $ValueName to $NewValue..." -ForegroundColor Yellow
    
    $newBytes = [BitConverter]::GetBytes([int32]$NewValue)
    $successCount = 0
    
    foreach ($address in $Addresses) {
        $bytesWritten = 0
        try {
            if ([SimpleMemoryEditor]::WriteProcessMemory($ProcessHandle, $address, $newBytes, 4, [ref]$bytesWritten)) {
                $successCount++
                if ($successCount -le 5) {
                    Write-Host "  Modified: 0x$($address.ToString('X16'))" -ForegroundColor Green
                }
            }
        } catch {
            # Skip write-protected addresses
        }
    }
    
    Write-Host "  Successfully modified: $successCount/$($Addresses.Count) locations" -ForegroundColor Cyan
    return $successCount -gt 0
}

# Search for common weapon damage values
Write-Host "PISTOL MODIFICATION" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$pistolValues = @(25, 30, 35, 40)
$allPistolAddresses = @()

foreach ($value in $pistolValues) {
    $addresses = Search-ProcessMemory -ProcessHandle $processHandle -SearchValue $value -ValueName "Pistol Damage"
    $allPistolAddresses += $addresses
}

$pistolSuccess = Set-MemoryValue -ProcessHandle $processHandle -Addresses $allPistolAddresses -NewValue 999 -ValueName "Pistol Damage"

Write-Host ""
Write-Host "MELEE MODIFICATION" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

$meleeValues = @(60, 75, 80, 90, 100, 120)
$allMeleeAddresses = @()

foreach ($value in $meleeValues) {
    $addresses = Search-ProcessMemory -ProcessHandle $processHandle -SearchValue $value -ValueName "Melee Damage"
    $allMeleeAddresses += $addresses
}

$meleeSuccess = Set-MemoryValue -ProcessHandle $processHandle -Addresses $allMeleeAddresses -NewValue 1500 -ValueName "Melee Damage"

# Clean up
[SimpleMemoryEditor]::CloseHandle($processHandle)

Write-Host ""
Write-Host "MODIFICATION COMPLETE!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

if ($pistolSuccess) {
    Write-Host "SUCCESS: Pistol modified to 999 damage" -ForegroundColor Green
} else {
    Write-Host "WARNING: Pistol modification may have failed" -ForegroundColor Yellow
}

if ($meleeSuccess) {
    Write-Host "SUCCESS: Melee modified to 1500 damage" -ForegroundColor Green
} else {
    Write-Host "WARNING: Melee modification may have failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "TEST YOUR WEAPONS NOW!" -ForegroundColor Yellow
Write-Host "- Fire your pistol at enemies" -ForegroundColor White
Write-Host "- Attack with melee weapons" -ForegroundColor White
Write-Host "- You should see massive damage numbers!" -ForegroundColor White

if ($pistolSuccess -or $meleeSuccess) {
    Write-Host ""
    Write-Host "SUCCESS! You now have overpowered weapons!" -ForegroundColor Red
}
