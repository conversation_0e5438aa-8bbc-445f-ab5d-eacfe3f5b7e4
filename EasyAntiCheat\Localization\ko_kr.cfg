#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	light = "Malgunsl.ttf";
	regular = "Malgun.ttf";
	bold = "Malgunbd.ttf";
};
bugreport:
{
	btn_continue = "게임을 종료하고 온라인 검색을 시작합니다.";
	btn_exit = "게임을 종료합니다.";
	btn_hidedetails = "상세 내용 숨기기";
	btn_showdetails = "상세 내용 보기";
	chk_sendreport = "오류 보고 보내기";
	error_code = "오류 코드:";
	lbl_body1 = "죄송합니다. 게임 시작 중에 문제가 발생했습니다.";
	lbl_body2 = "오류 보고를 통해 문제를 해결할 수 있도록 도와주세요.";
	lbl_body3 = "Easy Anti-Cheat가 온라인 검색을 통해 문제 해결 방법을 찾아볼 수 있습니다.";
	lbl_header = "게임을 시작할 수 없습니다.";
	title = "실행 오류";
};
game_error:
{
	error_catalogue_not_found = "EAC 인덱스를 찾을 수 없음";
	error_certificate_revoked = "EAC 인덱스 인증서 폐지";
	error_corrupted_memory = "변형된 메모리";
	error_corrupted_network = "변형된 패킷 플로우";
	error_file_forbidden = "알 수 없는 게임 파일";
	error_file_not_found = "필수 파일 누락";
	error_file_version = "알 수 없는 파일 버전";
	error_module_forbidden = "금지된 모듈";
	error_system_configuration = "금지된 시스템 구성";
	error_system_version = "신뢰할 수 없는 시스템 파일";
	error_tool_forbidden = "금지된 도구";
	error_violation = "내부적 안티 치트 오류";
	error_virtual = "가상 머신에서 구동할 수 없습니다.";
	peer_client_banned = "안티 치트 피어가 금지되었습니다.";
	peer_heartbeat_rejected = "안티 치트 피어가 거부되었습니다.";
	peer_validated = "안티 치트 피어 검증이 완료되었습니다.";
	peer_validation_failed = "안티 치트 피어 검증에 실패했습니다.";
	executable_not_hashed = "카탈로그에서 실행 가능한 게임 항목을 찾을 수 없습니다.";
};
launcher:
{
	btn_cancel = "취소";
	btn_exit = "나가기";
	error_cancel = "실행 취소됨";
	error_filenotfound = "파일을 찾을 수 없습니다.";
	error_init = "초기화 오류";
	error_install = "설치 오류";
	error_launch = "실행 오류";
	error_nolib = "Easy Anti-Cheat 라이브러리를 불러올 수 없습니다.";
	loading = "로딩 중";
	wait = "잠시만 기다려 주세요.";
	initializing = "초기화 중";
	success_waiting_for_game = "게임 실행 대기 중";
	success_closing = "성공";
	network_error = "네트워크 오류";
	error_no_settings_file = "{0} 찾을 수 없음";
	error_invalid_settings_format = "{0}에 유효한 JSON 형식이 없습니다.";
	error_missing_required_field = "{0}에 필수 영역이 누락되어 있습니다. ({1})";
	error_invalid_eos_identifier = "{0}에 올바르지 않은 EOS 식별자가 포함되어 있음: ({1})";
	download_progress = "다운로드 진행 상황: {0}";
};
launcher_error:
{
	error_already_running = "Easy Anti-Cheat를 사용하는 애플리케이션이 이미 실행 중입니다! {0}";
	error_application = "게임 클라이언트에 애플리케이션 오류가 발생했습니다. 오류 코드: {0}";
	error_bad_exe_format = "64비트 운영체제 필요";
	error_bitset_32 = "32-bit 버전 게임을 사용해 주세요.";
	error_bitset_64 = "64-bit 버전 게임을 사용해 주세요.";
	error_cancelled = "사용자에 의해 작업이 취소됨";
	error_certificate_validation = "Easy Anti-Cheat 코드 서명 인증서 검증 오류";
	error_connection = "CDN 연결 실패!";
	error_debugger = "디버거가 감지되었습니다. 디버거를 종료하고 다시 실행해 주십시오.";
	error_disk_space = "디스크 공간이 부족합니다.";
	error_dns = "컨텐츠 전송 네트워크 DNS 리졸브 실패!";
	error_dotlocal = "DotLocal DLL 재전송이 감지되었습니다.";
	error_dotlocal_instructions = "다음 파일을 삭제해 주십시오.";
	error_file_not_found = "파일 확인 불가:";
	error_forbidden_tool = "게임을 시작하기 전에 {0}을/를 종료해 주십시오.";
	error_forbidden_driver = "게임을 시작하기 전에 {0}을(를) 언로드하세요";
	error_generic = "예기치 않은 오류 발생";
	error_kernel_debug = "Easy Anti-Cheat는 커널 디버깅이 활성화된 상태에서 실행할 수 없습니다.";
	error_kernel_dse = "Easy Anti-Cheat는 DSE(Driver Signature Enforcement)가 비활성화된 상태에서 실행할 수 없습니다.";
	error_kernel_modified = "윈도우즈 커널의 금지된 변경사항이 감지되었습니다.";
	error_library_load = "Easy Anti-Cheat 라이브러리를 불러올 수 없습니다.";
	error_memory = "게임 시작에 필요한 메모리가 부족합니다.";
	error_module_load = "안티 치트 모듈 로딩 실패";
	error_patched = "윈도우즈 부트 로더의 패치가 감지되었습니다.";
	error_process = "프로세스 생성 불가";
	error_process_crash = "예상치 못한 문제로 프로세스가 종료되었습니다.";
	error_safe_mode = "Easy Anti-Cheat는 Windows 안전 모드에서는 실행할 수 없습니다.";
	error_socket = "애플리케이션의 인터넷 접근이 차단되어 있습니다!";
	error_ssl = "CDN 서비스와의 SSL 연결 설정 오류!";
	error_start = "게임 시작 실패";
	error_uncpath_forbidden = "네트워크 공유를 통해 게임을 실행할 수 없습니다(UNC 경로)";
	error_connection_failed = "연결 실패: ";
	error_missing_game_id = "게임 ID 누락";
	error_dns_resolve_failed = "DNS 프록시 확인 실패";
	error_dns_connection_failed = "콘텐츠 전송 네트워크(CDN) 연결 실패! Curl 코드: {0}!";
	error_http_response = "HTTP 응답 코드: {0} Curl 모드: {1}";
	error_driver_handle = "예상치 못한 오류. (드라이버 핸들 열기 실패)";
	error_incompatible_service = "호환되지 않는 Easy Anti-Cheat 서비스가 이미 실행 중입니다. 실행 중인 다른 게임을 종료하거나 다시 부팅하세요.";
	error_incompatible_driver_version = "호환되지 않는 Easy Anti-Cheat 드라이버 버전이 이미 실행 중입니다. 실행 중인 다른 게임을 종료하거나 다시 부팅하세요.";
	error_another_launcher = "예상치 못한 오류. (다른 런처가 이미 실행 중)";
	error_game_running = "예상치 못한 오류. (게임이 이미 실행 중)";
	error_patched_boot_loader = "패치된 Windows 부트 로더 감지됨. (Kernel 패치 보호 비활성화)";
	error_unknown_process = "확인되지 않은 게임 클라이언트. 클라이언트가 종료됩니다.";
	error_unknown_game = "구성되지 않은 게임. 계속 할 수 없습니다.";
	error_win7_required = "Windows 7 이상의 최신 버전이 필요합니다. ";
	success_initialized = "Easy Anti-Cheat가 성공적으로 초기화되었습니다.";
	success_loaded = "Easy Anti-Cheat가 성공적으로 인게임을 로딩했습니다.";
	error_create_process = "게임 프로세스를 만들지 못함: {0}";
	error_create_thread = "백그라운드 스레드를 만들지 못했습니다!";
	error_disallowed_cdn_path = "예상치 못한 오류. (올바르지 않은 CDN url)";
	error_empty_executable_field = "게임 바이너리의 경로가 제공되지 않았습니다.";
	error_failed_path_query = "프로세스의 경로를 받지 못함";
	error_failed_to_execute = "게임 프로세스를 실행하지 못했습니다.";
	error_game_binary_is_directory = "대상 실행 파일이 디렉터리입니다!";
	error_game_binary_is_dot_app = "대상 실행 파일이 디렉터리입니다. 대신 .app 내에 있는 바이너리를 대상으로 지정하세요!";
	error_game_binary_not_found = "게임 바이너리를 찾을 수 없음: '{0}'";
	error_game_binary_not_found_wine = "게임 바이너리를 찾지 못함(Wine)";
	error_game_security_violation = "게임 보안 위반 {0}";
	error_generic_ex = "예상치 못한 오류. {0}";
	error_instance_count_limit = "최대 동시 게임 인스턴스 수에 도달했습니다!";
	error_internal = "내부 오류!";
	error_invalid_executable_path = "올바르지 않은 게임 실행 파일 경로!";
	error_memory_ex = "{0} 게임을 시작하기에 메모리가 충분하지 않음";
	error_missing_binary_path = "게임 실행 파일 경로 누락.";
	error_missing_directory_path = "작업 디렉터리 경로 누락.";
	error_module_initialize = "{0}(으)로 모듈 초기화 실패";
	error_module_loading = "안티 치트 모듈을 로드하지 못했습니다.";
	error_set_environment_variables = "게임 프로세스에 대한 환경 변수를 설정하지 못했습니다.";
	error_unrecognized_blacklisted_driver = "N/A가 감지되었습니다. 언로드한 후 다시 시도하세요.";
	error_unsupported_machine_arch = "지원되지 않는 호스트 머신 아키텍처. ({0})";
	error_working_directory_not_found = "작업 디렉터리가 존재하지 않습니다.";
	error_x8664_required = "지원되지 않는 운영체제. 64비트(x86-64) 버전의 Windows가 필요합니다.";
	warn_module_download_size = "HTTP 응답 크기: {0}. null 클라이언트 모드로 시작 중입니다.";
	warn_vista_deprecation = "Easy Anti-Cheat는 호환되는 코드 서명을 더 이상 만들 수 없기 때문에 2020년 10월에 Windows Vista 지원을 종료합니다. https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus를 참조하세요.";
	warn_win7_update_required = "Windows 업데이트를 실행하세요. 시스템에 2020년 10월까지 필요한 주요 SHA-2 코드 서명 지원이 누락되어 있습니다. https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update를 참조하세요.";
	error_launch_ex = "실행 오류: {0}";
};
setup:
{
	btn_finish = "종료";
	btn_install = "지금 설치하기";
	btn_repair = "서비스 복구하기";
	btn_uninstall = "삭제하기";
	epic_link = "© Epic Games, Inc";
	install_progress = "설치 중...";
	install_success = "설치 완료";
	licenses_link = "라이선스";
	privacy_link = "개인정보보호";
	repair_progress = "복구 중...";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "삭제 중...";
	uninstall_success = "삭제 완료";
};
setup_error:
{
	error_cancelled = "사용자에 의해 작업이 취소되었습니다.";
	error_encrypted = "Easy Anti-Cheat 설치 폴더가 암호화되었습니다";
	error_intro = "Easy Anti-Cheat 셋업 실패";
	error_not_installed = "Easy Anti-Cheat가 설치되지 않았습니다.";
	error_registry = "서비스 실행 파일 복사 실패";
	error_rights = "권한 불충분";
	error_service = "서비스 생성 불가";
	error_service_ex = "서비스 생성 불가 {0}";
	error_system = "System32 접근 거부됨";
};