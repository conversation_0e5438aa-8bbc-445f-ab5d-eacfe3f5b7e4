__request   =   {
   __id   =   ""
   __select   =   {
      __id   =   ""
      __showParentModelCaption   =   True
      __models   =   [
         {
            __path   =   "childNodes/*"
         },
         {
            __path   =   "transitions/*"
         }
      ]
      __where   =   {
         __type   =   "^((?!mr_node_active_state).)*$"
         __parentType   =   ""
      }
      __type   =   "modelMulti"
   }
   __showOnlyChildren   =   True
   __format   =   "{index}"
   __showValue   =   False
   __where   =   {
      __type   =   ""
      __parentType   =   ""
   }
   __modelType   =   "mr_diagram"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

