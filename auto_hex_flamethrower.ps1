# Space Marine 2 - Automated Hex Flamethrower Mod
# This script automatically modifies weapon damage values in the game files

param(
    [int]$DamageValue = 999,
    [switch]$Backup = $true,
    [switch]$TestMode = $false
)

Write-Host "Space Marine 2 - Automated Hex Flamethrower Mod" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

$GameFile = "client_pc\root\paks\client\default\default.pak"
$BackupFile = "client_pc\root\paks\client\default\default.pak.backup"

# Check if game file exists
if (-not (Test-Path $GameFile)) {
    Write-Error "Game file not found: $GameFile"
    Write-Host "Make sure you're in the Space Marine 2 directory." -ForegroundColor Yellow
    exit 1
}

Write-Host "Target file: $GameFile" -ForegroundColor Cyan
Write-Host "Damage value: $DamageValue" -ForegroundColor Cyan

# Create backup
if ($Backup -and -not (Test-Path $BackupFile)) {
    Write-Host "Creating backup..." -ForegroundColor Yellow
    Copy-Item $GameFile $BackupFile -Force
    Write-Host "Backup created: $BackupFile" -ForegroundColor Green
}

# Convert damage value to little-endian hex bytes
$DamageBytes = [BitConverter]::GetBytes([int32]$DamageValue)
Write-Host "Hex bytes for $DamageValue : $([BitConverter]::ToString($DamageBytes))" -ForegroundColor Gray

# Read the file
Write-Host "Reading game file..." -ForegroundColor Yellow
$FileBytes = [System.IO.File]::ReadAllBytes($GameFile)
$OriginalSize = $FileBytes.Length
Write-Host "File size: $OriginalSize bytes" -ForegroundColor Gray

# Define damage values to search for and replace
$DamageTargets = @{
    25 = [BitConverter]::GetBytes([int32]25)   # 19 00 00 00
    30 = [BitConverter]::GetBytes([int32]30)   # 1E 00 00 00
    35 = [BitConverter]::GetBytes([int32]35)   # 23 00 00 00
    40 = [BitConverter]::GetBytes([int32]40)   # 28 00 00 00
    45 = [BitConverter]::GetBytes([int32]45)   # 2D 00 00 00
    50 = [BitConverter]::GetBytes([int32]50)   # 32 00 00 00
}

$ModificationCount = 0

# Search and replace damage values
foreach ($OriginalDamage in $DamageTargets.Keys) {
    $SearchBytes = $DamageTargets[$OriginalDamage]
    $SearchHex = [BitConverter]::ToString($SearchBytes)
    
    Write-Host "Searching for damage value $OriginalDamage ($SearchHex)..." -ForegroundColor Yellow
    
    # Search for the byte pattern
    for ($i = 0; $i -le ($FileBytes.Length - 4); $i++) {
        if ($FileBytes[$i] -eq $SearchBytes[0] -and
            $FileBytes[$i+1] -eq $SearchBytes[1] -and
            $FileBytes[$i+2] -eq $SearchBytes[2] -and
            $FileBytes[$i+3] -eq $SearchBytes[3]) {
            
            Write-Host "  Found at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
            
            if (-not $TestMode) {
                # Replace with new damage value
                $FileBytes[$i] = $DamageBytes[0]
                $FileBytes[$i+1] = $DamageBytes[1]
                $FileBytes[$i+2] = $DamageBytes[2]
                $FileBytes[$i+3] = $DamageBytes[3]
                
                Write-Host "    Replaced with $DamageValue" -ForegroundColor Cyan
                $ModificationCount++
            } else {
                Write-Host "    Would replace with $DamageValue (test mode)" -ForegroundColor Gray
            }
        }
    }
}

if ($TestMode) {
    Write-Host ""
    Write-Host "TEST MODE - No changes made" -ForegroundColor Yellow
    Write-Host "Found $ModificationCount locations that would be modified" -ForegroundColor Cyan
    Write-Host "Run without -TestMode to apply changes" -ForegroundColor White
    exit 0
}

if ($ModificationCount -eq 0) {
    Write-Host ""
    Write-Warning "No damage values found to modify!"
    Write-Host "This could mean:" -ForegroundColor Yellow
    Write-Host "  - Damage values are stored differently" -ForegroundColor White
    Write-Host "  - File is compressed or encrypted" -ForegroundColor White
    Write-Host "  - Values are in a different file" -ForegroundColor White
    Write-Host "Try manual hex editing with HxD instead." -ForegroundColor Cyan
    exit 1
}

# Write the modified file
Write-Host ""
Write-Host "Writing modified file..." -ForegroundColor Yellow
[System.IO.File]::WriteAllBytes($GameFile, $FileBytes)

# Verify file size didn't change
$NewSize = (Get-Item $GameFile).Length
if ($NewSize -ne $OriginalSize) {
    Write-Error "File size changed! This might cause problems."
    Write-Host "Original: $OriginalSize bytes, New: $NewSize bytes" -ForegroundColor Red
} else {
    Write-Host "File size verified: $NewSize bytes" -ForegroundColor Green
}

Write-Host ""
Write-Host "SUCCESS: Flamethrower mod applied!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""
Write-Host "Modifications made:" -ForegroundColor Cyan
Write-Host "  - $ModificationCount damage values changed to $DamageValue" -ForegroundColor White
Write-Host "  - Backup saved as: $(Split-Path $BackupFile -Leaf)" -ForegroundColor White
Write-Host ""
Write-Host "How to test:" -ForegroundColor Yellow
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. Start any mission" -ForegroundColor White
Write-Host "3. Equip your pistol" -ForegroundColor White
Write-Host "4. Fire at enemies" -ForegroundColor White
Write-Host "5. You should see $DamageValue+ damage!" -ForegroundColor White
Write-Host ""
Write-Host "If something goes wrong:" -ForegroundColor Red
Write-Host "1. Close the game" -ForegroundColor White
Write-Host "2. Delete: $GameFile" -ForegroundColor White
Write-Host "3. Rename: $BackupFile to $(Split-Path $GameFile -Leaf)" -ForegroundColor White
Write-Host ""
Write-Host "Ready to burn heretics with your overpowered pistol!" -ForegroundColor Green

# Create quick restore script
$RestoreScript = @"
# Quick Restore Script
Write-Host "Restoring original game file..." -ForegroundColor Yellow
if (Test-Path "$BackupFile") {
    Copy-Item "$BackupFile" "$GameFile" -Force
    Write-Host "Original file restored!" -ForegroundColor Green
} else {
    Write-Error "Backup file not found!"
}
"@

$RestoreScript | Out-File "restore_original.ps1" -Encoding UTF8
Write-Host "Created restore_original.ps1 for easy restoration." -ForegroundColor Gray
