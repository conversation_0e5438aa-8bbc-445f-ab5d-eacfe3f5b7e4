// Space Marine 2 - Auto-Unlock Everything System
// This file automatically unlocks all content when the game starts

// Override unlock requirements for all weapons
WeaponUnlockRequirements = {
    // Remove all unlock requirements
    unlockRequirements = {
        flamethrower_heavy = {
            levelRequired = 1           // Available from level 1
            campaignRequired = false    // No campaign completion needed
            challengesRequired = []     // No challenges needed
            costRequired = 0           // Free to unlock
        }
        
        heavy_bolter = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        plasma_gun = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        melta_gun = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        las_fusil = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        stalker_bolter = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        thunder_hammer = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
        
        power_sword = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
            costRequired = 0
        }
    }
    
    __type = "WeaponUnlockRequirements"
}

// Override class unlock requirements
ClassUnlockRequirements = {
    unlockRequirements = {
        tactical = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
        
        assault = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
        
        heavy = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
        
        vanguard = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
        
        bulwark = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
        
        sniper = {
            levelRequired = 1
            campaignRequired = false
            challengesRequired = []
        }
    }
    
    __type = "ClassUnlockRequirements"
}

// Override armor unlock requirements
ArmorUnlockRequirements = {
    unlockRequirements = {
        mk_vii_aquila = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        mk_viii_errant = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        mk_x_tacticus = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        mk_x_phobos = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        mk_x_gravis = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        artificer_armor = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        terminator_armor = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        veteran_armor = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
    }
    
    __type = "ArmorUnlockRequirements"
}

// Override weapon skin unlock requirements
WeaponSkinUnlockRequirements = {
    unlockRequirements = {
        default = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        veteran = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        artificer = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        relic = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        master_crafted = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        chapter_specific = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        campaign_reward = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
        special_edition = { levelRequired = 1, campaignRequired = false, costRequired = 0 }
    }
    
    __type = "WeaponSkinUnlockRequirements"
}

// Override game mode unlock requirements
GameModeUnlockRequirements = {
    unlockRequirements = {
        operations = {
            levelRequired = 1
            campaignRequired = false
            tutorialRequired = false
        }
        
        eternal_war = {
            levelRequired = 1
            campaignRequired = false
            tutorialRequired = false
        }
        
        private_match = {
            levelRequired = 1
            campaignRequired = false
            tutorialRequired = false
        }
    }
    
    __type = "GameModeUnlockRequirements"
}

// Override difficulty unlock requirements
DifficultyUnlockRequirements = {
    unlockRequirements = {
        minimal = { levelRequired = 1, campaignRequired = false }
        average = { levelRequired = 1, campaignRequired = false }
        substantial = { levelRequired = 1, campaignRequired = false }
        ruthless = { levelRequired = 1, campaignRequired = false }
        
        // Campaign difficulties
        recruit = { levelRequired = 1, campaignRequired = false }
        veteran = { levelRequired = 1, campaignRequired = false }
        angel_of_death = { levelRequired = 1, campaignRequired = false }
    }
    
    __type = "DifficultyUnlockRequirements"
}

// Auto-grant resources and currency
PlayerResourcesOverride = {
    startingResources = {
        requisition = 999999
        armory_data = 999999
        chapter_coins = 999999
        
        materials = {
            adamantium = 9999
            ceramite = 9999
            plasteel = 9999
            promethium = 9999
            sacred_oils = 9999
        }
    }
    
    // Infinite resources mode
    infiniteResources = true
    
    __type = "PlayerResourcesOverride"
}

// Auto-complete tutorial and intro sequences
TutorialOverride = {
    skipTutorial = true
    skipIntro = true
    skipCutscenes = false           // Keep cutscenes for story
    autoCompleteTraining = true
    
    __type = "TutorialOverride"
}

// Auto-unlock all achievements for testing
AchievementOverride = {
    unlockAll = true
    
    // Specific achievement unlocks
    achievements = [
        "campaign_complete",
        "veteran_complete", 
        "all_secrets_found",
        "all_dataslates_collected",
        "weapon_master",
        "all_weapons_mastered",
        "flamethrower_specialist",
        "operations_veteran",
        "pvp_veteran",
        "ranked_warrior"
    ]
    
    __type = "AchievementOverride"
}
