steps:
  - RES3_INITIAL_MASTER_SOUND_BANK
  - RES3_INITIAL_SOUNDS
  - RES3_INITIAL_SSL
  - RES3_INITIAL_SSL_DESC
  - RES3_INITIAL_SHADERS
  - RES3_INITIAL_SHADER_CACHE
  - RES3_INITIAL_MATERIALS_TEMPLATES
  - RES3_INITIAL_TD_DEFAULTS
  - RES3_INITIAL_MTL
  - RES3_INITIAL_MTL_MUTATORS
  - RES3_INITIAL_MML_CFG
  - RES3_INITIAL_PCT
  - RES3_INITIAL_ADAPTER_LOADING
  - RES3_INITIAL_VIDEO
  - RES3_INITIAL_GFX
  - RES3_INITIAL_MORPHEME
  - RES3_INITIAL_FONTS
  - RES3_INITIAL_TEXTS
  - RES3_INITIAL_ALL

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_INITIAL_SSL
  
  - name: finalize_sound_events_loading
    on:
      - step: RES3_INITIAL_SOUNDS

  - name: adapter_loading
    on:
      - step: RES3_INITIAL_ADAPTER_LOADING

rules:
  #
  # sounds
  #
  - name: master sound bank
    res_type: res_desc_sound_bank
    on:
      - mask: '*init.sound_bank*'
    step: RES3_INITIAL_MASTER_SOUND_BANK

  - name: other sound banks
    res_type: res_desc_sound_bank
    step: RES3_INITIAL_SOUNDS
  
  - name: initial sound media bundles
    res_type: res_desc_sound_media_bundle
    step: RES3_INITIAL_SOUNDS
        
  - name: initial streaming sound events
    res_type: res_desc_sound_streamed
    step: RES3_INITIAL_SOUNDS
    
  - name: initial sound events
    res_type: res_desc_sound_event
    step: RES3_INITIAL_SOUNDS

  - name: initial sound localization
    res_type: res_desc_sound_localization
    step: RES3_INITIAL_SOUNDS

  #
  # ssl
  #
  - name: process intital ssl
    res_type: res_desc_ssl
    step: RES3_INITIAL_SSL

  - name: process intital spdb
    res_type: res_desc_spdb
    step: RES3_INITIAL_SSL


  #
  # ssl descs
  #
  - name: process intital ssolib
    res_type: res_desc_ssolib
    step: RES3_INITIAL_SSL_DESC

  - name: process intital sso
    res_type: res_desc_sso
    step: RES3_INITIAL_SSL_DESC

  - name: process intital cls
    res_type: res_desc_cls
    step: RES3_INITIAL_SSL_DESC

  - name: process intital prop
    res_type: res_desc_prop
    step: RES3_INITIAL_SSL_DESC

  - name: process intital prefab
    res_type: res_prefab_desc
    step: RES3_INITIAL_SSL_DESC

  - name: process intital prefab asset
    res_type: res_desc_prefab_asset
    step: RES3_INITIAL_SSL_DESC

  #
  # basic descs
  #
  - name: process intital basic descs
    res_type: res_basic_ssl_desc
    step: RES3_INITIAL_SSL_DESC

  #
  # shaders
  #

  - name: process platforms resource
    res_type: res_desc_sdr_platforms
    step: RES3_INITIAL_SHADERS
  
  - name: process shader presets
    res_type: res_desc_sdr_preset
    step: RES3_INITIAL_SHADERS

  - name: process shaders
    res_type: res_desc_shaders
    step: RES3_INITIAL_SHADERS

  - name: process shaders
    res_type: res_desc_sdr_descs
    step: RES3_INITIAL_SHADERS

  - name: process prebuild shader cache
    res_type: res_desc_shader_cache
    step: RES3_INITIAL_SHADER_CACHE

  - name: set platforms shaders src
    res_type: res_desc_shader_cache_set
    step: RES3_INITIAL_SHADER_CACHE
    
  - name: process local shader cache
    res_type: res_desc_shader_cache_local
    step: RES3_INITIAL_SHADER_CACHE

  - name: process pso shader cache
    res_type: res_desc_shader_cache_pso
    step: RES3_INITIAL_SHADER_CACHE

  - name: process pso shader cache dump
    res_type: res_desc_shader_cache_pso_sump
    step: RES3_INITIAL_SHADER_CACHE

  #
  # mtl
  #
  - name: process material templates
    res_type: res_desc_material_templates
    step: RES3_INITIAL_MATERIALS_TEMPLATES

  - name: process default tds
    res_type: res_desc_td_defaults
    step: RES3_INITIAL_TD_DEFAULTS

  - name: process tds
    res_type: res_desc_td
    step: RES3_INITIAL_MTL

  - name: process mtl_mutators
    res_type: res_desc_mtl_mut
    step: RES3_INITIAL_MTL_MUTATORS

  #
  # gfx
  #
  - name: process intital mml cfg
    res_type: res_desc_mml_cfg
    step: RES3_INITIAL_MML_CFG

  - name: td
    res_type: res_desc_td
    step: RES3_INITIAL_PCT

  - name: process ui pct
    res_type: res_desc_pct
    on:
      - mask: 'res://ui/pct/*.pct.resource'
    step: RES3_INITIAL_PCT

  - name: process ui texture assets
    res_type: res_desc_pct
    on:
      - mask: 'res://textures/ui/*.pct.resource'
    step: RES3_INITIAL_PCT

  - name: process ui video assets
    res_type: res_desc_video
    on:
      - mask: 'res://video/ui/*.video.asset'
    step: RES3_INITIAL_VIDEO
    
  - name: process technical pct
    res_type: res_desc_pct
    on:
      - mask: 'res://pct/__*.pct.resource'
    step: RES3_INITIAL_PCT

  - name: process intital gfx
    res_type: res_desc_gfx
    step: RES3_INITIAL_GFX

  - name: part aliases
    res_type: res_desc_part_aliases
    step: RES3_INITIAL_GFX
    
  #
  # morpheme
  #
  - name: process initial morpheme
    res_type: res_desc_morpheme
    step: RES3_INITIAL_MORPHEME

  - name: process animations
    res_type: res_desc_animation
    step: RES3_INITIAL_MORPHEME

  #
  # fonts
  #

  - name: process font pct
    res_type: res_desc_pct
    on:
     - mask: 'res://font_main/*.pct.resource'
    step: RES3_INITIAL_FONTS

  - name: process imgui fonts
    res_type: res_desc_imgui_fonts
    step: RES3_INITIAL_FONTS

  - name: process main fonts
    res_type: res_desc_main_font
    step: RES3_INITIAL_FONTS

  - name: process texts
    res_type: res_desc_texts
    step: RES3_INITIAL_TEXTS

  #
  # all
  #
  - name: exclude all other
    exclude: true
