__request   =   {
   __select   =   {
      __select   =   {
         __id   =   "sfx"
         __select   =   {
            __id   =   "emitter"
            __path   =   "emitters2/*"
            __showValue   =   False
            __where   =   {
               __type   =   "GPU_EMITTER"
            }
            __type   =   "model"
         }
         __path   =   "list/*"
         __where   =   {
            __parentType   =   "sfx_part"
         }
         __type   =   "model"
      }
      __path   =   "properties/*"
      __where   =   {
         __type   =   "sfx"
      }
      __type   =   "model"
   }
   __type   =   "document"
}
__resultFormatter   =   {
   __format   =   "{sfx}.{emitter}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

