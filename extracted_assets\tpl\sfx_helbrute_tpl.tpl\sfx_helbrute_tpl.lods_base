lodInfo = {
   maxLodDist = [

   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      zone01 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      zone02 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      hammer_glow = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      aftershock_sphere = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      aftershock_distort = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      aftershock_wave = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      aftershock_emitter = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      charge_zone = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      plasma_charge_lght = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      plasma_heavy = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      hammer_sphere01 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      hammer_sphere02 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
