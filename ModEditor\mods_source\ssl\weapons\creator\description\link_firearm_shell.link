__request   =   {
   __select   =   {
      __select   =   {
         __models   =   [
            {
               __path   =   "shellDescriptions/*"
            },
            {
               __path   =   "descriptionOverride/shellDescriptions/*"
            }
         ]
         __type   =   "modelMulti"
      }
      __models   =   [
         {
            __path   =   "firearms/*"
         },
         {
            __path   =   "godlikeModificatorPresets/*"
         }
      ]
      __type   =   "modelMulti"
   }
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "^(firearmModificatorsLibrary)?(firearmLibraryPve)?$"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

