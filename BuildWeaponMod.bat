@echo off
title Space Marine 2 - Advanced Weapon Mod Builder
color 0A

echo.
echo  ██████╗ ██╗   ██╗██╗██╗     ██████╗     ███╗   ███╗ ██████╗ ██████╗ 
echo  ██╔══██╗██║   ██║██║██║     ██╔══██╗    ████╗ ████║██╔═══██╗██╔══██╗
echo  ██████╔╝██║   ██║██║██║     ██║  ██║    ██╔████╔██║██║   ██║██║  ██║
echo  ██╔══██╗██║   ██║██║██║     ██║  ██║    ██║╚██╔╝██║██║   ██║██║  ██║
echo  ██████╔╝╚██████╔╝██║███████╗██████╔╝    ██║ ╚═╝ ██║╚██████╔╝██████╔╝
echo  ╚═════╝  ╚═════╝ ╚═╝╚══════╝╚═════╝     ╚═╝     ╚═╝ ╚═════╝ ╚═════╝ 
echo.
echo                    🔥 ADVANCED WEAPON MOD BUILDER 🔥
echo                   ===================================
echo.

REM Check for Visual Studio Build Tools
echo 🔍 Checking for build environment...

REM Try to find Visual Studio installations
set "VS_PATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo ✅ Found Visual Studio 2022 Community
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    echo ✅ Found Visual Studio 2022 Professional
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo ✅ Found Visual Studio 2019 Community
) else (
    echo ❌ Visual Studio not found!
    echo.
    echo 📥 DOWNLOAD REQUIRED TOOLS:
    echo 1. Download Visual Studio Community (free)
    echo    https://visualstudio.microsoft.com/downloads/
    echo 2. Install with "Desktop development with C++" workload
    echo 3. Run this script again
    echo.
    goto :download_alternative
)

echo.
echo 🔨 Setting up build environment...
call "%VS_PATH%"

echo.
echo 🔧 Building Advanced Weapon Mod DLL...

REM Compile the DLL
cl.exe /LD /EHsc /O2 /DNDEBUG ^
    AdvancedWeaponInjector.cpp ^
    /Fe:AdvancedWeaponMod.dll ^
    /link kernel32.lib user32.lib

if %errorlevel% neq 0 (
    echo ❌ DLL compilation failed!
    goto :error
)

echo ✅ DLL compiled successfully!
echo.

echo 🔧 Building Injector Executable...

REM Compile the injector
cl.exe /EHsc /O2 /DNDEBUG ^
    WeaponModInjector.cpp ^
    /Fe:WeaponModInjector.exe ^
    /link kernel32.lib user32.lib

if %errorlevel% neq 0 (
    echo ❌ Injector compilation failed!
    goto :error
)

echo ✅ Injector compiled successfully!
echo.

REM Clean up object files
del *.obj 2>nul
del *.exp 2>nul
del *.lib 2>nul

echo 🎯 BUILD COMPLETE!
echo ================
echo.
echo 📁 Generated files:
echo   ✅ AdvancedWeaponMod.dll - The weapon modification DLL
echo   ✅ WeaponModInjector.exe - The injection tool
echo.
echo 🚀 HOW TO USE:
echo 1. Launch Space Marine 2
echo 2. Run WeaponModInjector.exe as Administrator
echo 3. The mod will automatically inject and modify weapons
echo 4. Enjoy your overpowered flamethrower and lightning sword!
echo.
echo 🔥 EXPECTED RESULTS:
echo   • Pistol: 999 damage with flame effects
echo   • Melee: 1500 damage with lightning effects
echo   • One-shot kills on most enemies
echo.
goto :end

:download_alternative
echo 💡 ALTERNATIVE: Pre-compiled Version
echo ===================================
echo.
echo If you don't want to install Visual Studio, you can:
echo 1. Download pre-compiled mod tools from modding communities
echo 2. Use existing Space Marine 2 trainers from:
echo    - FearlessRevolution.com
echo    - WeMod.com
echo    - CheatHappens.com
echo 3. Search for "Space Marine 2 cheat table" or "trainer"
echo.
goto :end

:error
echo.
echo ❌ BUILD FAILED!
echo ===============
echo.
echo Possible issues:
echo - Visual Studio not properly installed
echo - Missing Windows SDK
echo - Antivirus blocking compilation
echo.
echo Try:
echo 1. Install Visual Studio with C++ workload
echo 2. Run as Administrator
echo 3. Temporarily disable antivirus
echo.

:end
echo.
echo Press any key to exit...
pause >nul
