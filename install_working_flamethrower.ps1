# Space Marine 2 - Working Flamethrower Installation
# This script properly installs the flamethrower mod using the correct workflow

param(
    [switch]$Force = $false,
    [switch]$TestOnly = $false
)

Write-Host "Space Marine 2 - Working Flamethrower Installation" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if directories exist
$ModEditorPath = "ModEditor\mods_source\ssl\weapons"
$LocalPath = "client_pc\root\local\ssl\weapons"

if (-not (Test-Path $ModEditorPath)) {
    Write-Error "ModEditor path not found: $ModEditorPath"
    Write-Host "Make sure you're in the Space Marine 2 root directory." -ForegroundColor Yellow
    exit 1
}

# Create local directory if it doesn't exist
if (-not (Test-Path $LocalPath)) {
    Write-Host "Creating local weapons directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $LocalPath -Force | Out-Null
}

# Source file in ModEditor
$SourceFile = "$ModEditorPath\heavy_bolter_flamethrower_mod.sso"
$DestFile = "$LocalPath\heavy_bolter_flamethrower_mod.sso"

# Check if source file exists
if (-not (Test-Path $SourceFile)) {
    Write-Error "Source mod file not found: $SourceFile"
    exit 1
}

Write-Host "Found mod file: $SourceFile" -ForegroundColor Green

# Test mode - just verify files
if ($TestOnly) {
    Write-Host "TEST MODE - Verifying files only" -ForegroundColor Cyan
    
    # Check file content
    $Content = Get-Content $SourceFile -Raw
    if ($Content -match "heavy_bolter.*=.*\{" -and $Content -match "__type.*=.*FirearmDescription") {
        Write-Host "✓ Mod file format looks correct" -ForegroundColor Green
    } else {
        Write-Warning "Mod file format may be incorrect"
    }
    
    if ($Content -match "damage.*=.*{.*base.*=.*120\.0") {
        Write-Host "✓ Flamethrower damage settings found" -ForegroundColor Green
    } else {
        Write-Warning "Flamethrower damage settings not found"
    }
    
    if ($Content -match "areaOfEffect.*=.*3\.0") {
        Write-Host "✓ Area damage settings found" -ForegroundColor Green
    } else {
        Write-Warning "Area damage settings not found"
    }
    
    Write-Host "Test completed. Files are ready for installation." -ForegroundColor Cyan
    exit 0
}

# Backup existing file if it exists
if (Test-Path $DestFile) {
    if (-not $Force) {
        $Response = Read-Host "Destination file exists. Overwrite? (y/N)"
        if ($Response -ne "y" -and $Response -ne "Y") {
            Write-Host "Installation cancelled." -ForegroundColor Yellow
            exit 0
        }
    }
    
    $BackupFile = "$DestFile.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $DestFile $BackupFile -Force
    Write-Host "Backed up existing file to: $BackupFile" -ForegroundColor Gray
}

# Copy the mod file
Write-Host "Installing flamethrower mod..." -ForegroundColor Yellow
Copy-Item $SourceFile $DestFile -Force

if (Test-Path $DestFile) {
    Write-Host "✓ Mod file copied successfully!" -ForegroundColor Green
} else {
    Write-Error "Failed to copy mod file!"
    exit 1
}

# Verify the installation
Write-Host "Verifying installation..." -ForegroundColor Yellow

$InstalledContent = Get-Content $DestFile -Raw
if ($InstalledContent -match "heavy_bolter.*=.*\{" -and $InstalledContent -match "__type.*=.*FirearmDescription") {
    Write-Host "✓ Installation verified!" -ForegroundColor Green
} else {
    Write-Error "Installation verification failed!"
    exit 1
}

Write-Host ""
Write-Host "🔥 FLAMETHROWER MOD INSTALLED SUCCESSFULLY! 🔥" -ForegroundColor Red
Write-Host ""
Write-Host "What Changed:" -ForegroundColor Cyan
Write-Host "  • Heavy Bolter now has flamethrower stats" -ForegroundColor White
Write-Host "  • High damage (120 base damage)" -ForegroundColor White
Write-Host "  • Short range (15m maximum)" -ForegroundColor White
Write-Host "  • Area damage (3m radius)" -ForegroundColor White
Write-Host "  • Burning status effects" -ForegroundColor White
Write-Host "  • Fear effects on basic enemies" -ForegroundColor White
Write-Host "  • Environmental ignition" -ForegroundColor White
Write-Host "  • Overheating mechanics" -ForegroundColor White
Write-Host ""
Write-Host "How to Test:" -ForegroundColor Magenta
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. Start any mission (Campaign, Operations, Eternal War)" -ForegroundColor White
Write-Host "3. Select Heavy class or find Heavy Bolter pickup" -ForegroundColor White
Write-Host "4. The Heavy Bolter now behaves like a flamethrower!" -ForegroundColor White
Write-Host "5. Get close to enemies (within 15m)" -ForegroundColor White
Write-Host "6. Hold fire button for continuous damage" -ForegroundColor White
Write-Host "7. Watch enemies burn and flee!" -ForegroundColor White
Write-Host ""
Write-Host "Flamethrower Features:" -ForegroundColor Yellow
Write-Host "  • 120 damage per shot (very high)" -ForegroundColor White
Write-Host "  • 3m area of effect damage" -ForegroundColor White
Write-Host "  • Burning: 20 DPS for 4 seconds" -ForegroundColor White
Write-Host "  • Fear effects on weak enemies" -ForegroundColor White
Write-Host "  • Ignites environmental objects" -ForegroundColor White
Write-Host "  • Overheating prevents spam" -ForegroundColor White
Write-Host ""
Write-Host "Combat Tips:" -ForegroundColor Green
Write-Host "  • Get close - maximum range is 15 meters" -ForegroundColor White
Write-Host "  • Aim at groups - area damage hits multiple enemies" -ForegroundColor White
Write-Host "  • Watch heat buildup - let it cool down" -ForegroundColor White
Write-Host "  • Use against swarms - burning spreads damage" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Important Notes:" -ForegroundColor Red
Write-Host "  • This mod only works in offline/PvE modes" -ForegroundColor Yellow
Write-Host "  • Heavy Bolter is temporarily replaced" -ForegroundColor Yellow
Write-Host "  • Using mods online may result in bans" -ForegroundColor Yellow
Write-Host ""
Write-Host "Ready to purge heretics with righteous flame!" -ForegroundColor Green
Write-Host "The Emperor protects! 🔥" -ForegroundColor Red

# Create quick test script
$TestScript = @"
# Quick Test - Verify Flamethrower Installation
Write-Host "Testing flamethrower installation..." -ForegroundColor Green
& ".\install_working_flamethrower.ps1" -TestOnly
"@

$TestScript | Out-File "test_flamethrower_installation.ps1" -Encoding UTF8

Write-Host ""
Write-Host "Use test_flamethrower_installation.ps1 to verify anytime." -ForegroundColor Gray
