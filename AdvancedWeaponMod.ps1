# Space Marine 2 - Advanced Memory Scanner
# Comprehensive memory search for weapon values

Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class AdvancedMemoryEditor {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);
    
    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [DllImport("kernel32.dll")]
    public static extern int VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }
    
    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const uint MEM_COMMIT = 0x1000;
    public const uint PAGE_READWRITE = 0x04;
    public const uint PAGE_EXECUTE_READWRITE = 0x40;
}
"@

Write-Host "Space Marine 2 - Advanced Weapon Modification" -ForegroundColor Green
Write-Host "==============================================" -ForegroundColor Green
Write-Host ""

# Find game process
$gameProcess = Get-Process | Where-Object { 
    $_.ProcessName -like "*Space*" -or 
    $_.ProcessName -like "*Warhammer*" -or
    $_.MainWindowTitle -like "*Space Marine*"
}

if (-not $gameProcess) {
    Write-Host "ERROR: Space Marine 2 not found!" -ForegroundColor Red
    exit 1
}

$processId = $gameProcess[0].Id
Write-Host "Found: $($gameProcess[0].ProcessName) (PID: $processId)" -ForegroundColor Green

$processHandle = [AdvancedMemoryEditor]::OpenProcess([AdvancedMemoryEditor]::PROCESS_ALL_ACCESS, $false, $processId)
if ($processHandle -eq [IntPtr]::Zero) {
    Write-Host "ERROR: Failed to access process!" -ForegroundColor Red
    exit 1
}

Write-Host "Process access granted" -ForegroundColor Green
Write-Host ""

# Get all readable memory regions
function Get-MemoryRegions {
    param([IntPtr]$ProcessHandle)
    
    Write-Host "Mapping memory regions..." -ForegroundColor Yellow
    
    $regions = @()
    $address = [IntPtr]::Zero
    $memInfo = New-Object AdvancedMemoryEditor+MEMORY_BASIC_INFORMATION
    
    while ($true) {
        $result = [AdvancedMemoryEditor]::VirtualQueryEx($ProcessHandle, $address, [ref]$memInfo, [System.Runtime.InteropServices.Marshal]::SizeOf($memInfo))
        if ($result -eq 0) { break }
        
        if ($memInfo.State -eq [AdvancedMemoryEditor]::MEM_COMMIT -and 
            ($memInfo.Protect -eq [AdvancedMemoryEditor]::PAGE_READWRITE -or 
             $memInfo.Protect -eq [AdvancedMemoryEditor]::PAGE_EXECUTE_READWRITE)) {
            
            $regionSize = [int]$memInfo.RegionSize
            if ($regionSize -gt 0 -and $regionSize -lt 100MB) {
                $regions += @{
                    BaseAddress = $memInfo.BaseAddress
                    Size = $regionSize
                }
            }
        }
        
        $address = [IntPtr]::Add($memInfo.BaseAddress, [int]$memInfo.RegionSize)
        if ($address -eq [IntPtr]::Zero) { break }
    }
    
    Write-Host "Found $($regions.Count) writable memory regions" -ForegroundColor Green
    return $regions
}

# Comprehensive value search
function Search-AllValues {
    param(
        [IntPtr]$ProcessHandle,
        [hashtable[]]$MemoryRegions,
        [int[]]$SearchValues,
        [string]$WeaponType
    )
    
    Write-Host "Searching for $WeaponType values..." -ForegroundColor Yellow
    
    $foundAddresses = @()
    $regionCount = 0
    
    foreach ($region in $MemoryRegions) {
        $regionCount++
        if ($regionCount % 20 -eq 0) {
            Write-Host "  Scanned $regionCount/$($MemoryRegions.Count) regions..." -ForegroundColor Gray
        }
        
        $buffer = New-Object byte[] $region.Size
        $bytesRead = 0
        
        if ([AdvancedMemoryEditor]::ReadProcessMemory($ProcessHandle, $region.BaseAddress, $buffer, $region.Size, [ref]$bytesRead)) {
            
            foreach ($value in $SearchValues) {
                $searchBytes = [BitConverter]::GetBytes([int32]$value)
                
                for ($i = 0; $i -le ($buffer.Length - 4); $i++) {
                    if ($buffer[$i] -eq $searchBytes[0] -and
                        $buffer[$i+1] -eq $searchBytes[1] -and
                        $buffer[$i+2] -eq $searchBytes[2] -and
                        $buffer[$i+3] -eq $searchBytes[3]) {
                        
                        $foundAddress = [IntPtr]::Add($region.BaseAddress, $i)
                        $foundAddresses += @{
                            Address = $foundAddress
                            Value = $value
                        }
                        
                        if ($foundAddresses.Count -le 10) {
                            Write-Host "  Found $value at: 0x$($foundAddress.ToString('X16'))" -ForegroundColor Green
                        }
                    }
                }
            }
        }
    }
    
    Write-Host "  Total $WeaponType values found: $($foundAddresses.Count)" -ForegroundColor Cyan
    return $foundAddresses
}

# Apply modifications
function Apply-Modifications {
    param(
        [IntPtr]$ProcessHandle,
        [hashtable[]]$Addresses,
        [int]$NewValue,
        [string]$WeaponType
    )
    
    if ($Addresses.Count -eq 0) {
        Write-Host "  No $WeaponType addresses to modify" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Modifying $WeaponType to $NewValue..." -ForegroundColor Yellow
    
    $newBytes = [BitConverter]::GetBytes([int32]$NewValue)
    $successCount = 0
    
    foreach ($addr in $Addresses) {
        $bytesWritten = 0
        if ([AdvancedMemoryEditor]::WriteProcessMemory($ProcessHandle, $addr.Address, $newBytes, 4, [ref]$bytesWritten)) {
            $successCount++
            if ($successCount -le 10) {
                Write-Host "  Modified: 0x$($addr.Address.ToString('X16')) (was $($addr.Value))" -ForegroundColor Green
            }
        }
    }
    
    Write-Host "  Successfully modified: $successCount/$($Addresses.Count) locations" -ForegroundColor Cyan
    return $successCount -gt 0
}

# Main execution
$memoryRegions = Get-MemoryRegions -ProcessHandle $processHandle
Write-Host ""

# Search for weapon values with expanded ranges
Write-Host "PISTOL MODIFICATION" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan
$pistolValues = @(15, 20, 22, 25, 27, 28, 30, 32, 35, 37, 40, 42, 45, 48, 50)
$pistolAddresses = Search-AllValues -ProcessHandle $processHandle -MemoryRegions $memoryRegions -SearchValues $pistolValues -WeaponType "Pistol"
$pistolSuccess = Apply-Modifications -ProcessHandle $processHandle -Addresses $pistolAddresses -NewValue 999 -WeaponType "Pistol"

Write-Host ""
Write-Host "MELEE MODIFICATION" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
$meleeValues = @(45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 140, 150, 160, 180, 200)
$meleeAddresses = Search-AllValues -ProcessHandle $processHandle -MemoryRegions $memoryRegions -SearchValues $meleeValues -WeaponType "Melee"
$meleeSuccess = Apply-Modifications -ProcessHandle $processHandle -Addresses $meleeAddresses -NewValue 1500 -WeaponType "Melee"

[AdvancedMemoryEditor]::CloseHandle($processHandle)

Write-Host ""
Write-Host "MODIFICATION COMPLETE!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

if ($pistolSuccess) {
    Write-Host "SUCCESS: Pistol upgraded to 999 damage flamethrower!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Pistol modification may have failed" -ForegroundColor Yellow
}

if ($meleeSuccess) {
    Write-Host "SUCCESS: Melee upgraded to 1500 damage lightning sword!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Melee modification may have failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "TEST YOUR WEAPONS NOW!" -ForegroundColor Yellow
Write-Host "Go fight some enemies and watch the carnage!" -ForegroundColor White

if ($pistolSuccess -or $meleeSuccess) {
    Write-Host ""
    Write-Host "SUCCESS! You are now an unstoppable Space Marine!" -ForegroundColor Red
}
