dir = true



ai {
   dir = true

	ai_smt_zone_enter {
		toolTip = "Mesh that borders the entry zone of a smart object "
		params = [
			{
			name     = group
			type     = int
			defVal   = "0"
			}
		]
		mapping = [
			{
			name = ai_smt_zone_enter_affix
				params {
					group = "<group>"
				}
			}
			{
			name = visibility_hidden
			}
		]
	}
	
	ai_smt_zone_exit {
		toolTip = "Mesh that borders the entry zone of a smart object "
		params = [
			{
			name     = group
			type     = int
			defVal   = "0"
			}
		]
		mapping = [
			{
			name = ai_smt_zone_exit_affix
				params {
					group = "<group>"
				}
			}
			{
			name = visibility_hidden
			}
		]
	}
	ai_nav_mesh_on {
		toolTip = "Include in navmesh generation"
	}
	ai_nav_mesh_off {
		toolTip = "Exclude in navmesh generation"
	}
    ai_nav_mesh_forced {
        toolTip = "Forcefully include in navmesh generation"
    }
   ai_disable_player_traversal {
      toolTip = "disable player traversal for autogenerated navlinks"
   }
	ai_traversal_volume {
		toolTip = "Navlink volume marker"
		params = [
			{
                name = type
                type = enum
                values = [
                    "include"
                    "exclude"
                ]
                defVal   = "include"
			}
			{
                name = nav_link_type
                type = enum
                values = [
                    "any"
                    "common"
                    "zombie_only"
                    "survivor_only"
                    "player"
                    "disabled"
                    "autolink"
                ]
                defVal   = "any"
			}
		]
	}
    ai_autolink_blocker {
		toolTip = "Mark an object to be blocker for autolinks"
        params = [
			{
                name = type
                type = enum
                values = [
                    "include"
                    "exclude"
                ]
                defVal   = "exclude"
			}
			{
                name = nav_link_type
                type = enum
                values = [
                    "any"
                    "common"
                    "zombie_only"
                    "survivor_only"
                    "player"
                    "disabled"
                    "autolink"
                ]
                defVal   = "any"
			}
		]
		mapping = [
            {
                name = export_preserve_geometry
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
                name = ai_traversal_volume
                params {
                    type = "<type>"
                    nav_link_type = "<nav_link_type>"
                }
			}
			{
                name = visibility_hidden
			}
			{
                name = ai_nav_mesh_off
			}
			{
                name = cdt_off_all
			}
		]
	}
    ai_spawn_points_grid_domain {
        toolTip = "Generates a grid of spawn points"
        params = [
            {
                name     = step
                type     = float
                defVal   = "-1"
            }
        ]
    }
	nav_mesh_seed {
		toolTip = "Mark an object as navmesh seed"
		mapping = [
			{
			name = nav_mesh_seed_affix
			}
			{
			name = export_preserve_geometry
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
		]
	}
	nav_mesh_silhouette {
		toolTip = "Mark an object as cutting silhouette for navmesh"
	}
	nav_mesh_material {
      toolTip = "[b]navmesh material[/b] of denoted geometry.\n[b]Arguments:\n[b]* type[/b] - material name."
      params = [
         {
            name  = type
            type  = enum
            values = [
						"npc_only"
						"no_simplification"
                  ]
            defVal   = "default"
         }
      ]
   }
   nav_mesh_material_painter {
	   toolTip = "Mark an object as material painter"
	   mapping = [
			{
			name = nav_mesh_material_painter_affix
			}
			{
			name = export_preserve_geometry
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
		]
   }
   nav_mesh_bound {
		toolTip = "[b]navmesh material[/b] of denoted geometry.\n[b]Arguments:\n[b]* type[/b] - material name."
		mapping = [
			{
			name = nav_mesh_bound_affix
			}
			{
			name = export_preserve_geometry
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
			{
			name = ai_nav_mesh_off
			}
		]
   }
   nav_mesh_carver_affix {
		toolTip = "Nav mesh carver"
   }
   nav_mesh_carver_excluding_affix {
		toolTip = "Nav mesh carver which cuts geometry inside before building navmesh"
   }
   nav_mesh_carver {
		toolTip = "Navmesh carver."
		mapping = [
			{
			name = nav_mesh_carver_affix
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = export_preserve_geometry
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
			{
			name = ai_nav_mesh_off
			}
		]
   }
   nav_mesh_carver_excluding {
		toolTip = "Navmesh carver which excludes inner geometry before building navmesh.\nShould be [b]convex[/b]."
		mapping = [
			{
			name = nav_mesh_carver_excluding_affix
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = export_preserve_geometry
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
			{
			name = ai_nav_mesh_off
			}
		]
   }
   nav_mesh_carver_excluding_geometry {
		toolTip = "Navmesh carver which excludes inner geometry before building navmesh. The carver itself serves as navmesh input geometry.\nShould be [b]convex[/b]."
		mapping = [
			{
			name = nav_mesh_carver_excluding_affix
			}
			{
			name = ai_nav_mesh_forced
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = export_preserve_geometry
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
		]
   }
   nav_wall_edges {
		toolTip = "Mark an object as wall edges, used for climbing animation"
   }
   nav_edge {
		toolTip = "Nav edges for ssl logic"
		mapping = [
			{
			name = nav_edge_affix
			}
			{
			name = export_preserve_geometry
			}
			{
			name = export_preserve_geometry_for_cpu
			}
			{
			name = visibility_hidden
			}
			{
			name = cdt_off_all
			}
			{
			name = ai_nav_mesh_off
			}
		]
   }
   nav_edge_name {
      toolTip = "For set name for nav edge, for ssl logic"
      params = [
         {
            name  = name
            type  = string
            defVal   = ""
         }
      ]
      mapping = [
         {
            name = nav_edge_name_affix
            params {
               name = "<name>"
            }
         }
         {
         name = export_preserve_geometry
         }
         {
         name = visibility_hidden
         }
         {
         name = ai_nav_mesh_off
         }
      ]
   }
   nav_player_autolink_blocker {
		toolTip = "Mark an object to be blocker for autolinks"
		mapping = [
            {
                name = export_preserve_geometry
			}
			{
                name = ai_disable_player_traversal
			}
			{
                name = visibility_hidden
			}
			{
                name = ai_nav_mesh_off
			}
			{
                name = cdt_off_all
			}
		]
	}
}

swarm {
   dir = true

   swarm_pyramid {
      toolTip = "Swarm pyramid occluder."
   }

   swarm_path_on_geom {
      toolTip = "Swarm path to use geom edges"
   }

   swarm_pyramid_broken_state {
      toolTip = "Swarm pyramid occluder for broken state"
      mapping = [
         {
            name = export_preserve_geometry
         }
         {
            name = swarm_pyramid_broken_state_affix
         }
      ]
   }
}

aliases {
   dir = true

   export_preserve_geometry_hier {
      is_hierarchical = true
      toolTip = "[b]Preserve geometry[/b] of this object.\nApplies to special objects which names and geometry must be preserved and geometry not merged during export process."
      mapping = [
         {
	         name = export_preserve_geometry
         }
      ]
   }

   alias_borderdust {
      toolTip = "Denotes a geometry with some kind of [b]dust or litter texture in corners.\nFor example - a litter between city asphalt road and pavement border."
      mapping = [
         {
            name = cdt_off_all
         }
         {
            name = ai_nav_mesh_off
         }
         {
            name = lighting_lm_shadow_dont_cast
         }
         {
            name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
         {
         name = sorting_transparent_group
            params {
                group_number = "0"
            }
         }
      ]
   }
   alias_decal_ak {
      toolTip = "Denotes an [b]alfakill decals[/b] (sorting_z_bias = 1; sorting_transparent_group = 0)."
      mapping = [
         {
		 name = cdt_off_all
		 }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
         {
         name = decal
         }
         {
         name = sorting_transparent_group
         params {
            group_number = "0"
         }
         }
         {
         name = lighting_lm_to_vertex_color
         }

      ]
   }
   alias_decal_ak_bullet_cdt {
      toolTip = "Denotes an [b]alfakill decals[/b] with bullet collision enabled."
      mapping = [
		{
			name = cdt_on_wpn_bullet
		}
		{
			name = cdt_on_wpn_target
		}
		{
			name = cdt_on_wpn_sfx
		}
		{
			name = cdt_on_scorch
		}
        {
            name = ai_nav_mesh_off
        }
		{
			name = lighting_lm_shadow_dont_cast
		}
		{
			name = lighting_sm_dont_cast_shadow
		}
		{
			name = sorting_z_bias
			params {
			   value = "1"
			}
		}
		{
			name = decal
		}
		{
			name = sorting_transparent_group
			params {
			   group_number = "0"
			}
		}
		{
			name = lighting_lm_to_vertex_color
		}

      ]
   }
   alias_decal_ak_layer2 {
      toolTip = "Denotes an [b]alfakill decals[/b] (sorting_z_bias = 2; sorting_transparent_group = 1)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "2"
            }
         }
			{
			name = decal
			}
			{
			name = sorting_transparent_group
            params {
               group_number = "1"
            }
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_ak_layer3 {
      toolTip = "Denotes an [b]alfakill decals[/b] (sorting_z_bias = 3; sorting_transparent_group = 2)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "3"
            }
         }
			{
            name = decal
			}
			{
			name = sorting_transparent_group
            params {
               group_number = "2"
            }
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_ak_layer4 {
      toolTip = "Denotes an [b]alfakill decals[/b] (sorting_z_bias = 4; sorting_transparent_group = 3)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "4"
            }
         }
			{
            name = decal
			}
			{
			name = sorting_transparent_group
            params {
               group_number = "3"
            }
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_transp {
      toolTip = "Denotes a [b]decals[/b] with [b]full range transparency (sorting_z_bias = 0; sorting_transparent_group = 1)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_transparent_group
            params {
               group_number = "0"
            }
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
			{
			name = decal
			}
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = "vid.sdr.glt.takeFogFromBackground"
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_transp_layer2 {
      toolTip = "Denotes a [b]decals[/b] with [b]full range transparency (sorting_z_bias = 1; sorting_transparent_group = 2)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_transparent_group
            params {
               group_number = "1"
            }
         }
         {
         name = sorting_z_bias
            params {
               value = "2"
            }
         }
			{
            name = decal
			}
         {
            name = lighting_sm_dont_cast_shadow
         }
         {
            name = "vid.sdr.glt.takeFogFromBackground"
         }
			{
            name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_transp_layer3 {
      toolTip = "Denotes a [b]decals[/b] with [b]full range transparency (sorting_z_bias = 2; sorting_transparent_group = 3)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_transparent_group
            params {
               group_number = "2"
            }
         }
         {
         name = sorting_z_bias
            params {
               value = "3"
            }
         }
			{
			name = decal
			}
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = "vid.sdr.glt.takeFogFromBackground"
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_transp_layer4 {
      toolTip = "Denotes a [b]decals[/b] with [b]full range transparency (sorting_z_bias = 3; sorting_transparent_group = 4)."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_transparent_group
            params {
               group_number = "3"
            }
         }
         {
         name = sorting_z_bias
            params {
               value = "4"
            }
         }
			{
			name = decal
			}
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = "vid.sdr.glt.takeFogFromBackground"
         }
			{
			name = lighting_lm_to_vertex_color
			}
      ]
   }
   alias_decal_corner {
      toolTip = "Denotes a [b]corner decals[/b]."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = shading_discontinuity
            params {
               angle = "120"
            }
         }
      ]
   }
   alias_decal_corner_layer2 {
      toolTip = "Denotes a [b]corner decals[/b]."
      mapping = [
         {
         name = cdt_off_all
         }
         {
	     name = ai_nav_mesh_off
         }
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = sorting_z_bias
            params {
               value = "2"
            }
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = shading_discontinuity
            params {
               angle = "120"
            }
         }
      ]
   }
   alias_garbage_heap {
      toolTip = "Denotes a [b]garbage heap[/b] geometry."
      mapping = [
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
      ]
   }
   alias_garbage_heap_blend {
      toolTip = "Denotes a [b]garbage heap with blend[/b]."
      mapping = [
         {
         name = lighting_lm_shadow_dont_cast
         }
         {
         name = lighting_sm_dont_cast_shadow
         }
         {
         name = sorting_z_bias
            params {
               value = "1"
            }
         }
         {
         name = "vid.sdr.glt.blendMode"
            params {
               blendMode = "blend"
            }
         }
      ]
   }
   alias_grating {
      toolTip = "Denotes a [b]grating[/b] geometry."
      mapping = [
        {
            name = cdt_on_wpn_rocket
        }
        {
            name = cdt_on_wpn_sfx
        }
        {
            name = cdt_on_scorch
        }
        {
            name = cdt_on_rain
        }
        {
            name = cdt_on_ability_projectile
        }
        {
            name = ai_nav_mesh_off 
        }
        {
            name = lighting_lm_shadow_dont_cast
        }
      ]
   }
   alias_light_rays {
		toolTip = "Denotes a [b]light rays[/b] geometry."
		mapping = [
			{
				name = cdt_off_all
			}
            {
            name = ai_nav_mesh_off
            }
			{
				name = shading_double_sided
			}
			{
				name = sorting_transparent_group
				params {
				   group_number = "224"
				}
			}
			{
				name = lighting_dynamic_lighting_dont_recieve
			}
			{
				name = lighting_lm_invisible
			}
			{
				name = lighting_sm_dont_cast_shadow
			}
			{
				name = lighting_sm_dont_recieve_sm_lighting
			}
			{
				name = lighting_shoot_through
			}
			{
				name = decal_dont_apply
			}
		]
   }
   alias_rain_water_surf {
      toolTip = "Denotes a [b]rain water surface[/b] on the ground."
      mapping = [
         {
         name = anim_frame_base_texture
            params {
               number_of_rows = "1"
               number_of_columns = "32"
               cycle_time = "0.4"
               start_time = "0"
               number_of_frames = "-1"
					frame_x_size = "-1"
					frame_y_size = "-1"
            }
         }
         {
			name = lighting_lm_dont_apply
         }
         {
			name = lighting_lm_shadow_dont_cast
         }
         {
			name = lighting_sm_dont_cast_shadow
         }
         {
			name = cdt_off_all
         }
         {
            name = ai_nav_mesh_off
         }
      ]
   }
   alias_wet_material {
		is_hierarchical = true
      toolTip = "Applies a [b]wet material[/b] to denoted geometry. [b]Hierarchical affix[/b]."
      mapping = [
			{
			name = "vid.sdr.glt.specular.intensity"
            params {
               blendMode = "2"
            }
         }
			{
			name = "vid.sdr.glt.specular.power"
            params {
               blendMode = "256"
            }
         }
			{
			name = "vid.sdr.glt.detail.scale"
            params {
               blendMode = "0.1"
            }
         }
			{
			name = "vid.sdr.glt.reflection.intensity"
            params {
               blendMode = "0.2"
            }
         }
			{
			name = "vid.sdr.glt.tex.spec"
            params {
               blendMode = "wet_mat_spec"
            }
         }
			{
			name = "vid.sdr.glt.tex.refCube"
            params {
               blendMode = "glass_env_refl_cube"
            }
         }
         {
            name = "vid.sdr.glt.reflection.overrideReflectionMaskWithWhite"
            params {
               blendMode = "true"
            }
         }
         {
	    name = "vid.mtl_strict"
            params {
               material_name = "mtl_wet"
            }
         }
		]
   }
   alias_wires {
      toolTip = "Denotes a [b]wires[/b] geometry."
      mapping = [
         {
			name = cdt_off_all
         }
         {
            name = ai_nav_mesh_off
         }
      ]
   }
   alias_reflection_billboard {
      toolTip = "Mark [b]planes[/b] that will be visible in reflections (and only reflections)"
       params = [
         {
            name     = intensity
            type     = float
            defVal   = "5"
         }
      ]
      mapping = [
      {
         name = reflection_billboard_system
         params {
            intensity = "<intensity>"
         }
      }
      {
         name = export_preserve_position
      }
      {
         name = export_preserve_geometry
      }
      {
         name = export_preserve_hierarchy
      }
      {
         name = compound_skin_exclude
      }
      {
         name = visibility_hidden
      }
      ]
   }
   alias_reflection_cubemap_geometry {
      toolTip = "Mark geometry that define the [b]volume of cubemap influence[/b]"
      mapping = [
      {
         name = cdt_off_all
      }
      {
	     name = ai_nav_mesh_off
      }
      {
         name = export_preserve_position
      }
      {
         name = export_preserve_geometry
      }
      {
         name = export_no_vertex_compression
      }
      {
         name = visibility_hidden
      }
      ]
   }
   alias_visibility_reflection_cubemap_only {
      toolTip = "Mark geometry that will be visible in reflection [b]cubemaps only[/b]"
      mapping = [
      {
         name = visibility_visible_in_reflection_cubemap_only
      }
      {
         name = lighting_lm_invisible
      }
      {
         name = lighting_sm_dont_cast_shadow
      }
      {
         name = cdt_off_all
      }
      ]
   }

}

animation {
   dir = true

   frame {
      dir = true

      anim_frame_base_texture {
         toolTip = "[b]Frame animation[/b] of base texture.\n[b]Arguments:\n* number_of_rows\n* number_of_columns\n* cycle_time\n* start_time\n* number_of_frames (calculated automatically)\n* frame_x_size (calculated automatically)\n* frame_y_size (calculated automatically)\n"
         params = [
            {
               name  = number_of_rows
               type  = int
               defVal   = "8"
            }
            {
               name  = number_of_columns
               type  = int
               defVal   = "8"
            }
            {
               name  = cycle_time
               type  = float
               defVal   = "1"
            }
            {
               name  = start_time
               type  = float
               defVal   = "0"
            }
            {
               name  = number_of_frames
               type  = int
               defVal   = "-1"
            }
            {
               name  = frame_x_size
               type  = float
               defVal   = "-1"
            }
            {
               name  = frame_y_size
               type  = float
               defVal   = "-1"
            }
         ]
      }

      anim_frame_random_base_texture {
         toolTip = "[b]Frame animation[/b] of base texture in [b]random frame[/b] order.\n[b]Arguments:\n* number_of_rows\n* number_of_columns\n* number_of_frames\n* cycle_time"
         params = [
            {
               name  = number_of_rows
               type  = int
               defVal   = "8"
            }
            {
               name  = number_of_columns
               type  = int
               defVal   = "8"
            }
            {
               name  = number_of_frames
               type  = int
               defVal   = "-1"
            }
            {
               name  = cycle_time
               type  = float
               defVal   = "1"
            }
         ]
      }
   }

   misc {
      dir = true
      anim_leaf_joint_in_skin_lod {
         toolTip = "Defines joint that will accumulates all influences from all its child joints through the joint hierarchy for corresponding vertices that are skinned to these joints.\nThus all child joints will have zero influence on skinned geometry and less shader processing is required for that geometry on render.\n[b]lod_index[/b] parameter defines the LOD index of the mesh (0 - most highpoly mesh LOD in chain):\nall LOD meshes with index equal and below lod_index will be processed.\n[b]Optimization affix[\b]"	
         params = [
         	{
            		name  = lod_index
            		type  = int
            		defVal   = "1"
         	}
         ]
      }	

      anim_disable_animation {
         toolTip = "[b]Disables animation[/b] on selected objects.\nWidely used on skeleton systems for optimization purposes."
      }

      anim_char_eyes {
         toolTip = "Used for marking [b]character eyes[/b]"
      }

      anim_joint_optional_char_part {
         toolTip = "Used for marking unused [b]joints[/b] from [b]Character Builder[/b] parts"
      }

      anim_facial_only {
         toolTip = "Denoted [b]joints[/b] will be used only in [b]facial animation.[/b]"
			params = [
            {
               name  = type
               type    = enum
               values   = [
                  "lipsync"
                  "emotion"
                  "blink"
               ]
               defVal   = "lipsync"
            }
			]
      }
	  
	  anim_object_rotation_from_parent {
         toolTip = "Rotation of an object with this affix will be taken from its parent and applied with regards to own object's pivot."
      }

      anim_disable_additional_animation {
         toolTip = "[b]Disables additional animation[/b] on selected objects."
      }

      anim_object_billboard {
         toolTip = "Denotes a [b]billboard[/b] object - object that always aiming to camera by it's z-axis.\n[b]Arguments:\n[b]* is_aligned_y[/b] - object rotates only by y-axis when aims to camera\n[b]* is_fading[/b] - if [b]is_aligned_y[/b] is true object fades when reaches some angle to camera. currenty fade works only on additive transparent objects.\n[b]* fade_angle_start[/b] - the start fade angle of fade if [b]is_fading[/b] is true.\n[b]* fade_angle_end[/b] - the end fade angle of fade if [b]is_fading[/b] is true.\n[b]* precision[/b] - precise rotation. By default object rotates with some stepping because of optimization. [b]Skin compound is disabled[/b] with this affix! This can lead to big DIP number. So you should merge objects manually if it is possible."
         params = [
            {
               name  = is_aligned_y
               type  = bool
               defVal   = "0"
            }
            {
               name  = is_fading
               type  = bool
               defVal   = "0"
            }
            {
               name  = fade_angle_start
               type  = float
               defVal   = "10"
            }
            {
               name  = fade_angle_end
               type  = float
               defVal   = "0"
            }
            {
               name  = precision
               type  = bool
               defVal   = "0"
            }
         ]
      }

      anim_object_transparency_blink {
         toolTip = "Denotes a [b]blink[/b] animated object.\n[b]Arguments:\n* period\n* stay_on_time\n* stay_off_time\n* initial_time_offset"
         params = [
            {
               name  = period
               type  = float
               defVal   = "1"
            }
            {
               name  = stay_on_time
               type  = float
               defVal   = "0"
            }
            {
               name  = stay_off_time
               type  = float
               defVal   = "0"
            }
            {
               name  = initial_time_offset
               type  = float
               defVal   = "0"
            }
         ]
      }

      anim_object_transparency_distance_fade {
         toolTip = "Denotes an object that [b]fades with distance[/b].\n[b]Arguments:\n* alpha_min\n* alpha_max\n* distance_start\n* distance_end"
         params = [
            {
               name  = alpha_min
               type  = float
               defVal   = "0"
            }
            {
               name  = alpha_max
               type  = float
               defVal   = "255"
            }
            {
               name  = distance_start
               type  = float
               defVal   = "10"
            }
            {
               name  = distance_end
               type  = float
               defVal   = "20"
            }
         ]
      }      
   }

   random_transformations {
      dir = true

      anim_object_rotate_random_xyz {
         toolTip = "Object with [b]random rotation[/b] by [b]all axes.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "0"
            }
            {
               name  = random_value
               type  = float
               defVal   = "10"
            }
         ]
      }

      anim_object_rotate_random_x {
         toolTip = "Object with [b]random rotation[/b] by [b]x-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "0"
            }
            {
               name  = random_value
               type  = float
               defVal   = "10"
            }
         ]
      }

      anim_object_rotate_random_y {
         toolTip = "Object with [b]random rotation[/b] by [b]y-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "0"
            }
            {
               name  = random_value
               type  = float
               defVal   = "10"
            }
         ]
      }

      anim_object_rotate_random_z {
         toolTip = "Object with [b]random rotation[/b] by [b]z-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "0"
            }
            {
               name  = random_value
               type  = float
               defVal   = "10"
            }
         ]
      }

      anim_object_scale_random_xyz {
         toolTip = "Object with [b]random scaling[/b] by [b]all axes.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "1"
            }
            {
               name  = random_value
               type  = float
               defVal   = "0.5"
            }
         ]
      }

      anim_object_scale_random_x {
         toolTip = "Object with [b]random scaling[/b] by [b]x-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "1"
            }
            {
               name  = random_value
               type  = float
               defVal   = "0.5"
            }
         ]
      }

      anim_object_scale_random_y {
         toolTip = "Object with [b]random scaling[/b] by [b]y-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "1"
            }
            {
               name  = random_value
               type  = float
               defVal   = "0.5"
            }
         ]
      }

      anim_object_scale_random_z {
         toolTip = "Object with [b]random scaling[/b] by [b]z-axis.\n[b]Arguments:\n* initial_value\n* random_value"
         params = [
            {
               name  = initial_value
               type  = float
               defVal   = "1"
            }
            {
               name  = random_value
               type  = float
               defVal   = "0.5"
            }
         ]
      }

      anim_object_visibility_random {
         toolTip = "Object with [b]random visibility."
      }
   }
   uv {
      dir = true


      anim_uv_translate_u_base_texture {
         toolTip = "Object with [b]base texture U translate[/b] animation.\n[b]Arguments:\n* scroll_speed\n* initial_offset"
         params = [
            {
               name  = scroll_speed
               type  = float
               defVal   = "1"
            }
            {
               name  = initial_offset
               type  = float
               defVal   = "0"
            }
         ]
      }

      anim_uv_translate_v_base_texture {
         toolTip = "Object with [b]base texture V translate[/b] animation.\n[b]Arguments:\n* scroll_speed\n* initial_offset"
         params = [
            {
               name  = scroll_speed
               type  = float
               defVal   = "1"
            }
            {
               name  = initial_offset
               type  = float
               defVal   = "0"
            }
         ]
      }
		
	}
}

appearance {
   dir = true
   appearance_material_group {
      toolTip = "Applies appearance's material group. Appearance manager will change material on each member of group"
      params = [
         {
            name  = material_group
            type  = string
            defVal   = ""
         }
      ]
   }
   facegen_normals_lock {
      toolTip = "Mark object as one that doesn't need to converting normals by facegen"
   }
}

cdt {
   dir = true

	aliases_cdt {
      dir = true

      cdt_high_res_geometry {
         toolTip = "Denotes a [b]high resolution geometry[/b] - collision with projectiles, scorchmarks, rain.\nUsually works in conjunction with cdt_low_res_geometry."
         mapping = [
				{
				name = cdt_on_wpn_bullet
				}
				{
				name = cdt_on_wpn_target
				}
				{
				name = cdt_on_wpn_rocket
				}
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_scorch
				}
				{
				name = cdt_on_rain
				}
                {
                name = cdt_on_ability_projectile
                }
				{
				name = ai_nav_mesh_off 
				}
         ]
      }
      cdt_high_res_geometry_bullets_fly_through {
         toolTip = "Denotes a [b]high resolution geometry[/b] - collision with scorchmarks, rain, non-bullet projectiles.\nUsually works in conjunction with cdt_low_res_geometry."
         mapping = [
				{
				name = cdt_on_wpn_rocket
				}
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_scorch
				}
				{
				name = cdt_on_rain
				}
                {
                name = cdt_on_ability_projectile
                }
				{
				name = ai_nav_mesh_off 
				}
         ]
      }
      cdt_high_res_vegetation_leaves {
         toolTip = "Denotes a [b]high resolution geometry of leaves in vegetation assets[/b] - acts like [b]cdt_high_res_geometry[/b], but ignores all projectiles."
         mapping = [
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_scorch
				}
				{
				name = cdt_on_rain
				}
				{
				name = ai_nav_mesh_off 
				}
         ]
      }
      cdt_low_res_geometry {
         toolTip = "Denotes a [b]low resolution geometry[/b] - invisible collision with player, physics, vehicles and NPC.\nUsually works in conjunction with [b]cdt_high_res_geometry."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_ai_move
				}
				{
				name = cdt_on_ai_ext_move
				}
				{
				name = cdt_on_ai_fly
				}
				{
				name = cdt_on_ai_look
				}
				{
				name = cdt_on_phys_move
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_camera
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
      cdt_low_res_geometry2 {
         toolTip = "Denotes a [b]low resolution geometry with additional collisions[/b] - invisible collision with player, physics, vehicles and NPC.\nUsually that affix assumes that high-res geometry does not have any collisions ([b]cdt_off_all[/b])."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_ai_move
				}
				{
				name = cdt_on_ai_ext_move
				}
				{
				name = cdt_on_ai_fly
				}
				{
				name = cdt_on_ai_look
				}
				{
				name = cdt_on_phys_move
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_camera
				}
				{
				name = cdt_on_wpn_bullet
				}
				{
				name = cdt_on_wpn_target
				}
				{
				name = cdt_on_wpn_rocket
				}
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
      cdt_low_res_geometry_no_camera {
         toolTip = "Denotes a [b]low resolution geometry[/b] - invisible collision with player, physics, vehicles and NPC.\nSame as [b]cdt_low_res_geometry[/b] but does not include camera collision. Usually works in conjunction with [b]cdt_high_res_geometry."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_ai_move
				}
				{
				name = cdt_on_ai_ext_move
				}
				{
				name = cdt_on_ai_fly
				}
				{
				name = cdt_on_ai_look
				}
				{
				name = cdt_on_phys_move
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
      cdt_low_res_geometry2_no_camera {
         toolTip = "Denotes a [b]low resolution geometry with additional collisions[/b] - invisible collision with player, physics, vehicles and NPC, but does not include camera collision.\nUsually that affix assumes that high-res geometry does not have any collisions ([b]cdt_off_all[/b])."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_ai_move
				}
				{
				name = cdt_on_ai_ext_move
				}
				{
				name = cdt_on_ai_fly
				}
				{
				name = cdt_on_ai_look
				}
				{
				name = cdt_on_phys_move
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_wpn_bullet
				}
				{
				name = cdt_on_wpn_target
				}
				{
				name = cdt_on_wpn_rocket
				}
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
	  cdt_low_res_geometry_no_cam_no_ai {
         toolTip = "Denotes a [b]low resolution geometry[/b] - invisible collision with player, physics and vehicles, but does not include NPC and camera collision.\nUsually that affix assumes that high-res geometry does not have any collisions ([b]cdt_off_all[/b])."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
	  cdt_low_res_geometry2_no_cam_no_ai {
         toolTip = "Denotes a [b]low resolution geometry with additional collisions[/b] - invisible collision with player, physics and vehicles, but does not include NPC and camera collision.\nUsually that affix assumes that high-res geometry does not have any collisions ([b]cdt_off_all[/b])."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_wpn_bullet
				}
				{
				name = cdt_on_wpn_target
				}
				{
				name = cdt_on_wpn_rocket
				}
				{
				name = cdt_on_wpn_sfx
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
      cdt_low_res_geometry_ai_look_through {
         toolTip = "Denotes a [b]low resolution geometry[/b] - invisible collision with player, physics, vehicles and NPC.\nSame as [b]cdt_low_res_geometry[/b] but does not include ai look collision. Usually works in conjunction with [b]cdt_high_res_geometry."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_on_ai_move
				}
				{
				name = cdt_on_ai_ext_move
				}
				{
				name = cdt_on_ai_fly
				}
				{
				name = cdt_on_phys_move
				}
				{
				name = cdt_on_plr_move
				}
				{
				name = cdt_on_ability_move
				}
				{
				name = cdt_on_vhc_move
				}
				{
				name = cdt_on_camera
				}
				{
				name = cdt_on_sound_occlusion
				}
				{
				name = cdt_on_sound_obstruction
				}
         ]
      }
	 cdt_on_team1_collision {
				toolTip = "Enable [b]Team1 move collision[/b] on denoted object.\nTeam1 [b]can't move[/b] through this geometry."
			mapping = [
					{
					name = visibility_hidden
					}
					{
					name = cdt_on_scorch
					}
					{
					name = cdt_on_wpn_bullet
					}
					{
					name = cdt_on_wpn_target
					}
					{
					name = cdt_on_phys_move
					}
					{
					name = cdt_on_wpn_rocket
					}
					{
					name = cdt_on_wpn_sfx
					}
					{
					name = cdt_off_camera
					}
					{
					name = cdt_on_team1_move
					}
					{
					name = cdt_on_sound_occlusion
					}
					{
					name = cdt_on_sound_obstruction
					}
				]
		}
		cdt_on_team2_collision {
				toolTip = "Enable [b]Team2 move collision[/b] on denoted object.\nTeam2 [b]can't move[/b] through this geometry."
			mapping = [
					{
					name = visibility_hidden
					}
					{
					name = cdt_on_scorch
					}
					{
					name = cdt_on_wpn_bullet
					}
					{
					name = cdt_on_wpn_target
					}
					{
					name = cdt_on_phys_move
					}
					{
					name = cdt_on_wpn_rocket
					}
					{
					name = cdt_on_wpn_sfx
					}
					{
					name = cdt_off_camera
					}
					{
					name = cdt_on_team2_move
					}
					{
					name = cdt_on_sound_occlusion
					}
					{
					name = cdt_on_sound_obstruction
					}
				]
		}
      cdt_nav_mesh_geometry {
         toolTip = "Denotes a [b]low resolution geometry[/b] used for building [b]navigation mesh."
         mapping = [
				{
				name = visibility_hidden
				}
				{
				name = cdt_off_all
				}
				{
				name = ai_nav_mesh_on
				}
         ]
      }
		
   }
   cdtbox {
      dir = true
      cdt_cdtbox_anim {
         toolTip = "Denotes a [b]collision box[/b] for [b]animated part[/b] of scene.\nApplies to polygon cube.\nAll animated obects within that cube will be included in collision calculations."
      }
      cdt_cdtbox_static {
         toolTip = "Denotes a [b]collision box[/b] for for [b]static part[/b] of scene.\nApplies to polygon cube.\nAll static geometry within that cube will be included in collision calculations."
      }
   }

   cdt_halo {
      dir = true

      cdt_halo_add {
         toolTip = "Denotes an [b]additional collision[/b] geometry."
      }
   }

   misc {
      dir = true
      cdt_disable_climb {
         toolTip = "Disable climp up the steep slopes for player."
      }
      cdt_fence {
         toolTip = "Denotes that object is looking through."
      }
      cdt_projectile_fly_through {
         toolTip = "Denotes geometry that will be ignored at collision detection for special projectiles (like grappling hooks, etc.)."
      }
      cdt_drone_fly_through {
         toolTip = "Denotes geometry that will be ignored at collision detection for flying drone (ignoring other cdt affixes)."
      }
      cdt_look_trigger_geometry {
         toolTip = "Set cdt_look_trigger_geometry on object, to enable it in look trigger in view check"
      }   
	  cdt_aim_assist_visibility_ignore {
         toolTip = "Ignore this object for aim assist visibility checks"
      }   
      cdt_soul_constraint {
         toolTip = "Denotes geometry that will be used in collision checks with souls (Quake specific)"
      }
      cdt_soul_ignore {
         toolTip = "Denotes geometry that will be skipped in collision checks with souls (Quake specific)"
      }
      cdt_grappling_hook_unreachable {
         toolTip = "Denotes geometry that will be detected as unreachable for grappling hook ability"
      }
     cdt_special_mesh {
         toolTip = "[b]Special collision geometry[/b]"
         params = [
            {
               name  = name
               type  = string
               defVal   = "root"
            }
            {
               name  = material
               type  = enum
               values   = [
                  "armor"
                  "chaos_armor"
                  "chaos_energy"
                  "chaos_flesh"
                  "chaos_shield"
                  "cosmic_flesh"
                  "default"
                  "energy_shield"
                  "flesh"
                  "flesh_head"
                  "flesh_player"
                  "glass"
                  "marine_armor"
                  "marine_flesh"
                  "marine_shield"
                  "metal"
                  "oil"
                  "silver_slime"
                  "silver_slime_hard"
                  "slime_cocoon"
                  "toxic"
                  "tyranid_armor"
                  "tyranid_flesh"
                  "tyranid_shield"
                  "zombie_flesh"
                  "zombie_flesh_head"
               ]
               defVal = "default"
            }
            {
               name = lod
               type = enum
               values   = [
                  "hide_never"
                  "hide_far"
                  "hide_near"
               ]
               defVal = "hide_never"
            }
            {
               name = collision_type
               type = enum
               values   = [
                  "0"
                  "1"
                  "2"
                  "3"
                  "4"
                  "5"
               ]
               defVal = "0"
            }			
         ]
      }
	  cdt_mud_level {
         toolTip = "Denotes geometry that will be used as a markup for mud gameplay queries"
      }
      force_validate_anim {
         toolTip = "[b]Marked as force validated in animation[/b]"
      }
	  
	  cdt_footsteps {
         toolTip = "Enable [b]footsteps collision[/b] on denoted object."
      }
	  
	  cdt_on_crawlingers {
         toolTip = "Denotes geometry that will be used as floor to crawlingers entities."
	  }
   }

   switchers_on {
      dir = true
		cdt_on_ai_look {
         toolTip = "Enable [b]AI look collision[/b] on denoted object.\nNPC [b]can't see[/b] through this geometry."
      }
		cdt_on_ai_move {
         toolTip = "Enable [b]AI move collision[/b] on denoted object.\nNPC [b]can't move[/b] through this geometry."
      }
		cdt_on_ai_ext_move {
         toolTip = "Enable [b]AI ext move collision[/b] on denoted object.\nSpecial NPC [b]can't move[/b] through this geometry."
      }
		cdt_on_ai_fly {
         toolTip = "Enable [b]AI fly collision[/b] on denoted object.\nNPC [b]can't fly[/b] through this geometry."
      }
		cdt_on_phys_move {
         toolTip = "Enable [b]collision with physics[/b] on denoted object."
      }
		cdt_on_plr_move {
         toolTip = "Enable [b]Player move collision[/b] on denoted object.\nPlayer [b]can't move[/b] through this geometry."
      }
		cdt_on_ability_move {
         toolTip = "Enable [b]character ability-driven movement[/b] on denoted object.\nCharacter [b]can't move[/b] through this geometry using abilities (such as grappling hook etc.)."
      }
		cdt_on_team1_move {
         toolTip = "Enable [b]Team1 move collision[/b] on denoted object.\nTeam1 [b]can't move[/b] through this geometry."
      }
		cdt_on_team2_move {
         toolTip = "Enable [b]Team2 move collision[/b] on denoted object.\nTeam2 [b]can't move[/b] through this geometry."
      }
		cdt_on_rain {
         toolTip = "Enable [b]collision with rain[/b] on denoted object."
      }
		cdt_on_scorch {
         toolTip = "Enable [b]collision with scorchmarks[/b] on denoted object.\nScorchmarks [b]will appear[/b] on this geometry when bullet hit the surface."
      }
		cdt_on_vhc_move {
         toolTip = "Enable [b]vehicle move collision[/b] on denoted object.\nVehicle [b]can't move[/b] through this geometry."
      }
		cdt_on_wpn_bullet {
         toolTip = "Enable [b]weapon bullet collision[/b] on denoted object.\nBullets [b]can't fly[/b] through this geometry."
      }
		cdt_on_wpn_target {
         toolTip = "Enable [b]weapon target collision[/b] on denoted object.\nWeapon will aim in this geometry."
      }
		cdt_on_wpn_rocket {
         toolTip = "Enable [b]weapon rocket collision[/b] on denoted object.\nRockets [b]can't fly[/b] through this geometry."
      }
      cdt_on_wpn_winch {
         toolTip = "Enable [b]winch collision[/b] on denoted object.\nWinch [b]can be attached[/b] to this geometry."
      }
		cdt_on_wpn_sfx {
         toolTip = "Enable [b]weapon SFX collision[/b] on denoted object.\nSFX [b]will be played[/b] on this geometry when bullet or rocket hit the surface."
      }
        cdt_on_ability_projectile {
         toolTip = "Enable [b]character ability projectile collision[/b] on denoted object.\nAbility projectiles [b]can't fly[/b] through this geometry."
      }
      cdt_on_camera {
         toolTip = "Enable [b]camera collision[/b] on denoted object.\nCamera [b]can't fly[/b] through this geometry."
      }
      cdt_on_spectator_camera {
         toolTip = "Enable [b]spectator camera collision[/b] on denoted object.\nCamera [b]can't fly[/b] through this geometry."
      }
		cdt_on_hideout_incl {
         toolTip = "Enable including this geometry into hideout processing"
      }
		cdt_on_hideout_excl {
         toolTip = "Enable excluding this geometry from hideout processing"
      }
		cdt_on_sound_occlusion {
			toolTip = "Enable sound occlusion on denoted object"
		}
		cdt_on_sound_obstruction {
			toolTip = "Take denoted object into account during generating level map for sound path calculations"
		}
	}

   switchers_off {
      dir = true
		cdt_off_all {
			toolTip = "[b]Disable collision[/b] system on denoted object"
      }
		cdt_off_ai_look {
         toolTip = "Disable [b]AI look collision[/b] on denoted object.\nNPC [b]can see[/b] through this geometry."
      }
		cdt_off_phys_move {
         toolTip = "Disable [b]collision with physics[/b] on denoted object."
      }
		cdt_off_rain {
         toolTip = "Disable [b]collision with rain[/b] on denoted object."
      }
		cdt_off_scorch {
         toolTip = "Disable [b]collision with scorchmarks[/b] on denoted object.\nScorchmarks [b]will not appear[/b] on this geometry when bullet hit the surface."
      }
		cdt_off_wpn_bullet {
         toolTip = "Disable [b]weapon bullet collision[/b] on denoted object.\nBullets [b]can fly[/b] through this geometry."
      }
		cdt_off_wpn_target {
         toolTip = "Disable [b]weapon target collision[/b] on denoted object.\nWeapon will aim through this geometry."
      }
		cdt_off_wpn_rocket {
         toolTip = "Disable [b]weapon rocket collision[/b] on denoted object.\nRockets [b]can fly[/b] through this geometry."
      }
      cdt_off_wpn_winch {
         toolTip = "Disable [b]winch collision[/b] on denoted object.\nWinch [b]can't be attached[/b] to this geometry."
      }
		cdt_off_wpn_sfx {
         toolTip = "Disable [b]weapon SFX collision[/b] on denoted object.\nSFX [b]will not be played[/b] on this geometry when bullet or rocket hit the surface."
      }
		cdt_off_camera {
         toolTip = "Disable [b]camera collision[/b] on denoted object.\nCamera [b]can fly[/b] through this geometry."
      }
        cdt_off_spectator_camera {
         toolTip = "Disable [b]spectator camera collision[/b] on denoted object.\nCamera [b]can fly[/b] through this geometry."
      }
        cdt_off_ability_projectile {
         toolTip = "Disable [b]character ability projectile collision[/b] on denoted object.\nAbility projectiles [b]can fly[/b] through this geometry."
      }
        cdt_off_wall_jump {
         toolTip = "Disable [b]wall jumps[/b] on collision with denoted geometry."
      }
		cdt_off_hideout_incl {
         toolTip = "Disable including this geometry into hideout processing"
      }
		cdt_off_hideout_excl {
         toolTip = "Disable excluding this geometry from hideout processing"
      }
		cdt_off_sound_occlusion {
			toolTip = "Disable sound occlusion on denoted object"
		}
		cdt_off_sound_obstruction {
			toolTip = "Ignore denoted object during generating level map for sound path calculations"
		}
	}
}

cloth {
   dir = true

   cloth_material {
     toolTip = "Internal affix, better use [b]cloth_render_mesh[/b] alias in art setup! Denoted geometry will be rendered as cloth"
   }

   cloth_simulation_mesh {
      toolTip = "Denoted geometry will be processed as cloth simulated mesh in engine" 
      mapping = [
         {
            name = export_preserve_position
         }
         {
            name = export_preserve_geometry
         }
         {
            name = export_preserve_geometry_for_cpu
         }
         {
            name = export_preserve_hierarchy
         }
         {
            name = render_preserve_uv_data
         }
         {
            name = cdt_off_all
         }
      ]
   }

   cloth_render_mesh {
      toolTip = "Denoted geometry will be processed as cloth rendered mesh in engine"
      mapping = [
         {
            name = cloth_material
         }
         {
            name = export_preserve_position
         }
         {
            name = export_preserve_geometry
         }
         {
            name = export_preserve_geometry_for_cpu
         }
         {
            name = export_preserve_hierarchy
         }
         {
            name = render_preserve_uv_data
         }
         {
            name = cdt_off_all
         }
      ]
   }
}

compound {
   dir = true

   compound_skin_dont_delete_originals {
      toolTip = "Don't delete [b]original[/b] objects [b]geometry[/b] after skin compound creation.\nCan be assigned to actors, tpl descriptors or individual objects.\nFor memory optimization purposes."
   }

   compound_skin_dynamic_color {
      toolTip = "[b]No info available.\nSomething about this: this object has animated color attributes and do something."
   }

   compound_skin_exclude {
      toolTip = "[b]Exclude[/b] this object from [b]skin compound."
   }

   compound_skin_group {
      toolTip = "This group of object [b]animated in the same way[/b], so [b]include it to skin compound."
      params = [
         {
            name  = group_name
            type  = string
            defVal   = "group_name"
         }
      ]
   }

   compound_instance_include {
      toolTip = "Include this object to [b]instance compound."
      params = [
         {
            name  = number_of_instances
            type  = int
            defVal   = "2"
         }
      ]
   }

   compound_skin_merge_skins {
      toolTip = "[b]No info available."
   }

   geom_sync_position {
      toolTip = "Synchronize object position with corresponding placeholder."
      params = [
         {
            name  = placeholder
            type  = string
            defVal   = "<object-name>"
         }
      ]
   }
}

decals {
   dir = true

   decal_dont_apply {
      toolTip = "[b]Do not apply decals[/b] to denoted object."
   }
   decal_threshold = {
		toolTip = "[b]Heightmap value threshold[/b] for decals blending."
		params	=	[
			{
				name = threshold
				type = float
				defVal = "0.8"
			}
		]
   }
   decal_softness = {
		toolTip = "[b]Decals blending softness[/b] value."
		params	=	[
			{
				name = softness
				type = float
				defVal = "0.4"
			}
		]
   }
   decal_invtest = {
		toolTip = "Decals blending [b]inverted[/b] test."
		params	=	[
			{
				name = invtest
				type = int
				defVal = "0"
			}
		]
   }
   decal {
      toolTip = "The denoted object is a decal, so fog and ssao will be applied based on the underlying geometry information."
   }
   mesh_decals_scattering_surface {
      toolTip = "The denoted mesh will be used for scattering mesh decal positions over its surface."
   }
}

outlines {
   outline_render_mask = {
      toolTip = "Setup outlines stencil mask"
   }
}

editor {
   dir = true
   
   editor_interaction_disable {
      toolTip = "Denoted mesh will not be selectable in editor, it also would be ignored while searching for snapping targets when performing in-editor transformations of other objects"
   }
}

export {
   dir = true

   export_char_mesh_no_combine {
		toolTip = "Denoted mesh won't be combined with other geometry on character project export"
		params	=	[
			{
				name   = object_name
				type  = string
				defVal   = "object_name"
			}	
		]
   }
   export_light_hints {
      toolTip = "Export hint positions for lights"
   }
   export_exclude {
      toolTip = "[b]Exclude[/b] denoted object [b]from export[/b] process."
   }
   export_exclude_actor {
      toolTip = "[b]Exclude[/b] denoted actor [b]from export list[/b]. It still will be processed by exporter).\nThis affix can be set automatically by exporter."
   }
   export_force_animobj {
      toolTip = "Applies to [b]not animated bones in skin template[/b], so that they can [b]accept animation[/b] from [b]external animation templates."
	  params = [
		 {
		    name   = is_rotation_only
            type    = bool
            defVal   = "0"
		 }
	  ]
   }
   export_no_anim_splines {
      toolTip = "Applies to [b]tplDescriptor[/b] of characters skin, to prevent skin templates to have own animation splines."
   }
   export_no_face_compression {
      toolTip = "[b]Disable face compression[/b] on denoted objects. Applies to Actors, TPL Descriptors and separate objects."
   }
   export_no_normal_compression {
      toolTip = "[b]Disable normal compression[/b] on denoted objects. Applies to Actors, TPL Descriptors and separate objects."
   }
   export_no_vertex_compression {
      toolTip = "[b]Disable vertex compression[/b] on denoted objects. Applies to Actors, TPL Descriptors and separate objects."
   }
   export_no_norm_in_vert4_compression {
      toolTip = "[b]Disable norm-in-vert4 compression[/b] on denoted objects. Applies to Actors, TPL Descriptors and separate objects."
   }
   export_no_texcoord_compression {
      toolTip = "[b]Disable texture coords compression[/b] on denoted objects. Applies to Actors, TPL Descriptors and separate objects."
   }
   export_ignore_nonuniform_scale {
      toolTip = "[b]Ignore non-uniform scale[/b] export warning on denoted object."
   }
   export_calculate_normals_and_tangents {
      toolTip = "[b]Normals and tangents[/b] will be [b]automatically calculated[/b] during export. Maya data will be ignored."
	}
   export_merge_group {
		is_hierarchical = true
		toolTip = "Objects will be [b]merged[/b] on export to single split only within the [b]same merge group.[/b]\nOptimization affix."
		params = [
		 {
		    name   = merge_group
            type    = int
            defVal   = "0"
		 }
	  ]
   }
   export_merge_hierarchy {
		toolTip = "Objects will be [b]merged[/b] with all its [b]childson[/b]."
   }
   export_merge_hierarchy_exclusive {
		toolTip = "Objects will be [b]merged separately[/b] from any 'export_merge_hierarchy'."
   }
   export_merge_hierarchy_ignore {
		toolTip = "Objects will be [b]removed[/b] from current merge hierarchy and optimized as 'splits without groups'."
   }
	export_lod_offset {
		toolTip = "Drop lods for [b]static reference[/b] (farplane objects optimization)."
		params = [
			{
				name 	= keep_last_lods
				type 	= int
				defVal	= "1"
			},
			{
				name 	= always_visible 
				type 	= bool
				defVal 	= "0"
			}
		]
	}
   export_preserve_position {
      toolTip = "[b]Preserve position[/b] of this object.\nApplies to special objects which names and positions must be preserved and geometry not merged during export process."
   }
   export_preserve_geometry {
       toolTip = "[b]Preserve geometry[/b] of this object.\nApplies to special objects which names and geometry must be preserved and geometry not merged during export process.\n[b]use_hierarchy_name[/b] to export fully qualified names for objects inside references"
		params = [
		 {
			name   = use_hierarchy_name
			type    = bool
			defVal   = "0"
		 }
		]
   }
   export_preserve_geometry_for_cpu {
       toolTip = "[b]Preserve geometry [/b] of this object for cpu in runtime.\n For optimization reasons geometry data is available only on gpu in runtime, but special cases require accessing it on cpu (runtime cdt generation, runtime navmesh generation, scene markup using geometry data)"
   }

   export_preserve_hierarchy {
      toolTip = "Prevent children from moving to a level of hierarchy higher that this during optimization."
   }
   export_lightprobes {
		toolTip = "Marker for lightprobe volumes that have tetrahedralization performed in Maya. This geometry is used for generating lightprobes locations and links on scene export."
			params = [
			{
				name   = volumeConstraint
				type    = float
				defVal   = "3.0"
			}
		]
   }
   export_tetrahedralization {
      toolTip = "Mesh will be used only for building Delaunay tetrahedralization. Mesh wouldn't be exported. Mesh must not have any intersections."
      params = [
         {
            name = distConstraint
            type  = float
            defVal   = "1"
         }
		]
   }
   export_tetrahedralization_hole {
      toolTip = "Mesh will be used only for building Delaunay tetrahedralization as a hole in other mesh. Mesh wouldn't be exported. Mesh must have any intersections. Mesh must be children mesh of mesh with export_tetrahedralization affix"
   }
   export_patch_tesselated_edges {
      toolTip = "Generates additional texture coordinates to prevent tesselation issues on edges."
   }
   export_blendshape_source {
      toolTip = "Generates morphing data for blend shape animated objects."
      params = [
         {
            name   = accuracy
            type    = float
            defVal   = "0.001"
         }
      ]
   }

   export_blendshape_shape {
      toolTip = "Using this mesh as shape for export_blendshape_source marked mesh"
   }

   export_wrinkles_animation {
      toolTip = "Exporting wrinkles animation from specific attributes (exAnim_xxx_NN, where xxx - arbitrary name, NN - number sequence 00, 01, .. 10, 11, etc)"
   }
   
   export_facegen_mesh_parameters {
      toolTip = "This mesh is transformed by FaceGen system"
      params = [
         {
            name    = controls
            type    = string
            defVal  = "<default>"
         }
         {
            name    = setname
            type    = string
            defVal  = "<set-name>"
         }
         {
            name 	= part
            type    = string
            defVal  = "<part-name>"
         }
         {
            name 	= precision
            type    = float
            defVal  = "0.00001"
         }
      ]
   }
   
   export_facegen_mesh {
      toolTip = "This mesh is transformed by FaceGen system"
      params = [
         {
            name    = controls
            type    = string
            defVal  = "<default>"
         }
         {
            name    = name
            type    = string
            defVal  = "<set-name>"
         }
         {
            name 	= part
            type    = string
            defVal  = "<part-name>"
         }
         {
            name 	= precision
            type    = float
            defVal  = "0.00001"
         }
      ]
		mapping = [
			{
				name = export_no_normal_compression
			}
			{
				name = export_no_vertex_compression
			}
			{
			  name = export_facegen_mesh_parameters
			  params {
				controls = "<controls>"
				setname = "<name>"
				part = "<part>"
				precision = "<precision>"
			  }
			}
		]
   }
	export_actor_type_not_rtc {
		toolTip = "Only for actors in references, actor will be nested, not rtc"
	}
    
    export_actor_to_static_lwi {
		toolTip = "Actor will be replaced with a static LWI instance"
	}
	
	export_exportbox {
		toolTip = "Define ExportBox object 'Geometry selection export' flag is 'Export boxes only' then only geometry touched by export boxes are exported"
	}
    
    export_triangles_cutout_volume {
		toolTip = "Defines enclosed geometry that cut-out all triangles of other renderable geometry, that are contained within its volume."
	}
    
    export_forbid_triangles_cutout {
		toolTip = "Defines objects that must ignore triangles cut-out procedure."
	}
   
   export_lmbox {
		toolTip = "If 'Lightmap' is 'Merge' and 'Lightmap->LmBox' defined than light will be recalculated for geometry touched by lm_boxes with name as in 'Lightmap->LmBox'"
      params = [
         {
            name    = name
            type    = string
            defVal  = "<default>"
         }
      ]
	}
	
	export_terrain_alternative_splits {
		toolTip = "Mark object as 'max-layered terrain', resulting in one-DIP rendering of the object using special 'terrain shader' (thus excluding from breaking apart into multiple splits by Mtl)"
	}
   
   export_skin_exclude {
   		toolTip = "Removing Skin Cluster from mesh on export. Adding locator and group. Usefully for 'Blend transform' node."
		params = [
         {
            name    = object_name
            type    = string
            defVal  = "object_name"
         }
      ]
   }
      
   export_include_only_top_lod {
		toolTip = "Set this affix on tpl_desc, only top LODs will be included in TPL (ex. FPS weapon)"
   }
   
   export_exclude_top_lod {
		toolTip = "Set this affix on tpl_desc, exclude top LODs from setup (ex. to ignore hi-res LOD weapon in third person setup)"
   }
   
   export_ignore_lod_exclusion {
		toolTip = "Set this affix on LOD-Root nodes, for ignore export_include_only_top_lod affix on tpl_desc"
   }
}

effects {
	dir = true
   
	snowfall_area {
		toolTip = "Mark geometry as an area of snow falling effect\n[b]Arguments:\n[b]* effectName[/b] - name of a snowfall effect to link this object to."
		params = [
			 {
					name   = effectName
					type    = string
					defVal  = "sfx_snowfall"
			 }
		]
	}
	
	snowfall_area_mesh {
			toolTip = "Alias for geometry that represents an area of snow falling effect\n[b]Arguments:\n[b]* effectName[/b] - name of a snowfall effect to link this object to."
			params = [
				{
					name   = effectName
					type    = string
					defVal  = "sfx_snowfall"
				}
			]
			mapping = [
				{
					name = snowfall_area
					params {
					   effectName = "<effectName>"
					}
				}
				{
					name = visibility_hidden
				}
				{
					name = export_preserve_geometry
				}
			]
		}
}

fog {
   dir = true

   fog_not_affected {
      toolTip = "[b]Not affected[/b] by [b]fog."
   }
   fog_affected {
      toolTip = "[b]Affected[/b] by [b]fog."
   }
   fog_global {
      toolTip = "Applies to an [b]actor of class \"entity\".\nProperty sheet of this actor will contain a [b]global fog settings[/b] for all scene."
   }
   fog_global_default {
      toolTip = "Applies to an [b]actor of class \"entity\".\nAdditional to [b]fog_global[/b] affix, marks fog params as default for the level."
   }
   fog2_portal {
      toolTip = "Portal that obscures its contents with the secondary fog"
   }
   behind_fog2_portal {
      toolTip = "Object is behind the secondary fog portals"
   }
}

hdr = {
   dir = true
   
   hdr_global {
      toolTip = "Applies to an [b]actor of class \"entity\".\nProperty sheet of this actor will contain a [b]global HDR settings[/b] for all scene."
   }
}

lighting {
   dir = true

   misc {
      dir = true

      lighting_dynamic_lighting_dont_recieve {
         toolTip = "Denoted geometry [b]will not recieve dynamic lighting."
      }

      lighting_object_from_lightprobe {
         toolTip = "Generate a [b]light set[/b] on [b]denoted geometry[/b]."
         params = [
            {
               name  = object_name
               type  = string
               defVal   = "object_name"
            }
            {
               name  = camera_offset
               type  = float
               defVal   = "0"
            }
            {
               name  = stationary
               type     = bool
               defVal   = "0"
            }
         ]
      }
      
      lighting_hierarchy_from_lightprobe {
         toolTip = "Generate a [b]light set[/b] on denoted geometry and sets it to [b]all its children[/b]."
      }
		
      lighting_light_shade_geometry {
         toolTip = "Applies to lights and defines a [b]shade geometry[/b] for denoted lights.\n[b]Arguments:\n[b]* object_name[/b] - shade object name for denoted light."
         params = [
            {
               name  = object_name
               type  = string
               defVal   = "object_name"
            }
         ]
      }


      lighting_no_screen_space_shadow {
         toolTip = "Denoted geometry will not cast [b]screen space shadow[/b]."
      }


		lighting_skydome_geometry {
			toolTip = "Alias for generic [b]skydome geometry[/b] that orginizes affixes for misc lighting and rendering features.\nArguments:\n * [b]sorting_group_number[/b] - group with index [b]0[/b] will be rendered [b]FIRST[/b], group with index [b]255[/b] will be rendered [b]LAST[/b]."
			params = [
				{
					name  = sorting_group_number
					type  = int
					defVal   = "0"
				}
			]
			mapping = [
				{
					name = sorting_transparent_group
					params {
					   group_number = "<sorting_group_number>"
					}
				}
				{
					name = lighting_dynamic_lighting_dont_recieve
				}
				{
					name = lighting_sm_dont_recieve_sm_lighting
				}
				{
					name = lighting_sm_dont_cast_shadow
				}
				{
					name = lighting_lm_invisible
				}
				{
					name = cdt_off_all
				}
				{
					name = visibility_visible_in_mirror
				}
                {
					name = editor_interaction_disable
				}
			]
		}
        lighting_invisible_blocker {
         toolTip = "Denoted geometry will act as invisible blocker for all lighting (dynamic, lightimapped direct and indirect)."
			mapping = [
                {
				name = lighting_lm_dont_apply
				}
				{
				name = lighting_lm_invisible_shadowcaster_affix
				}
				{
				name = lighting_sm_invisible_shadowcaster_affix
				}
				{
				name = cdt_off_all
				}
			]
		}
        lighting_invisible_doublesided_blocker {
         toolTip = "Denoted geometry will act as invisible double-sided blocker for all lighting (dynamic, lightimapped direct and indirect)."
			mapping = [
                {
				name = lighting_lm_dont_apply
				}
				{
				name = lighting_lm_invisible_shadowcaster_affix
				}
				{
				name = lighting_sm_invisible_shadowcaster_affix
				}
				{
				name = cdt_off_all
				}
                {
				name = shading_double_sided
				}
			]
		}
		lighting_disable_light_shafts {
			toolTip = "[b]Disables Light Shafts[/b] over selected objects."
		}
   }
   lightmap {
      dir = true

      lighting_lm_dont_apply {
         toolTip = "[b]Don't calculate and apply lightmap[/b] to denoted geometry."
      }
      lighting_lm_invisible_shadowcaster {
         toolTip = "Denoted geometry will be [b]hidden[/b] but will [b]cast lightmap shadow."
			mapping = [
				{
				name = lighting_lm_invisible_shadowcaster_affix
				}
				{
				name = lighting_lm_dont_apply
				}
				{
				name = cdt_off_all
				}
				{
				name = visibility_hidden
				}
			]
		}
      lighting_lm_invisible_emissivecaster {
         toolTip = "Denoted geometry will be [b]hidden[/b] but will [b]cast light from emissive texture."
			mapping = [
				{
				name = lighting_lm_invisible_emissivecaster_affix
				}
				{
				name = lighting_lm_visible
				}
				{
				name = lighting_lm_dont_apply
				}
				{
            name = lighting_lm_shadow_dont_cast
				}
				{
				name = cdt_off_all
				}
				{
				name = visibility_hidden
            params {
                  preserveMaterial = true
               }
				}
			]
		}
     lighting_lm_occlusion_dont_apply {
         toolTip = "[b]Don't calculate and apply occlusion [/b] to denoted geometry."
      }
      lighting_lm_occlusion_dont_cast {
         toolTip = "[b]Set denoted geometry invisible for AO [/b]"
      }
      lighting_lm_shadow_dont_cast {
         toolTip = "Denoted geometry [b]will not cast lightmap shadow and occlusion."
      }
      lighting_lm_shadow_dont_receive {
         toolTip = "Denoted geometry [b]will not receive lightmap shadow and occlusion."
      }
      lighting_lm_occlusion_cast {
         toolTip = "Denoted geometry [b]will cast lightmap occlusion (Occlusion is enable by default. Need if you want disable shadows but enable occlusion)."
      }
      lighting_lm_occlusion_receive {
         toolTip = "Denoted geometry [b]will receive lightmap occlusion (Occlusion is enable by default. Need if you want disable shadows but enable occlusion)."
      }
      lighting_lm_gi_dont_cast {
         toolTip = "Denoted geometry [b]will not cast lightmap GI."
      }
      lighting_lm_gi_dont_receive {
         toolTip = "Denoted geometry [b]will not recieve lightmap GI."
      }
      lighting_lm_invisible_for_gi {
         toolTip = "Denoted geometry [b]will be invisible for GI."
      }
      lighting_lm_dont_self_shadow {
         toolTip = "Denoted geometry [b]will not cast shadows on itself."
      }
      lighting_lm_shadow_cast {
         toolTip = "All [b]instances/references[/b] those template has this flag [b]will cast lightmap shadow.\nThis flag should be used only for static objects - trees, bushes, hydrants, etc."
      }
       lighting_lm_invisible {
         toolTip = "All [b]instances/references[/b] those template has this flag [b]will exclude from lightmap."
      }
      lighting_lm_to_texture {
         toolTip = "[b]Calculate lightmap[/b] and store this information in [b]lightmap texture[/b] of denoted geometry instead of vertex color (default).\n[b]Arguments:\n[b]* scale[/b] - lightmap patch scale. Default scale is 1. Increasing scale will result in lower quality (resolution) of lightmap on denoted geometry."
         params = [
            {
               name  = patch_scale
               type  = float
               defVal   = "1"
            }
         ]
      }
      lighting_lm_to_vertex_color {
         params = [
            {
               name     = sampling_type
               type     = enum
               values   = [
                          "Supersampling",
                          "Per Vertex"
               ]
               defVal   = "Supersampling"
               toolTip = "If set to true, light will be calculated for exactly point where vertices lays without any approximation."
            }
         ]
         toolTip = "[b]Calculate lightmap[/b] and store this information in [b]vertex color[/b] of denoted geometry instead of lightmap texture."
      }
      lighting_lm_emissive_intensity_factor {
         toolTip = "Emissive intensity factor for Beast."
         params = [
            {
               name  = intensity
               type  = float
               defVal   = "10"
            }
         ]
      }
      lighting_lm_texture_group {
         toolTip = "All geometry in hierarchy below that node will share the same lightmap texture."
      }
      lighting_lm_visible {
         toolTip = "Visible for lightmapper even if another affixes affect visibility."
      }
      lighting_lm_partbox {
         params = [
            {
               name  = name
               type  = string
               defVal   = ""
            }
         ]
         toolTip = "Lightmap for all geometry inside this box will baked separately from other scene. Every light will be used for such baking, not only those that are inside this box."
      }
      lighting_lm_invisible_in_secondary_pass {
         toolTip = "Denoted actor [b]will not be included[/b] in secondary pass of lightmaps baking (aletrnative lightmaps are used for certain versions of game levels - perticulary in Quake)"
      }
      lighting_lm_secondary_lightmap_bbox {
         toolTip = "Debug mark area in secondary pass of lightmaps baking (aletrnative lightmaps are used for certain versions of game levels - perticulary in Quake)"
      }
   }
   shadowmap {
      dir = true
      lighting_sm_cast_in_highres_segment_only {
         toolTip = "Denoted geometry will [b]cast shadowmap shadow[/b] only in [b]closest to camera segment."
      }
      lighting_sm_dont_cast_shadow {
         toolTip = "Denoted geometry [b]will not cast shadowmap shadow."
      }
      lighting_sm_dont_recieve_sm_lighting {
         toolTip = "Denoted geometry [b]will not recieve dynamic lighting[/b] from [b]shadowmap light."
      }
      lighting_sm_invisible_shadowcaster {
         toolTip = "Denoted geometry will be [b]hidden[/b] but will [b]cast shadowmap shadow."
			mapping = [
				{
				name = lighting_sm_invisible_shadowcaster_affix
				}
				{
				name = lighting_lm_shadow_dont_cast
				}
				{
				name = cdt_off_all
				}
			]
		}
   }
}

reflection {
   dir = true
   reflection_billboard_system {
      toolTip = "Mark polygonal planes to be used as billboards in reflection"
      params = [
         {
            name     = intensity
            type     = float
            defVal   = "5"
         }
      ]
   }
   reflection_cubemap_name {
      toolTip = "Name of cubemap, which will be used as reflection cubemap"
      params = [
         {
            name  = cubeName
            type  = string
            defVal = ""
         }
      ]
   }
   refraction_cubemap_name {
      toolTip = "Name of cubemap, which will be used as refraction cubemap"
      params = [
         {
            name  = cubeName
            type  = string
            defVal = ""
         }
      ]
   }
}

render {
   dir = true
   render_first_person_model {
      toolTip = "Applies to actors and tpl descriptors.\nTarget geometry will be [b]scaled along camera look direction vector[/b] in order to eliminate clipping with environment.\nIf the corresponding parameter is set to [b]true[/b], target geometry will get artificial [b]vertical FOV compensation[/b] in shader\n(in order to maintain correct proportions when overall camera FOV changes)."
      params = [
         {
            name    = fov_compensation
            type     = bool
            defVal   = "1"
         }
         {
            name     = disableSM
            type     = bool
            defVal   = "0"
         }
      ]
   }
   
   instanced_rendering {
      toolTip = "Denoted dynamic geometry (in actors/tpl) will be rendered using instancing. Optimization / performance improvement affix."
   }
   
   allow_extendable_skin_weights {
      toolTip = "Denoted dynamic geometry can have [b]8 weights[/b] per vertex."
   }
   
   render_has_alternative_skin {
      toolTip = "Denoted dynamic geometry has additional color set that contain data for [b]alternative skinning weights[/b]."
   }
   
   render_has_vertex_mask {
      toolTip = "Denoted dynamic geometry has additional color set that contain data for [b]per vertex binary mask[/b]."
   }
   
   render_preserve_uv_data {
      toolTip = "Denoted dynamic geometry will preserve [b]UV data[/b] for CPU processing."
   }
   
   material_customization_set {
      toolTip = "Name of customization set for this object"
      params = [
         {
            name  = name
            type  = string
            defVal = "default"
         }
      ]
   }
}

lod {
   dir = true

   lod_distance_anim {
      toolTip = "Applies to actors. [b]Animation[/b] of actor's children [b]will be turned off[/b] if [b]distance[/b] between camera and objects is [b]greater than argument[/b] of this affix.\n[b]Arguments:\n[b]* distance"
      params = [
         {
            name  = distance
            type  = float
            defVal   = "10"
         }
      ]
   }

   lod_distance_vis {
      toolTip = "Applies to actors. [b]Visibility[/b] of actor's children [b]will be turned off[/b] if [b]distance[/b] between camera and objects is [b]greater than argument[/b] of this affix.\n[b]Arguments:\n[b]* distance"
      params = [
         {
            name  = distance
            type  = float
            defVal   = "10"
         }
      ]
   }

   lod_system_exclude {
      toolTip = "Denoted object will be [b]excluded from LOD system."
   }
	
   lod_system_root {
      toolTip = "Denotes a [b]LOD system root[/b] - a most high resolution model. [b]Children[/b] of this object are [b]the rest LOD models[/b] ordered by number of vertices.\n[b]Arguments:\n[b]* is_shared[/b] - must be 1 if all LOD models was created by deleting vertices from high resolution model\n* hide_at_last_distance - #of distances must be equil to #of lods"
      params = [
         {
            name  = is_shared
            type  = bool
            defVal   = "0"
         }
         {
            name  = distances
            type  = string
            defVal   = ""
         }
         {
            name  = hide_at_last_distance
            type  = bool
            defVal   = "1"
         }
      ]

   }

   lod_tpl_use_per_object_lods {
   }
   
   lod_last_dissolve {
	toolTip = "Enable [b]LOD dissolve[/b] functionality. dist = -1.0 - disable dissolve"
	params = [
		{
			name = dissolve_start_dist
			type = float
			defVal = "-1"
		}
	]
   }
   
   lod_next_is_fake {
      toolTip = "[b]Next LOD[/b] object in system is a [b]fake lod."
   }
   

   static_lod_visibility_range {
      toolTip = "Denotes [b]visibility range for static LOD[/b].\n[b]Arguments:\n[b]* start[/b] - distance at which current LOD will become visible.\n[b]* end[/b] - distance at which current LOD will become hidden. (value [b]-1[/b] stands for infinity)"
      params = [
         {
            name  = start
            type  = float
            defVal   = "0"
         }
         {
            name  = end
            type  = float
            defVal   = "-1"
         }
      ]
   }
}

misc {
   dir = true

   camera {
      dir = true

      cam_transp_instance {
      }

      cam_add_transp_mtl {
         mapping = [
            {
               name = "vid.mtl_add"
               params {
                  global_mtl_name = "cam_transp"
               }         
            }
         ]
      }

      aliases_camera {
         dir = true

         camera_transparency_instance {
            toolTip = "Make instance transparent when camera collision with instance"
            is_hierarchical = true

            mapping = [
               {
                  name = cam_add_transp_mtl    
               }
               {
                  name = cam_transp_instance
               }
            ]
         }

         camera_transparency_low_res_geom {
            toolTip = "Turn off collision camera with low resolution geometry"

            mapping = [
               {
                  name = cam_transp_low_res_geom
               }
            ]
         }

         camera_transparency_high_res_geom {     
            toolTip = "Make high resolution geometry transparent when exist collision with camera"

            mapping = [
               {
                  name = cam_add_transp_mtl      
               }
               {
                  name = cam_transp_high_res_geom
               }
            ]
         }
      }
   }

   cinematic_start_frame {
      toolTip = "Applies to cameras. Mark a particular frame on denoted camera's animation timeline as a [b]start frame for cinematic.\n[b]Arguments:\n[b]* frame[/b] - cinematic start frame."
      params = [
         {
            name  = frame
            type  = int
            defVal   = "0"
         }
      ]
   }

   game_material {
      toolTip = "[b]Game material[/b] of denoted geometry.\n[b]Arguments:\n[b]* type[/b] - material name."
      params = [
         {
            name  = type
            type  = enum
            values = [
                        default
                        acid
                        acid_polyp
                        asphalt
                        asphalt_hot
                        armor
                        black_matter
                        blood_pool
                        brick
                        chaos_armor
                        chaos_energy
                        chaos_flesh
                        chaos_shield
                        ceramic
                        cloth
                        concrete
                        dirt
                        flesh
                        flesh_heavy
                        flesh_player
                        cosmic_flesh
                        energy_shield
                        energy_shield_clutch
                        garbage
                        glass
                        grass
                        gravel_big
                        gravel_small
                        ground
                        lava
                        leaves
                        marine_armor
                        marine_flesh
                        marine_shield
                        metal
                        metal_fences
                        metal_flesh
                        metal_heavy
                        metal_light
                        metal_medium
                        metal_plane
                        mud
                        mud_grass
                        mud_sand
                        oil
                        paper
                        plastic
                        rubber
                        sand
                        silver_slime
                        silver_slime_hard
                        slime_cocoon
                        slime_mud
                        snow
                        sparkling
                        stone
                        stone_dark
                        toxic
                        tyranid_armor
                        tyranid_flesh
                        tyranid_shield
                        water
                        water_deep
                        water_oil
                        wood
                        wood_boxes
                  ]
            defVal   = "default"
         }
      ]
   }   

   smart_object {
      toolTip = "Denotes a [b]smart object[/b] for NPC and Player.\n[b]Arguments:\n[b]* type[/b] - smart object type."
      params = [
         {
            name  = type
            type  = string
            defVal   = ""
         }
      ]
   }

   water_domain_geometry {
      toolTip = "water_domain_geometry"
   }
	
/*  domain shoud be placed by models, not by affixes
   domain {
      params = [
         {
            name  = type
            type    = enum
            values   = [
               action
               eax
               fog
               grass
               gravity
               ladder
               rain
               sound
               visibility
               spawn
               ai
            ]
            defVal   = "action"
         }
      ]
   }
*/
//   merge_exclude {
//   }

}

multiplayer {
	dir = true

	mp_team_0 {
	  toolTip = "Multiplayer Team 0"
	}
	mp_team_1 {
	  toolTip = "Multiplayer Team 1"
	}
	mp_team_2 {
	  toolTip = "Multiplayer Team 2"
	}
	mp_cam_0 {
	  toolTip = "Multiplayer Camera 0"
	}
	mp_cam_1 {
	  toolTip = "Multiplayer Camera 1"
	}
	mp_cam_2 {
	  toolTip = "Multiplayer Camera 2"
	}
	mp_tdm {
	   toolTip = "Multiplayer tdm"	
	}
	mp_dm {
	   toolTip = "Multiplayer dm"	
	}
	mp_observer_cam {
	   toolTip = "Observer camera"	
	}
	mp_set_cam_1 {
	   toolTip = "Camera Set 1"	
	}
	mp_set_cam_2 {
	   toolTip = "Camera Set 1"	
	}
	mp_set_cam_3 {
	   toolTip = "Camera Set 1"	
	}
	mp_team1_attack{
	   toolTip = "Multiplayer Team 1 Attack"	
	}
	mp_team2_attack{
	   toolTip = "Multiplayer Team 2 Attack"	
	}
	mp_team1_defence{
	   toolTip = "Multiplayer Team 1 Defence"	
	}
	mp_team2_defence{
	   toolTip = "Multiplayer Team 2 Defence"	
	}
	mp_stage_1{
	   toolTip = "Multiplayer Stage 1"
	}
	mp_stage_2{
	   toolTip = "Multiplayer Stage 2"
	}
	mp_stage_3{
	   toolTip = "Multiplayer Stage 3"
	}
	mp_stage_4{
	   toolTip = "Multiplayer Stage 4"
	}
	mp_stage_5{
	   toolTip = "Multiplayer Stage 5"
	}
	mp_stage_6{
	   toolTip = "Multiplayer Stage 6"
	}
	mp_stage_7{
	   toolTip = "Multiplayer Stage 7"
	}
	mp_stage_8{
	   toolTip = "Multiplayer Stage 8"
	}
	mp_stage_9{
	   toolTip = "Multiplayer Stage 9"
	}
	mp_stage_10{
	   toolTip = "Multiplayer Stage 10"
	}
}

physics {
   dir = true

	physics_destr_hit_counter {
      toolTip = "Sets [b]hits number[/b] needed to [b]destroy[/b] denoted destruction object [b]piece.\n[b]Arguments:\n[b]* min\n[b]* max"
      params = [
         {
            name  = min
            type    = float
            defVal   = "1"
         }
         {
            name  = max
            type    = float
            defVal   = "2"
         }
		]
	}

   physics_disable_break {
      toolTip = "Denoted destroyable object [b]will never break apart."
   }

   physics_event_cdt {
      toolTip = "Physics system will [b]call SSL event[/b] with this number when denoted object will [b]collide with something.\n[b]Arguments:\n[b]* number[/b] - event number."
      params = [
         {
            name  = number
            type    = int
            defVal   = "0"
         }
      ]
   }

	physics_filter_explosion {
      toolTip = "Denoted objects will be [b]filtered[/b] when [b]big explosion[/b] will occur for optimization puposes"
	}
	
	physics_filter_never_skip {
      toolTip = "Denoted objects will be [b]never skipped[/b] from physics calculation when [b]big explosion[/b] will occur."
	}
	
	physics_quality_debris {
      toolTip = "Applies a [b]'debris' physics calculation quality[/b] preset on denoted objects for optimization purposes."
	}

   physics_shape_new_root {
      toolTip = "Denoted object and all it's children will form a [b]new, separate from parent collision shape.\nSpecial affix for optimization purposes. Must be used carefully.\nContact Pavel Kuznetsov for additional info."
   }

   physics_property_obj_type {
      toolTip = "Denoted object may be used by compound property. Some objects can be provided with additional properties. Arguments:\n[b]* type[/b] - property name, that is described in 'cp_descs' section of compound property"
      params = [
			{
				name  = type
				type    = enum
				values   = [
					"none"
					"hut_piece"
					"pot_piece"
					"cardboard_piece"
					"glass_piece"
					"concrete_piece"
					"flowerbed_piece"
					"wood_piece"
					"wood_block"
					"car_wheel"
					"barricade"
					"untouchable"
					"unbreakable"
				]
				defVal   = "none"
			}
		]
	}
}

rma {
   dir = true
   useFullNames = true

   divider {
      toolTip = "Denotes a [b]divider for RMA."
   }
}

scorch {
   dir = true

   end_effect_type {
      toolTip = "[b]End effect type[/b] of denoted geometry.\n[b]Arguments:\n[b]* type[/b] - type of end effect."
      params = [
         {
            name  = type
            type    = enum
            values   = [
                default
                acid
                acid_polyp
                asphalt
                asphalt_hot
                armor
                black_matter
                blood_pool
                brick
                chaos_armor
                chaos_energy
                chaos_flesh
                chaos_shield
                ceramic
                cloth
                concrete
                dirt
                flesh
                flesh_heavy
                flesh_player
                cosmic_flesh
                energy_shield
                energy_shield_clutch
                garbage
                glass
                grass
                gravel_big
                gravel_small
                ground
                lava
                leaves
                marine_armor
                marine_flesh
                marine_shield
                metal
                metal_fences
                metal_flesh
                metal_heavy
                metal_light
                metal_medium
                metal_plane
                mud
                mud_grass
                mud_sand
                oil
                paper
                plastic
                rubber
                sand
                silver_slime
                silver_slime_hard
                slime_cocoon
                slime_mud
                snow
                sparkling
                stone
                stone_dark
                toxic
                tyranid_armor
                tyranid_flesh
                tyranid_shield
                water
                water_deep
                water_oil
                wood
                wood_boxes
            ]
            defVal   = "default"
         }
      ]
   }

   scorch_force_apply {
      toolTip = "[b]ALways put[/b] any [b]scrochmarks[/b] on denoted geometry regardless of other affixes and material type."
   }
}

shading {
   dir = true

   shading_constant_color {
      toolTip = "Shade denoted geometry with [b]constant color.\n[b]Argumets:\n[b]* color[/b] - constant color to shade. Default value - 255 255 255 255"
      params = [
         {
            name  = color
            type  = color
            defVal   = "255 255 255 255"
         }
      ]
   }

   shading_disable_transp_occlusion_query {
      toolTip = "Denoted transparent geometry wont be z-tested for occlusion.\nSometimes it can help with render small transparent objects."
   }

   shading_discontinuity {
      toolTip = "Defines a [b]discontinuity angle[/b] of denoted geometry.\nIf argument is 0 - all edges are hard.\nIf argument is 180 - all edges are smooth.\n[b]Arguments:\n[b]* angle[/b] - discontinuity angle. Default value - 45."
      params = [
         {
            name  = angle
            type  = float
            defVal   = "45"
         }
      ]
   }

   shading_discontinuity_hard_texture_border {
      toolTip = "All [b]texture borders[/b] of denoted geometry are [b]always hard."
   }

   shading_distortion_only {
      toolTip = "Denoted geometry will be rendered [b]only in distortion pass[/b].\nObject will reveal itself only by it's distortion."
   }

   shading_double_sided {
      toolTip = "Denoted geometry will be [b]double sided.[/b]"
		params = [
			{
				name = preserveNormals
            type  = bool
            defVal   = "0"
			}
		]
   }

   shading_override_cubemap {
      toolTip = "[b]Overrides cubemap[/b] on denoted geometry materials.\n'reflection.source' shader property (in PictView) must be set to automatic.\nHierarchical affix."
		is_hierarchical = true

		params = [
			{
				name = cubemapName
            type  = string
            defVal   = ""
			}
		]
		
      mapping = [
				{
					name = "vid.sdr.glt.tex.refCubeAuto"
					params {
						refCubeAuto = "<cubemapName>"
					}
				}
			]
   }

   shading_zpass_only {
      toolTip = "Denoted geometry will be rendered [b]only in z pass[/b].\nMake sure there is another object at the same positions, or there would be red blobs."
   }

   shading_static_shader_data {
      toolTip = "Denoted that shading data will not be changed at runtime and can be cached."
   }
	
   shading_layer_transition_index {
      toolTip = "The number [0..2] of the non-base layer that can overlap the layers with a smaller number. Use together with .ssl .prop layer_transition"
      params = [
         {
            name  = layer_index
            type  = int
            defVal   = "1"
         }
      ]
   }

}

sorting {
   dir = true

   sorting_transparent_distance_offset {
      toolTip = "Transparent objects are sorted by the distance from the camera + the specified offset.\nThe objects with the largest distance are drawn first.\nThe offset can be zero, positive or negative.\nShould be used only for distant objects"
      params = [
         {
            name  = offset
            type  = float
            defVal   = "0.0"
         }
      ]
   }
   
   sorting_transparent_group {
      toolTip = "Put denoted [b]semitransparent objects[/b] in particular [b]sorting group.\n[b]It is better to use values in 64 - 254 range[/b].\nGroup with index [b]0[/b] will be rendered [b]FIRST.\nGroup with index [b]255[/b] will be rendered [b]LAST.\n[b]Arguments:\n[b]* group_number"
      params = [
         {
            name  = group_number
            type  = int
            defVal   = "128"
         }
      ]
   }
   sorting_transparent_group_first {
      toolTip = "Put denoted [b]semitransparent objects[/b] in [b]sorting group[/b] with index [b]32."
      mapping = [
         {
         name = sorting_transparent_group
            params {
               group_number = "32"
            }
         }
      ]
   }

   sorting_transparent_group_last {
      toolTip = "Put denoted [b]semitransparent objects[/b] in [b]sorting group[/b] with index [b]224."
      mapping = [
         {
         name = sorting_transparent_group
            params {
               group_number = "224"
            }
         }
      ]
   }

   sorting_transparent_perface {
      toolTip = "Triangles of denoted object will be sorted by distance to camera"
   }

   sorting_z_bias {
      toolTip = "Add a [b]z-bias[/b] to denoted geometry to [b]prevent z-fighting.\n[b]Arguments:\n[b]* value[/b] - integer number from 1 to 8. Bigger number corresponds to more z-bias value."
      params = [
         {
            name  = value
            type  = int
            defVal   = "1"
         }
      ]
   }

}

tessellation {
   dir = true
	
	tessellation_mesh {
		toolTip = "Denotes a mesh with [b]tesselation.\n[b]Arguments:\n[b]* displace[/b] - enable displacement\n[b]* smooth[/b] - enable smoothing\n[b]* scale[/b] - tessellation scale\n[b]* maxLevel[/b] - maximum tessellation level\n[b]* maxDist[/b] - maximum tessellation distance (meters)\n[b]* triangleSize[/b] - tessellated triangle size (pixels)\n[b]* transitionFactor[/b] - adaptive tessellation factor\n[b]* angleFactor[/b] - angle dependence tessellation factor"

		params = [
         {
            name = displace
            type = enum
            values   = [
					"none"
					"heightmap"
            ]
            defVal = "none"
         }
         {
            name = smooth
            type = enum
            values   = [
					"none"
					"pn_triangles"
					"phong"
            ]
            defVal = "none"
         }
		{
            name = scale
            type  = float
            defVal   = "1.0"
         }
		{
            name = maxLevel
            type = enum
            values   = [
					"16"
					"32"
					"64"
					"128"
            ]
            defVal = "16"
         }
		 {
            name = maxDist
            type  = float
            defVal   = "50.0"
         }
		 {
            name = triangleSize
            type  = float
            defVal   = "20.0"
         }
		 {
            name = transitionFactor
            type  = float
            defVal   = "0.5"
         }
		 {
            name = angleFactor
            type  = float
            defVal   = "1.0"
         }
      ]

      mapping = [
			{
				name = export_no_normal_compression
			}
			{
				name = tessellation_parameters
				params {
					displace = "<displace>"
					smooth = "<smooth>"
					scale = "<scale>"
					maxLevel = "<maxLevel>"
					maxDist = "<maxDist>"
					triangleSize = "<triangleSize>"
					transitionFactor = "<transitionFactor>"
					angleFactor = "<angleFactor>"
				}
			}
		]
		
	
	}
	
   tessellation_parameters {
		toolTip = "Use this affix [b]only for testing purposes.[/b] Apply [b]tessellation_mesh[/b] in usual cases.\n[b]Tessellation[/b] parameters.\n[b]Arguments:\n[b]* displace[/b] - enable displacement\n[b]* smooth[/b] - enable smoothing\n[b]* scale[/b] - tessellation scale\n[b]* maxLevel[/b] - maximum tessellation level\n[b]* maxDist[/b] - maximum tessellation distance (meters)\n[b]* triangleSize[/b] - tessellated triangle size (pixels)\n[b]* transitionFactor[/b] - adaptive tessellation factor\n[b]* angleFactor[/b] - angle dependence tessellation factor"
		params = [
         {
            name = displace
            type = enum
            values   = [
					"none"
					"heightmap"
            ]
            defVal = "none"
         }
         {
            name = smooth
            type = enum
            values   = [
					"none"
					"pn_triangles"
					"phong"
            ]
            defVal = "none"
         }
		{
            name = scale
            type  = float
            defVal   = "1.0"
         }
		{
            name = maxLevel
            type = enum
            values   = [
					"16"
					"32"
					"64"
					"128"
            ]
            defVal = "16"
         }
		 {
            name = maxDist
            type  = float
            defVal   = "1000.0"
         }
		 {
            name = triangleSize
            type  = float
            defVal   = "20.0"
         }
		 {
            name = transitionFactor
            type  = float
            defVal   = "0.5"
         }
		 {
            name = angleFactor
            type  = float
            defVal   = "1.0"
         }
      ]
   }
   
}

tpl {
   dir = true
   tpl_load_on_demand {
     toolTip = "Tpl must be loaded in ssl explicitly"
   }
}

water {
   dir = true
   
   water_coast { 
      toolTip = "[b]Need to form shore waves[/b]"
   }
   water_caustic {
      toolTip = "water_caustic"
   }
   caustics_geometry {
      toolTip = "Mark mesh to be used as caustics"      
   }
}

visibility {
   dir = true

   visibility_hidden {
      toolTip = "At export time remove all vertex data for the object except geometry. If [b]preserveMaterial[/b] = 'true' then the Material info is not removed (used for geometry particles)"
		params = [
		 {
            name = preserveMaterial
            type  = bool
            defVal   = "0"
         }
      ]
	  
   }
   visibility_exclude{
      toolTip = "Denoted geometry will [b]ignore visibility[/b] system and will be [b]always visible."
   }
   
   visibility_hidden_in_reflection_cubemap {
      toolTip = "Hide an object from rendering to cubemap"
   }
   
   visibility_visible_in_reflection_cubemap_only {
      toolTip = "This object is visible in reflection cubemap only"
   }
   
   visibility_invisible_in_mirror {
      toolTip = "[b]Do not render[/b] denoted geometry [b]in mirrors."
      params = [
         {
            name  = is_hierarchical
            type  = bool
            defVal   = "1"
         }
      ]
   }
   visibility_visible_in_mirror {
      toolTip = "[b]Render[/b] denoted geometry [b]in mirrors.[/b]"
      params = [
         {
            name  = is_hierarchical
            type  = bool
            defVal   = "1"
         }
      ]
   }
   visibility_occluder {
      toolTip = "Visibility occluder. This object will be used as [b]shadow caster[/b] in visibility occlusion tests.\n[b]Arguments:\n[b]* activation_distance[/b] - distance between camera and occluder at which occluder gets activated (0 means occluder is always active)"
      params = [
         {
            name  = activation_distance
            type  = float
            defVal   = "0"
         }
      ]
   }
   visibility_per_object {
      toolTip = "Denoted instance will be represented in visibility system as separate objects."
   }
   visibility_pixel_cull_disable {
      toolTip = "Denoted instance will not be culled by pixel size."
   }
   visibility_occluder_mesh {
      toolTip = "Alias for setting up visibility occluder on mesh.\n[b]Arguments:\n[b]* activation_distance[/b] - distance between camera and occluder at which occluder gets activated (0 means occluder is always active)"
      params = [
         {
            name  = activation_distance
            type  = float
            defVal   = "0"
         }
      ]
      mapping = [
         {
            name = visibility_occluder
            params {
               activation_distance = "<activation_distance>"
            }
         }
		   {
		      name = visibility_hidden
         }
         {
            name = cdt_off_all
         }
      ]
   }
}

vid {
   dir = true
   useFullNames = true

   mtl_strict {
      toolTip = "Use material with given name for this geometry.\nMaterial must exist for assigned texture.\nAll shader-overloading affixes will be ignored for this material."
      params = [
         {
            name  = material_name
            type  = string
            defVal   = "default"
         }
      ]
   }

   mtl {
		is_hierarchical = true
      toolTip = "Use material with given name for this geometry.\nMaterial must exist for assigned texture. [b]Hierarchical affix."
      params = [
         {
            name  = material_name
            type  = string
            defVal   = "default"
         }
      ]
   }

   mtl_add {
     toolTip = "Add all specified (comma-separated) global materials to all textures assigned to this geometry."
     params = [
        {
           name = global_mtl_name
           type = string
           defVal = ""
        }
     ]
   }   

   sdr_add {
      toolTip = "Add all specified shaders to all materials of all textures assigned to this geometry.\nSyntax: [sdr_name:preset_name], i.e. 'sfx:default, item_highlight:red'"
      params = [
         {
            name = sdr_and_preset_name
            type = string
            defVal = ""
         }
      ]
   }

   preload {
      is_hierarchical = true
      toolTip = "Preload another material (to switch later in game).\nMaterial must exist for assigned texture. [b]Hierarchical affix."
      params = [
         {
            name  = material_name
            type  = string
            defVal   = "default"
         }
      ]
   }
}

wpn {
   dir = true
   
   wpn_optical_sight {
      toolTip = "Optical sight parameters"
      params = [
         {
            name = type
            type = enum
            values = [
               "telescopic"
            ]
            defVal   = "telescopic"
         }
      ]
   }
}

// alias template decsription. do not delete
//
//test_alias {
// params = [
//    {
//    name  = number_of_rows
//    type  = int
//    defVal = "8"
//    }
// ]
// mapping = [
//    {
//    name = anim_frame_base_texture
//    params {
//       number_of_rows = "<number_of_rows>"
//       number_of_columns = "6"
//       cycle_time = "1"
//       start_time = "0"
//       number_of_frames = "-1"
//       frame_x_size = "-1"
//       frame_y_size = "-1"
//       }
//    }
//    {
//    name = ai_constraint_motion
//    }
//    {
//    name = "vid.mtl"
//       params {
//          material_name = "default"
//          }
//    }
// ]
//}
