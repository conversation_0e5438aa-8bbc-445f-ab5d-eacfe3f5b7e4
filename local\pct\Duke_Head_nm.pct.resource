__type: res_desc_pct
downsampled: true
header:
  faceSize: 5592400
  format: 36
  mipLevel:
  - offset: 0
    size: 4194304
  - offset: 4194304
    size: 1048576
  - offset: 5242880
    size: 262144
  - offset: 5505024
    size: 65536
  - offset: 5570560
    size: 16384
  - offset: 5586944
    size: 4096
  - offset: 5591040
    size: 1024
  - offset: 5592064
    size: 256
  - offset: 5592320
    size: 64
  - offset: 5592384
    size: 16
  nFaces: 1
  nMipMap: 10
  sign: 1346978644
  size: 5592400
  sx: 2048
  sy: 2048
  sz: 1
imageOffset: 0
isCopyDst: false
isImposter: false
isLightmap: false
linkTd: res://td/<PERSON>_Head_nm.td.resource
mipMaps:
- <PERSON>_Head_nm_1.pct_mip
- <PERSON>_Head_nm_2.pct_mip
- <PERSON>_Head_nm_3.pct_mip
- <PERSON>_Head_nm_4.pct_mip
- <PERSON>_Head_nm_5.pct_mip
- <PERSON>_Head_nm_6.pct_mip
- <PERSON>_Head_nm_7.pct_mip
- <PERSON>_Head_nm_8.pct_mip
- <PERSON>_Head_nm_9.pct_mip
- <PERSON>_Head_nm_10.pct_mip
pct: ''
predownsampleMipsCount: 11
source: <source>\characters\Duke_Head_nm.tga
texName: Duke_Head_nm
texType: nm
tileLayout: 0
tiles: []
useHeaderFromResource: true
