lodInfo = {
   maxLodDist = [

   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      bullet01_baked05 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      bullet02 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      bullet_hot = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      bullet_hotwave = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail_bullet_hotlines = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning02 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning03 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning04 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning05 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      boostgate_glow01 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      boostgate_lightning1 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      speedtrail = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      glow_rele_r = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      glow_rele_l = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      boostgate_glow02 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      boostgate_lightning2 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      speedtrail1 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning06 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning07 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning08 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      lightning09 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      glow_rele_l1 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      glow_rele_r1 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      road_glow_overlocking = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail01 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail02 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail03 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail04 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail05 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail06 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail07 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail08 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail09 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail10 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail11 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail12 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail13 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail14 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail15 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail16 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail17 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail18 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail19 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail20 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail21 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail22 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail23 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail24 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail25 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail26 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail27 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail28 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail29 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail30 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail31 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail32 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail33 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail34 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail35 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail36 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail37 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail38 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail39 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail40 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail41 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail42 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail43 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail44 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail45 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail46 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail47 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail48 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail49 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail50 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail51 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail52 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail53 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail54 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail55 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail56 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail57 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail58 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail59 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail60 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail61 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail62 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail63 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail64 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail65 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail66 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail67 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail68 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail69 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail70 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail71 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rail72 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      line_distort_hard = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      line_distort2_low = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      line_distort_hard2 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      line_distort_low2 = {
         collision = {
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
