// Space Marine 2 - Flamethrower Projectile System
// This file defines how the flame projectiles behave

flame_stream_projectile = {
    // Basic projectile properties
    projectileUid = "flame_stream"
    projectileName = "Flame Stream"
    projectileType = "continuous_beam"
    
    // Physics properties
    physics = {
        speed = 25.0              // 25 m/s projectile speed
        mass = 0.1               // Very light projectile
        gravity = 0.5            // Slight gravity effect for realism
        airResistance = 0.8      // High air resistance (flames dissipate)
        lifetime = 0.8           // Projectile exists for 0.8 seconds
        bounces = 0              // Flames don't bounce
    }
    
    // Damage properties
    damage = {
        baseDamage = 45.0        // Base damage per hit
        damageType = "fire"      // Fire damage type
        damageRadius = 3.0       // 3 meter damage radius
        falloffType = "linear"   // Linear damage falloff
        penetration = 0          // Cannot penetrate armor/walls
        
        // Damage over time
        dotEffect = {
            enabled = true
            tickRate = 0.2       // Damage every 0.2 seconds
            tickDamage = 8.0     // 8 damage per tick
            duration = 0.8       // Lasts for projectile lifetime
        }
    }
    
    // Area of effect behavior
    areaOfEffect = {
        enabled = true
        radius = 3.0             // 3 meter AoE radius
        shape = "cone"           // Cone-shaped AoE (like real flamethrower)
        coneAngle = 45.0         // 45 degree cone angle
        maxTargets = 8           // Can hit up to 8 enemies
        friendlyFire = false     // Cannot damage allies
        
        // AoE damage scaling
        centerDamageMultiplier = 1.0    // Full damage at center
        edgeDamageMultiplier = 0.3      // 30% damage at edge
    }
    
    // Status effect application
    statusEffects = {
        burning = {
            applyChance = 0.9    // 90% chance to apply burning
            duration = 5.0       // 5 second burn duration
            damagePerSecond = 10.0 // 10 DPS while burning
            stackable = true     // Can stack multiple burns
            maxStacks = 3        // Maximum 3 stacks
            
            // Visual effects for burning
            burnEffect = "sfx_enemy_burning"
            burnSound = "sfx_burning_loop"
        }
        
        // Fear effect on some enemies
        fear = {
            applyChance = 0.3    // 30% chance to cause fear
            duration = 2.0       // 2 second fear duration
            affectedEnemyTypes = ["tyranid_basic", "cultist"] // Only affects basic enemies
        }
    }
    
    // Environmental interactions
    environmentalEffects = {
        igniteObjects = {
            enabled = true
            igniteChance = 0.8   // 80% chance to ignite flammable objects
            burnDuration = 10.0  // Objects burn for 10 seconds
            spreadRadius = 2.0   // Fire can spread 2 meters
        }
        
        clearVegetation = {
            enabled = true
            clearRadius = 2.0    // Clears vegetation in 2m radius
            regrowthTime = 30.0  // Vegetation regrows after 30 seconds
        }
        
        meltIce = {
            enabled = true
            meltRadius = 1.5     // Melts ice in 1.5m radius
            refreezeTime = 60.0  // Ice reforms after 60 seconds
        }
    }
    
    // Visual effects
    visualEffects = {
        // Main flame stream
        flameStream = {
            effect = "sfx_flamethrower_stream"
            color = [255, 120, 0]     // Orange flame color
            intensity = 2.5           // Bright flames
            width = 1.2              // 1.2 meter wide stream
            length = 15.0            // 15 meter long stream
            
            // Flame animation
            flickerRate = 0.1        // Flicker every 0.1 seconds
            flickerIntensity = 0.3   // 30% intensity variation
        }
        
        // Impact effects
        impactEffect = {
            effect = "sfx_flame_impact"
            duration = 1.0           // Impact effect lasts 1 second
            radius = 2.0             // 2 meter impact radius
            sparks = true            // Generate sparks on impact
            smoke = true             // Generate smoke on impact
        }
        
        // Scorch marks
        scorchMark = {
            texture = "scr_flamethrower"
            size = 2.5               // 2.5 meter scorch mark
            duration = 60.0          // Lasts 60 seconds
            fadeTime = 10.0          // Fades over 10 seconds
        }
        
        // Particle effects
        particles = {
            emberCount = 50          // 50 ember particles
            smokeCount = 30          // 30 smoke particles
            sparkCount = 20          // 20 spark particles
            
            // Particle behavior
            emberLifetime = 2.0      // Embers last 2 seconds
            smokeLifetime = 5.0      // Smoke lasts 5 seconds
            sparkLifetime = 0.5      // Sparks last 0.5 seconds
        }
    }
    
    // Audio effects
    audioEffects = {
        flameSound = {
            sound = "wpn_flamethrower_flame"
            volume = 0.8             // 80% volume
            pitch = 1.0              // Normal pitch
            loop = true              // Loop while firing
            fadeIn = 0.2             // 0.2 second fade in
            fadeOut = 0.5            // 0.5 second fade out
        }
        
        impactSound = {
            sound = "wpn_flame_impact"
            volume = 0.6             // 60% volume
            randomPitch = 0.2        // ±20% pitch variation
            maxInstances = 3         // Maximum 3 simultaneous impacts
        }
    }
    
    // Collision behavior
    collision = {
        collideWithTerrain = true    // Collides with ground/walls
        collideWithEnemies = true    // Collides with enemies
        collideWithObjects = true    // Collides with destructible objects
        
        // What happens on collision
        onTerrainHit = "spread"      // Flame spreads on terrain
        onEnemyHit = "damage"        // Damages enemies
        onObjectHit = "ignite"       // Ignites objects
        
        // Collision effects
        terrainSpread = {
            radius = 2.0             // Spreads 2 meters on terrain
            duration = 3.0           // Terrain fire lasts 3 seconds
            damage = 15.0            // 15 DPS for terrain fire
        }
    }
    
    __type = "ProjectileDescription"
}
