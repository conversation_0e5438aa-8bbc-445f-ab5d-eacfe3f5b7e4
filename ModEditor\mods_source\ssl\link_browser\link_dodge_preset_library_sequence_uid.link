__request   =   {
   __select   =   {
      __id   =   "preset"
      __select   =   {
         __id   =   "sequence"
         __path   =   "sequenceRulesDescs/*"
         __type   =   "model"
      }
      __path   =   "dodgePresets/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "DodgePresetsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{sequence}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

