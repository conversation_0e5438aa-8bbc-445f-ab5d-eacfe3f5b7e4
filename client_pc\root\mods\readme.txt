
You can place mod paks in this directory. Additionally, you can create a `pak_config.yaml` file here to configure which
mod paks are enabled and specify their loading order. The configuration file should contain an array of objects, each 
object must have a 'pak' key specifying the pak file name and optionally a 'disabled' boolean key. For example:
- pak: mod_a.pak
- pak: mod_b.pak
  disabled: true

All `.pak` files in the mods directory that are not listed in the configuration file are loaded first (in alphabetical order).
After that, paks enabled in the configuration file are loaded, following the order in which they are listed in the config.
