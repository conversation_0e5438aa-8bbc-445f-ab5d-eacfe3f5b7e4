excludeFromSkinAffixes = [
   "cdt_cdtbox_static",
   "cdt_cdtbox_anim",
   "cdt_special_mesh",
   "domain",
   "visibility_hidden",
   "anim_object_billboard"// 20.05.2011, PS3: can't use skin without EDGE, but EDGE doesn't work with billboard.
]

excludeFromSkinAffixesHier = [
   "lod_system_root"
]

useAffixes = [
	compound_skin_group,
	export_no_normal_compression,
	fog_not_affected,
	lighting_dynamic_lighting_dont_recieve,
	lighting_generate_light_set_on_object,
	lighting_generate_light_set_on_hierarchy,
	lighting_sm_dont_cast_shadow,
	lighting_sm_dont_recieve_sm_lighting,
	lighting_sm_invisible_shadowcaster,
	lighting_object_from_lightprobe,
	lighting_no_screen_space_shadow,
	shading_constant_color,
	shading_distortion_only,
	shading_zpass_only,
	shading_double_sided,
	sorting_transparent_group,
	sorting_transparent_perface,
	sorting_z_bias,
	visibility_hidden,
	visibility_ignore_pvs_system,
	visibility_invisible_in_mirror,
	anim_object_billboard,
	instanced_rendering,
	material_customization_set,
	"vid.sdr*",
	"vid.mtl*",
]