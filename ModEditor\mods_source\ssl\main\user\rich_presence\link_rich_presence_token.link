__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __format   =   "{name}"
            __showValue   =   False
            __path   =   "tokens/*"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __format   =   "{value}"
         __path   =   "richPresenceSettings"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __format   =   "{value}"
      __showValue   =   False
      __path   =   "userManagerSettings"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "UserSystems"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"

