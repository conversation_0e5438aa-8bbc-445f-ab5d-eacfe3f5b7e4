steps:
  - RES3_REPLICA_SSL
  - RES3_REPLICA_DESC

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_REPLICA_SSL

rules:
  - name: process initial replica list
    res_type: res_desc_replicas
    on:
      - mask: 'res://initial/*'
    step: RES3_REPLICA_SSL

  - name: process initial replica list
    res_type: res_desc_replicas_all
    step: RES3_REPLICA_SSL

  - name: process replica ssl
    res_type: res_desc_ssl
    step: RES3_REPLICA_SSL

  - name: process initial ssolib
    res_type: res_desc_ssolib
    step: RES3_REPLICA_DESC

  - name: process initial sso
    res_type: res_desc_sso
    step: RES3_REPLICA_DESC

  - name: process initial cls
    res_type: res_desc_cls
    step: RES3_REPLICA_DESC

  - name: process initial prop
    res_type: res_desc_prop
    step: RES3_REPLICA_DESC

  - name: process initial prefab
    res_type: res_prefab_desc
    step: RES3_REPLICA_DESC

  - name: process initial basic descs
    res_type: res_basic_ssl_desc
    step: RES3_REPLICA_DESC
