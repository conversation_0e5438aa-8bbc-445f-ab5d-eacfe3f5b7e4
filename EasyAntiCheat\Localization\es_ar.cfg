#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

windows_fonts:
{
	# Use the embedded font.
};
bugreport:
{
	btn_continue = "Conéctate a Internet para buscar una solución y cierra el juego.";
	btn_exit = "Cerrar el juego.";
	btn_hidedetails = "Ocultar detalles";
	btn_showdetails = "Mostrar detalles";
	chk_sendreport = "Enviar informe de errores";
	error_code = "Código de error:";
	lbl_body1 = "Lo sentimos, se ha producido un problema al iniciar tu juego";
	lbl_body2 = "Ayúdanos enviándonos un informe del problema.";
	lbl_body3 = "Easy Anti-Cheat puede buscar una solución al problema en Internet para intentar resolverlo.";
	lbl_header = "No se puede iniciar el juego";
	title = "Error al iniciar";
};
game_error:
{
	error_catalogue_corrupted = "Catálogo hash de Easy Anti-Cheat dañado";
	error_catalogue_not_found = "Índice EAC no encontrado";
	error_certificate_revoked = "Se ha revocado el certificado del catálogo de EAC";
	error_corrupted_memory = "Memoria dañada";
	error_corrupted_network = "Flujo de paquetes dañado";
	error_file_forbidden = "Archivo de juego desconocido";
	error_file_not_found = "Falta un archivo necesario";
	error_file_version = "Versión de archivo desconocida";
	error_module_forbidden = "Módulo prohibido";
	error_system_configuration = "Configuración del sistema prohibida";
	error_system_version = "Archivo de sistema no confiable";
	error_tool_forbidden = "Herramienta prohibida";
	error_violation = "Error interno antitrampas";
	error_virtual = "No puede ejecutarse en máquinas virtuales.";
	peer_client_banned = "Par antitrampas vetado.";
	peer_heartbeat_rejected = "Par antitrampas rechazado.";
	peer_validated = "Validación par antitrampas completada.";
	peer_validation_failed = "Error en la validación par antitrampas.";
	executable_not_hashed = "No se pudo encontrar el ejecutable del juego en el catálogo.";
};
launcher:
{
	btn_cancel = "Cancelar";
	btn_exit = "Salir";
	error_cancel = "Iniciación cancelada";
	error_filenotfound = "Archivo no encontrado";
	error_init = "Error al iniciar";
	error_install = "Error al instalar";
	error_launch = "Error al iniciar";
	error_nolib = "No se pudo cargar la biblioteca de Easy Anti-Cheat";
	loading = "CARGANDO";
	wait = "Espera, por favor";
	initializing = "INICIALIZANDO";
	success_waiting_for_game = "ESPERANDO AL JUEGO";
	success_closing = "¡Listo!";
	network_error = "Error de red";
	error_no_settings_file = "{0} no encontrado";
	error_invalid_settings_format = "{0} no tiene un formato JSON válido";
	error_missing_required_field = "{0} no tiene un campo requerido ({1})";
	error_invalid_eos_identifier = "{0} contiene un identificador de EOS inválido: ({1})";
	download_progress = "Progreso de la descarga: {0}";
};
launcher_error:
{
	error_already_running = "¡Ya está en ejecución una aplicación que usa Easy Anti-Cheat! {0}";
	error_application = "El cliente del juego ha encontrado un error en la aplicación. Código de error: {0}";
	error_bad_exe_format = "Se requiere un sistema operativo de 64 bits";
	error_bitset_32 = "Por favor, usa la versión 32-bit del juego";
	error_bitset_64 = "Por favor, usa la versión 64-bit del juego";
	error_cancelled = "Operación cancelada por el usuario";
	error_certificate_validation = "Error al validar el certificado de firmas del código de Easy Anti-Cheat";
	error_connection = "¡Ha fallado la conexión a la red de distribución de contenido!";
	error_debugger = "Se ha detectado un programa de depuración. Por favor, desactívalo y vuelve a intentarlo.";
	error_disk_space = "Espacio en disco insuficiente.";
	error_dns = "¡Ha fallado la resolución DNS para la red de distribución de contenidos!";
	error_dotlocal = "Se ha detectado una redirección DotLocal DLL.";
	error_dotlocal_instructions = "Por favor, borra el siguiente archivo";
	error_file_not_found = "Archivo no encontrado:";
	error_forbidden_tool = "Cierra {0} antes de iniciar el juego";
	error_forbidden_driver = "Descarga {0} antes de empezar el juego";
	error_generic = "Error inesperado.";
	error_kernel_debug = "Easy Anti-Cheat no se puede ejecutar si la depuración del kernel está activa";
	error_kernel_dse = "Easy Anti-Cheat no se podrá ejecutar si se ha deshabilitado el uso obligatorio de controladores firmados";
	error_kernel_modified = "Se ha detectado una modificación prohibida del kernel de Windows";
	error_library_load = "No se pudo cargar la biblioteca de Easy Anti-Cheat";
	error_memory = "Memoria insuficiente para iniciar partida";
	error_module_load = "No pudo cargarse el módulo antitrampas";
	error_patched = "Se ha detectado un gestor de arranque de Windows con parches";
	error_process = "No se puede crear el proceso";
	error_process_crash = "El proceso se ha detenido abruptamente";
	error_safe_mode = "Easy Anti-Cheat no se puede ejecutar con el modo seguro de Windows";
	error_socket = "¡Algo está impidiendo que la aplicación acceda a Internet!";
	error_ssl = "¡Se ha producido un error al establecer una conexión SSL con el servicio CDN!";
	error_start = "No se ha podido iniciar el juego";
	error_uncpath_forbidden = "No se puede ejecutar el juego a través de recursos compartidos de red. (ruta UNC).";
	error_connection_failed = "Hubo un error de conexión: ";
	error_missing_game_id = "No se encuentra el id del juego";
	error_dns_resolve_failed = "Hubo un error en la resolución del DNS a través del proxy";
	error_dns_connection_failed = "¡Hubo un problema de conexión a la red de distribución de contenidos! Código curl: {0}!";
	error_http_response = "Código de respuesta HTTP: {0} Código curl: {1}";
	error_driver_handle = "Error inesperado. (Error al abrir el puntero del controlador)";
	error_incompatible_service = "Ya se está ejecutando un servicio incompatible de Easy Anti-Cheat. Cierra los otros juegos que estén en funcionamiento o reinicia el sistema.";
	error_incompatible_driver_version = "Ya se está ejecutando una versión incompatible del controlador Easy Anti-Cheat. Cierra los otros juegos que estén en funcionamiento o reinicia el sistema.";
	error_another_launcher = "Error inesperado. (Otro iniciador está ejecutándose en este momento)";
	error_game_running = "Error inesperado (El juego está ejecutándose en este momento)";
	error_patched_boot_loader = "Se detectó un gestor de arranque de Windows parchado. (La protección contra revisiones del núcleo está desactivada)";
	error_unknown_process = "Cliente de juego no reconocido. Imposible continuar.";
	error_unknown_game = "Juego no configurado. No se puede continuar.";
	error_win7_required = "Es necesario Windows 7 o superior.";
	success_initialized = "Se ha iniciado con éxito Easy Anti-Cheat";
	success_loaded = "Se ha cargado con éxito Easy Anti-Cheat en el juego";
	error_create_process = "Error al crear el proceso de juego: {0}";
	error_create_thread = "Error al crear el hilo de fondo.";
	error_disallowed_cdn_path = "Error inesperado. (Enlace de CDN incorrecto).";
	error_empty_executable_field = "No se ha proporcionado una ruta para el código binario del juego.";
	error_failed_path_query = "Error en la búsqueda de la ruta del proceso";
	error_failed_to_execute = "Error al ejecutar el proceso del juego.";
	error_game_binary_is_directory = "El ejecutable de destino es un directorio.";
	error_game_binary_is_dot_app = "El ejecutable de destino es un directorio. Usa el código binario dentro de la .app.";
	error_game_binary_not_found = "No se encontró el código binario del juego: '{0}'.";
	error_game_binary_not_found_wine = "No se encontró el código binario del juego (Wine).";
	error_game_security_violation = "Violación de la seguridad del juego {0}";
	error_generic_ex = "Error inesperado. {0}";
	error_instance_count_limit = "Cantidad máxima de instancias de juego alcanzada.";
	error_internal = "Error interno.";
	error_invalid_executable_path = "La ruta ejecutable del juego no es válida.";
	error_memory_ex = "Memoria insuficiente para iniciar el juego {0}";
	error_missing_binary_path = "No se encuentra la ruta del ejecutable del juego.";
	error_missing_directory_path = "No se encuentra una ruta de directorio de trabajo.";
	error_module_initialize = "Inicialización fallida del módulo con {0}";
	error_module_loading = "Error al cargar el módulo antitrampas.";
	error_set_environment_variables = "Error al establecer las variables de entorno para el proceso del juego.";
	error_unrecognized_blacklisted_driver = "Se detectó N/A. Descárgalo e inténtalo de nuevo.";
	error_unsupported_machine_arch = "Arquitectura de la computadora anfitriona no compatible. ({0})";
	error_working_directory_not_found = "El directorio de trabajo no existe.";
	error_x8664_required = "Sistema operativo no compatible. Se requiere una versión de Windows de 64 bits (x86-64).";
	warn_module_download_size = "Tamaño de respuesta de HTTP: {0}. Inicio en modo de cliente nulo.";
	warn_vista_deprecation = "Easy Anti-Cheat dejó de recibir soporte para Windows Vista en octubre de 2020 porque ya no se pueden crear firmas de código compatibles. Visita https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "No se pudo validar la configuración anti-cheat.";
	warn_win7_update_required = "Ejecuta las actualizaciones de Windows, tu sistema carece de compatibilidad crítica con la firma de código SHA-2 requerida desde octubre de 2020. Visita https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Expiró la actualización del servicio de Easy Anti-Cheat."
	error_launch_ex = "Error al iniciar: {0}";
};
setup:
{
	btn_finish = "Finalizar";
	btn_install = "Instalar ahora";
	btn_repair = "Servicio de reparación";
	btn_uninstall = "Desinstalar";
	epic_link = "© Epic Games, Inc";
	install_progress = "Instalando...";
	install_success = "Se ha instalado con éxito";
	licenses_link = "Licencias";
	privacy_link = "Privacidad";
	repair_progress = "Reparando...";
	title = "Configuración del servicio Easy Anti-Cheat";
	uninstall_progress = "Desinstalando...";
	uninstall_success = "Se ha desinstalado con éxito";
};
setup_error:
{
	error_cancelled = "Operación cancelada por el usuario";
	error_encrypted = "La carpeta de instalación de Easy Anti-Cheat se ha encriptado";
	error_intro = "Error en la configuración de Easy Anti-Cheat";
	error_not_installed = "Easy Anti-Cheat no está instalado.";
	error_registry = "Acceso denegado al registro";
	error_rights = "Privilegios insuficientes";
	error_service = "No se puede crear el servicio";
	error_service_ex = "No se puede crear el servicio {0}";
	error_system = "Acceso denegado a System32";
};