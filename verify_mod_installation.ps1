# Space Marine 2 - Mod Installation Verification Script
# This script verifies your mod installation is correct

Write-Host "Space Marine 2 Mod Installation Verification" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

$LocalPath = "client_pc\root\local"
$ModsPath = "client_pc\root\mods"
$ModsSourcePath = "ModEditor\mods_source"

# Check if directories exist
Write-Host "`nChecking directory structure..." -ForegroundColor Yellow

$Checks = @(
    @{Path = $LocalPath; Name = "Local mods directory"},
    @{Path = $ModsPath; Name = "Pak mods directory"},
    @{Path = $ModsSourcePath; Name = "Mods source directory"}
)

foreach ($Check in $Checks) {
    if (Test-Path $Check.Path) {
        Write-Host "✓ $($Check.Name): Found" -ForegroundColor Green
    } else {
        Write-Host "✗ $($Check.Name): Missing" -ForegroundColor Red
    }
}

# Check local folder contents
Write-Host "`nAnalyzing local folder contents..." -ForegroundColor Yellow

if (Test-Path $LocalPath) {
    $LocalFiles = Get-ChildItem -Path $LocalPath -Recurse -File
    $FileTypes = $LocalFiles | Group-Object Extension | Sort-Object Count -Descending
    
    Write-Host "Files in local folder: $($LocalFiles.Count)" -ForegroundColor Cyan
    
    if ($FileTypes) {
        Write-Host "File types found:" -ForegroundColor Cyan
        foreach ($Type in $FileTypes) {
            $Extension = if ($Type.Name) { $Type.Name } else { "(no extension)" }
            Write-Host "  $Extension : $($Type.Count) files" -ForegroundColor White
        }
    }
    
    # Check for common mod files
    $SSLFiles = $LocalFiles | Where-Object { $_.Extension -in @('.sso', '.cls', '.ssl') }
    $ResourceFiles = $LocalFiles | Where-Object { $_.Extension -in @('.tpl', '.pct', '.resource') }
    
    Write-Host "`nMod file analysis:" -ForegroundColor Cyan
    Write-Host "  SSL/Script files: $($SSLFiles.Count)" -ForegroundColor White
    Write-Host "  Resource files: $($ResourceFiles.Count)" -ForegroundColor White
    
    if ($SSLFiles.Count -gt 0) {
        Write-Host "`nSSL files found:" -ForegroundColor Cyan
        $SSLFiles | ForEach-Object {
            $RelPath = $_.FullName.Replace((Resolve-Path $LocalPath).Path + "\", "")
            Write-Host "  $RelPath" -ForegroundColor White
        }
    }
} else {
    Write-Host "Local folder not found!" -ForegroundColor Red
}

# Check mods folder
Write-Host "`nAnalyzing mods folder..." -ForegroundColor Yellow

if (Test-Path $ModsPath) {
    $PakFiles = Get-ChildItem -Path $ModsPath -Filter "*.pak"
    $ConfigFile = Join-Path $ModsPath "pak_config.yaml"
    
    Write-Host "Pak files found: $($PakFiles.Count)" -ForegroundColor Cyan
    
    if ($PakFiles.Count -gt 0) {
        Write-Host "Pak files:" -ForegroundColor Cyan
        $PakFiles | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor White
        }
    }
    
    if (Test-Path $ConfigFile) {
        Write-Host "✓ pak_config.yaml found" -ForegroundColor Green
    } else {
        Write-Host "! pak_config.yaml not found (optional)" -ForegroundColor Yellow
    }
} else {
    Write-Host "Mods folder not found!" -ForegroundColor Red
}

# Check for potential conflicts
Write-Host "`nChecking for potential conflicts..." -ForegroundColor Yellow

if ((Test-Path $LocalPath) -and (Test-Path $ModsSourcePath)) {
    # Compare timestamps
    $LocalFiles = Get-ChildItem -Path $LocalPath -Recurse -File | Where-Object { $_.Extension -in @('.sso', '.cls', '.ssl') }
    $SourceFiles = Get-ChildItem -Path $ModsSourcePath -Recurse -File | Where-Object { $_.Extension -in @('.sso', '.cls', '.ssl') }
    
    $OutdatedFiles = @()
    foreach ($LocalFile in $LocalFiles) {
        $RelPath = $LocalFile.FullName.Replace((Resolve-Path $LocalPath).Path, "").TrimStart('\')
        $SourceFile = Join-Path $ModsSourcePath $RelPath
        
        if (Test-Path $SourceFile) {
            $SourceTime = (Get-Item $SourceFile).LastWriteTime
            $LocalTime = $LocalFile.LastWriteTime
            
            if ($SourceTime -gt $LocalTime) {
                $OutdatedFiles += $RelPath
            }
        }
    }
    
    if ($OutdatedFiles.Count -gt 0) {
        Write-Host "⚠ Outdated files in local (source is newer):" -ForegroundColor Yellow
        $OutdatedFiles | ForEach-Object {
            Write-Host "  $_" -ForegroundColor Yellow
        }
        Write-Host "Consider re-copying these files from mods_source" -ForegroundColor Yellow
    } else {
        Write-Host "✓ All local files are up to date" -ForegroundColor Green
    }
}

Write-Host "`nVerification complete!" -ForegroundColor Green
Write-Host "`nRecommendations:" -ForegroundColor Magenta
Write-Host "1. Ensure local files maintain exact folder structure from mods_source" -ForegroundColor White
Write-Host "2. Copy both SSL files AND their associated resource files" -ForegroundColor White
Write-Host "3. Test in-game to verify mods are working" -ForegroundColor White
Write-Host "4. Check game logs if mods don't load properly" -ForegroundColor White
