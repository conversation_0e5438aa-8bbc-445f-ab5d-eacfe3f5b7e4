lodInfo = {
   maxLodDist = [
      10,
      20,
      30,
      40,
      50
   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      rotor_01 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rotor_01_LOD1 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rotor_01_LOD2 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rotor_01_LOD3 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rotor_01_LOD4 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      rb_wpn = {
         collision = {
            abilityProjectile = false
            light = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            soundOcclusion = true
            soundObstruction = true
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      shells_source = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      flame_area = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      flame_area1 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      tube3 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      cooling = {
         collision = {
            plrMove = false
            abilityProjectile = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      base = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      base_LOD1 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      base_LOD2 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      base_LOD3 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      base_LOD4 = {
         collision = {
            plrMove = false
            abilityMove = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
