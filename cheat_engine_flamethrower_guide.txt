SPACE MARINE 2 - CHEAT ENGINE FLAMETHROWER GUIDE
===============================================

STEP 1: DOWNLOAD CHEAT ENGINE
1. Go to: https://cheatengine.org/
2. Download the latest version (free)
3. Install Cheat Engine
4. Launch Cheat Engine

STEP 2: LAUNCH SPACE MARINE 2
1. Start Space Marine 2
2. Load any mission (Campaign, Operations, etc.)
3. Make sure you have your pistol equipped
4. Note your current pistol damage (probably 25-35)

STEP 3: ATTACH CHEAT ENGINE
1. In Cheat Engine, click the computer icon (top-left)
2. Find "Warhammer 40000 Space Marine 2.exe" in the process list
3. Click it and press "Open"
4. Cheat Engine is now attached to the game

STEP 4: SEARCH FOR PISTOL DAMAGE
1. In the "Value" field, enter: 25
2. Make sure "Value Type" is set to "4 Bytes"
3. Click "First Scan"
4. You'll see many results in the left panel

STEP 5: NARROW DOWN THE SEARCH
1. Go back to the game
2. Fire your pistol at an enemy and note the damage
3. If it's still 25, search for 25 again
4. If it changed, search for the new value
5. Keep narrowing until you have 1-10 results

STEP 6: MODIFY THE DAMAGE
1. Double-click on the damage value in the left panel
2. It will appear in the bottom panel
3. Double-click the "Value" column in the bottom panel
4. Change it to: 999
5. Press Enter

STEP 7: TEST THE FLAMETHROWER
1. Go back to the game
2. Fire your pistol
3. You should now see 999+ damage!
4. Enemies should die instantly

ADVANCED CHEAT ENGINE TIPS:
- Search for "Float" values if 4 Bytes doesn't work
- Try searching for damage ranges (e.g., 20 to 30)
- Look for multiple damage values (base damage, crit damage, etc.)
- Use "Unknown initial value" then "Decreased value" after firing

COMMON PISTOL DAMAGE VALUES TO SEARCH FOR:
- 25 (most common)
- 30 (upgraded pistol)
- 35 (veteran pistol)
- 28 (with perks)
- 32 (with modifications)

TROUBLESHOOTING:
- If too many results, fire pistol and search for "Changed value"
- If no results, try searching for Float instead of 4 Bytes
- Some values might be stored as percentages (0.25 instead of 25)
- Try searching for ammo count (60, 80, 100) to find weapon data

SUCCESS INDICATORS:
- Pistol damage shows 999+ in game
- Enemies die in 1-2 shots
- Area damage affects multiple enemies
- Burning effects appear on enemies

This method gives INSTANT results but resets when you restart the game.
Use the hex editing method below for permanent changes.
