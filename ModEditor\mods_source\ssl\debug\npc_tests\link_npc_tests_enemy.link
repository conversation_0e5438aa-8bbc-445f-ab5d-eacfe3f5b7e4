__request   =   {
   __select   =   {
      __id   =   "actor"
      __where   =   {
         __type   =   "NpcTestsAdditionalActor"
      }
      __path   =   "additionalActors/*"
      __type   =   "model"
   }
   __modelType   =   "NpcTestsTestedActorEnemyInfo"
   __type   =   "modelParent"
}
__resultFormatter   =   {
   __format   =   "{actor}"
   __type   =   "rf_text"
}
__addEmpty   =   {
   __value   =   True
   __alias   =   "<TEST_NPC>"
}
__type   =   "linkBrowser"

