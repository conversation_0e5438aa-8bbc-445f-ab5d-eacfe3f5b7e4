// Space Marine 2 - Simple Heavy Bolter to Flamethrower Conversion
// This directly modifies the Heavy Bolter to behave like a flamethrower

FirearmLibraryPve = {
    firearms = {
        heavy_bolter = {
            uid = "heavy_bolter"
            name = "Heavy Bolter"  // Keep original name so it shows up normally
            
            // Change to flamethrower stats
            damage = {
                base = 80.0                    // High damage
                armorPenetration = 0.2         // Low penetration
                criticalMultiplier = 1.3       // Moderate crits
                falloffStart = 8.0             // Short range falloff
                falloffEnd = 15.0              // Max range 15m
            }
            
            // Flamethrower-like fire rate
            fireRate = {
                roundsPerMinute = 600          // High rate of fire
                burstLength = 10               // Long bursts
                spinUpTime = 0.3               // Quick spin-up
                cooldownTime = 1.5             // Short cooldown
            }
            
            // Fuel-like ammo system
            ammo = {
                maxAmmo = 300                  // Lots of "ammo"
                clipSize = 100                 // Large magazine
                reloadTime = 3.0               // Quick reload
                ammoPerShot = 1                // Normal consumption
            }
            
            // Short range, high accuracy
            range = {
                effective = 12.0               // Short effective range
                maximum = 18.0                 // Short max range
                optimal = 6.0                  // Very close optimal
            }
            
            // Very accurate at close range
            accuracy = {
                hipFire = 0.95                 // Excellent hip fire
                aimDownSight = 0.98            // Perfect when aiming
                movementPenalty = 0.1          // Minimal movement penalty
                recoil = {
                    vertical = 0.1             // Very low recoil
                    horizontal = 0.05          // Minimal horizontal recoil
                    pattern = "tight"          // Tight pattern
                }
            }
            
            // Keep heavy weapon feel
            handling = {
                weight = 7.0                   // Heavy but manageable
                aimDownSightTime = 1.0         // Reasonable ADS time
                movementSpeedMultiplier = 0.8  // 20% speed reduction
                swapTime = 2.0                 // Normal swap time
            }
            
            // Area damage projectile
            projectile = {
                type = "explosive"             // Use explosive type for AoE
                speed = 30.0                   // Moderate speed
                gravity = 0.3                  // Slight drop
                lifetime = 0.6                 // Short lifetime
                penetration = 1                // Can penetrate 1 enemy
                areaOfEffect = 2.5             // 2.5m AoE radius
                maxTargets = 6                 // Hit up to 6 enemies
            }
            
            // Enhanced effects
            effects = {
                muzzleFlash = "sfx_heavy_bolter_muzzle"     // Keep original effects
                projectileTrail = "sfx_heavy_bolter_trail"
                impactEffect = "sfx_heavy_bolter_impact"
                
                // Add some flame-like visuals
                additionalEffects = {
                    flameParticles = true
                    burnMarks = true
                    smokeTrail = true
                }
            }
            
            // Keep original audio but enhance
            audio = {
                fireSound = "wpn_heavy_bolter_fire"
                reloadSound = "wpn_heavy_bolter_reload"
                emptySound = "wpn_heavy_bolter_empty"
                
                // Add flame-like audio layers
                additionalAudio = {
                    flameLoop = true
                    crackleSound = true
                    whooshSound = true
                }
            }
            
            // Special flamethrower mechanics
            specialMechanics = {
                // Overheating system
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 2.0
                    coolingRate = 20.0
                    overheatPenalty = 2.0
                }
                
                // Area damage on impact
                areaDamage = {
                    enabled = true
                    radius = 2.5
                    damageMultiplier = 0.8
                    falloffType = "linear"
                }
                
                // Burning effect
                statusEffects = {
                    burning = {
                        enabled = true
                        applyChance = 0.7
                        duration = 3.0
                        damagePerSecond = 15.0
                    }
                }
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
