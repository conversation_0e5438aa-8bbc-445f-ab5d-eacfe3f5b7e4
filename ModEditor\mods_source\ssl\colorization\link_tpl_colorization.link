__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __id   =   "COLOR_INDEX"
            __select   =   {
               __id   =   "COLOR_NAME"
               __format   =   "{value}"
               __showValue   =   True
               __path   =   "name"
               __type   =   "model"
            }
            __showOnlyChildren   =   True
            __where   =   {
               __name   =   "^\\d+$"
            }
            __path   =   "/tpl_markup/tplCustomization/colorizationSet/entries/*"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __type   =   "tplMarkup"
      }
      __showOnlyChildren   =   True
      __showValue   =   True
      __path   =   "nameTpl"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __type   =   "modelParent"
}
__resultFormatter   =   {
   __format   =   "{COLOR_INDEX}. {COLOR_NAME}"
   __type   =   "rf_text"
}
__addEmpty   =   {
   __value   =   True
}
__type   =   "linkBrowser"

