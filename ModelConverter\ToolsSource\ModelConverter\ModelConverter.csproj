﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>mc_icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="mc_icon.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SharpGLTF.Core" Version="1.0.3" />
    <PackageReference Include="SharpGLTF.Runtime" Version="1.0.3" />
    <PackageReference Include="SharpGLTF.Toolkit" Version="1.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SaberTools\SaberTools.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
