#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;
layout (location = 2) in vec3 aNormal;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
uniform float time;
uniform float flameIntensity;

out vec2 TexCoord;
out vec3 Normal;
out vec3 FragPos;
out float FlameTime;

void main() {
    vec3 pos = aPos;
    
    // Add flame movement and flickering
    float flicker = sin(time * 8.0 + aPos.y * 4.0) * 0.1;
    pos.x += sin(time * 3.0 + aPos.y * 2.0) * 0.15 * flameIntensity;
    pos.z += cos(time * 2.5 + aPos.y * 3.0) * 0.1 * flameIntensity;
    pos.y += flicker * flameIntensity;
    
    FragPos = vec3(model * vec4(pos, 1.0));
    Normal = mat3(transpose(inverse(model))) * aNormal;
    TexCoord = aTexCoord;
    FlameTime = time;
    
    gl_Position = projection * view * vec4(<PERSON>agP<PERSON>, 1.0);
}