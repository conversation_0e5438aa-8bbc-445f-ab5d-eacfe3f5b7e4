__request   =   {
   __select   =   {
      __id   =   "meleeDescs"
      __select   =   {
         __id   =   "comboDesc"
         __select   =   {
            __id   =   "settings"
            __path   =   "strikeSettings/*"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __showValue   =   False
         __path   =   "comboDesc"
         __type   =   "model"
      }
      __format   =   "{name}"
      __path   =   "meleeDescs/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "MeleeDescriptionsLibrary"
   }
   __type   =   "brand"
}
__type   =   "linkBrowser"


