// Space Marine 2 - Bolt Pistol Flamethrower Replacement
// This replaces the Bolt Pistol with flamethrower behavior
// Easy access - every class starts with a pistol!

bolt_pistol = {
    uid = "bolt_pistol"
    name = "Bolt Pistol"
    
    // Flamethrower damage - very high for a pistol
    damage = {
        base = 150.0                    // Extremely high damage
        armorPenetration = 0.1          // Very low armor penetration
        criticalMultiplier = 1.1        // Low crit multiplier
        falloffStart = 6.0              // Damage starts falling off at 6m
        falloffEnd = 12.0               // No damage beyond 12m
    }
    
    // Flamethrower fire rate - rapid continuous fire
    fireRate = {
        roundsPerMinute = 1200          // Very high rate of fire
        burstLength = 15                // Long bursts
        spinUpTime = 0.1                // Instant spin-up
        cooldownTime = 0.8              // Quick cooldown
    }
    
    // Flamethrower ammo - fuel system
    ammo = {
        maxAmmo = 500                   // Lots of fuel
        clipSize = 80                   // Large fuel tank
        reloadTime = 2.0                // Quick refuel
        ammoPerShot = 1                 // Normal consumption
    }
    
    // Short range characteristics
    range = {
        effective = 10.0                // Short effective range
        maximum = 15.0                  // Short max range
        optimal = 5.0                   // Very close optimal range
    }
    
    // Perfect accuracy at close range
    accuracy = {
        hipFire = 0.99                  // Perfect hip fire
        aimDownSight = 1.0              // Perfect when aiming
        movementPenalty = 0.0           // No movement penalty
        recoil = {
            vertical = 0.0              // No recoil
            horizontal = 0.0            // No horizontal recoil
            pattern = "none"            // No recoil pattern
        }
    }
    
    // Light weapon handling
    handling = {
        weight = 2.0                    // Very light
        aimDownSightTime = 0.3          // Very quick ADS
        movementSpeedMultiplier = 1.0   // No speed reduction
        swapTime = 0.8                  // Very quick swap
    }
    
    // Area damage projectile
    projectile = {
        type = "explosive"              // Use explosive for AoE
        speed = 40.0                    // Fast projectile
        gravity = 0.1                   // Minimal drop
        lifetime = 0.4                  // Short lifetime
        penetration = 3                 // Can penetrate 3 enemies
        areaOfEffect = 2.5              // 2.5m AoE radius
        maxTargets = 6                  // Hit up to 6 enemies
    }
    
    // Enhanced visual effects
    effects = {
        muzzleFlash = "sfx_bolt_pistol_muzzle"
        projectileTrail = "sfx_bolt_pistol_trail"
        impactEffect = "sfx_bolt_pistol_impact"
        
        // Additional flame-like effects
        additionalEffects = {
            flameParticles = true
            burnMarks = true
            smokeTrail = true
            emberEffect = true
            sparkEffect = true
        }
    }
    
    // Enhanced audio
    audio = {
        fireSound = "wpn_bolt_pistol_fire"
        reloadSound = "wpn_bolt_pistol_reload"
        emptySound = "wpn_bolt_pistol_empty"
        
        // Additional flame audio
        additionalAudio = {
            flameLoop = true
            crackleSound = true
            whooshSound = true
            hissSound = true
        }
    }
    
    // Flamethrower special mechanics
    specialMechanics = {
        // Overheating system
        overheating = {
            enabled = true
            maxHeat = 100.0
            heatPerShot = 2.5
            coolingRate = 30.0
            overheatPenalty = 1.0
        }
        
        // Area damage on impact
        areaDamage = {
            enabled = true
            radius = 2.5
            damageMultiplier = 1.0
            falloffType = "linear"
            friendlyFire = false
        }
        
        // Burning status effect
        statusEffects = {
            burning = {
                enabled = true
                applyChance = 0.95
                duration = 5.0
                damagePerSecond = 25.0
                stackable = true
                maxStacks = 4
            }
            
            // Fear effect on basic enemies
            fear = {
                enabled = true
                applyChance = 0.5
                duration = 3.0
                affectedTypes = [
                    "basic_enemy",
                    "cultist",
                    "tyranid_basic",
                    "chaos_marine_basic"
                ]
            }
            
            // Panic effect
            panic = {
                enabled = true
                applyChance = 0.3
                duration = 2.0
                movementSpeedMultiplier = 1.5
                accuracyPenalty = 0.7
            }
        }
        
        // Environmental effects
        environmental = {
            igniteObjects = true
            clearVegetation = true
            meltIce = true
            igniteChance = 0.95
            burnDuration = 10.0
            spreadRadius = 3.0
            
            // Ground fire patches
            groundFire = {
                enabled = true
                duration = 6.0
                damagePerSecond = 15.0
                radius = 1.5
            }
        }
        
        // Continuous fire mode
        continuousFire = {
            enabled = true
            damagePerTick = 150.0
            tickRate = 0.1
            fuelConsumption = 1.0
        }
    }
    
    // UI display (keep Bolt Pistol appearance)
    ui = {
        icon = "ui_bolt_pistol_icon"
        description = "Compact flamethrower with devastating close-range area damage."
        weaponClass = "Pistol"
        unlockLevel = 1
        
        // Enhanced stats display
        stats = {
            damage = 10                 // Maximum damage
            range = 3                   // Short range
            accuracy = 10               // Perfect accuracy
            mobility = 10               // Maximum mobility
            ammo = 8                    // Good ammo capacity
        }
    }
    
    // Weapon mastery progression
    mastery = {
        tier1 = {
            name = "Extended Range"
            effect = "range_increase"
            value = 1.3
        }
        tier2 = {
            name = "Fuel Efficiency"
            effect = "ammo_efficiency"
            value = 0.7
        }
        tier3 = {
            name = "Enhanced Burning"
            effect = "status_duration"
            value = 1.5
        }
        tier4 = {
            name = "Rapid Cooling"
            effect = "cooling_rate"
            value = 2.0
        }
        tier5 = {
            name = "Inferno Burst"
            effect = "area_damage"
            value = 1.5
        }
    }
    
    __type = "FirearmDescription"
}
