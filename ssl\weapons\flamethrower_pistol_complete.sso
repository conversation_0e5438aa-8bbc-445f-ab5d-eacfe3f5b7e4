// Space Marine 2 - Complete Flamethrower Pistol with Full Animations
// This replaces the bolt pistol with a fully animated flamethrower

FirearmLibraryPve = {
    firearms = {
        bolt_pistol = {
            uid = "bolt_pistol"
            name = "Inferno Pistol"
            description = "Blessed flamethrower weapon that purges heretics with righteous fire"
            category = "pistol"
            
            // Weapon type and behavior
            weaponType = "flamethrower"
            damageType = "fire"
            fireMode = "continuous"
            
            // Devastating flamethrower damage
            damage = {
                base = 999.0
                armorPenetration = 0.1
                criticalMultiplier = 2.0
                falloffStart = 8.0
                falloffEnd = 15.0
                damageType = "fire"
                statusEffect = "burning"
            }
            
            // Continuous fire mechanics
            fireRate = {
                roundsPerMinute = 2000
                burstLength = -1
                spinUpTime = 0.1
                cooldownTime = 0.5
                fireType = "continuous"
            }
            
            // Fuel system
            ammo = {
                maxAmmo = 999
                clipSize = 200
                reloadTime = 2.0
                ammoPerShot = 1
                ammoType = "promethium"
            }
            
            // Short range flamethrower
            range = {
                effective = 12.0
                maximum = 18.0
                optimal = 6.0
                spreadAngle = 15.0
            }
            
            // Perfect accuracy for flame stream
            accuracy = {
                hipFire = 1.0
                aimDownSight = 1.0
                movementPenalty = 0.0
                recoil = {
                    vertical = 0.0
                    horizontal = 0.0
                    pattern = "none"
                }
            }
            
            // Lightweight handling
            handling = {
                weight = 2.0
                aimDownSightTime = 0.2
                movementSpeedMultiplier = 0.95
                swapTime = 0.4
            }
            
            // Flame projectile system
            projectile = {
                type = "flame_stream"
                speed = 30.0
                gravity = 0.0
                lifetime = 0.6
                penetration = 5
                areaOfEffect = 4.0
                maxTargets = 10
                
                // Flame-specific properties
                flameProperties = {
                    streamWidth = 2.0
                    particleDensity = "high"
                    heatDistortion = true
                    ignitionChance = 1.0
                }
            }
            
            // Complete visual effects system
            effects = {
                // Muzzle effects
                muzzleFlash = "fx_flamethrower_muzzle_burst"
                muzzleSmoke = "fx_flame_ignition_smoke"
                muzzleLight = "fx_orange_flame_light"
                
                // Projectile effects
                projectileTrail = "fx_flame_stream_continuous"
                projectileParticles = "fx_fire_particles_dense"
                projectileLight = "fx_flame_dynamic_light"
                
                // Impact effects
                impactEffect = "fx_flame_explosion_large"
                impactDecal = "fx_burn_mark_ground"
                impactSound = "sfx_flame_impact_sizzle"
                
                // Environmental effects
                environmentalEffects = {
                    heatShimmer = "fx_heat_distortion_cone"
                    emberTrail = "fx_floating_embers"
                    smokeTrail = "fx_black_smoke_trail"
                    groundFire = "fx_ground_flame_spread"
                }
                
                // Screen effects
                screenEffects = {
                    heatBlur = "fx_screen_heat_blur"
                    fireGlow = "fx_screen_orange_glow"
                    emberOverlay = "fx_screen_ember_particles"
                }
            }
            
            // Complete audio system
            audio = {
                // Primary sounds
                fireSound = "sfx_flamethrower_continuous_roar"
                reloadSound = "sfx_promethium_tank_reload"
                emptySound = "sfx_flamethrower_empty_hiss"
                
                // Ignition sounds
                ignitionSound = "sfx_pilot_light_ignite"
                shutoffSound = "sfx_flame_extinguish"
                
                // Environmental audio
                environmentalAudio = {
                    crackleLoop = "sfx_fire_crackle_loop"
                    whooshSound = "sfx_flame_whoosh_continuous"
                    hissSound = "sfx_gas_pressure_hiss"
                    sizzleSound = "sfx_burning_flesh_sizzle"
                }
                
                // 3D audio properties
                audioProperties = {
                    volume = 1.2
                    pitch = 1.0
                    spatialBlend = 0.8
                    dopplerLevel = 0.5
                    rolloffMode = "logarithmic"
                }
            }
            
            // Advanced mechanics
            specialMechanics = {
                // Overheating system
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 1.5
                    coolingRate = 25.0
                    overheatPenalty = 2.0
                    overheatEffect = "fx_weapon_overheat_steam"
                }
                
                // Area damage system
                areaDamage = {
                    enabled = true
                    radius = 4.0
                    damageMultiplier = 1.0
                    falloffType = "linear"
                    friendlyFire = false
                    penetrateWalls = false
                }
                
                // Status effects
                statusEffects = {
                    burning = {
                        enabled = true
                        applyChance = 1.0
                        duration = 8.0
                        damagePerSecond = 75.0
                        stackable = true
                        maxStacks = 5
                        visualEffect = "fx_enemy_burning_flames"
                    }
                    
                    panic = {
                        enabled = true
                        applyChance = 0.8
                        duration = 5.0
                        effect = "fear"
                        visualEffect = "fx_enemy_panic_aura"
                    }
                }
                
                // Environmental interaction
                environmentalInteraction = {
                    ignitesGas = true
                    meltsIce = true
                    burnsCover = true
                    lightsArea = true
                    clearsFog = true
                }
            }
            
            // Animation system
            animations = {
                // Weapon animations
                weaponAnimations = {
                    idle = "anim_flamethrower_idle_pilot"
                    fire = "anim_flamethrower_continuous_stream"
                    reload = "anim_promethium_tank_swap"
                    draw = "anim_flamethrower_ignite_sequence"
                    holster = "anim_flamethrower_extinguish"
                }
                
                // Character animations
                characterAnimations = {
                    fireStance = "anim_marine_flamethrower_stance"
                    reloadStance = "anim_marine_tank_reload"
                    movementModifier = "anim_marine_heavy_weapon_walk"
                }
                
                // First person animations
                firstPersonAnimations = {
                    fireAnimation = "anim_fp_flamethrower_stream"
                    reloadAnimation = "anim_fp_tank_reload_detailed"
                    inspectAnimation = "anim_fp_flamethrower_inspect"
                }
            }
            
            // UI display
            ui = {
                icon = "ui_flamethrower_pistol_icon"
                description = "Compact flamethrower that unleashes the Emperor's wrath in streams of purifying fire"
                weaponClass = "Flamethrower"
                unlockLevel = 1
                
                // Enhanced stats display
                stats = {
                    damage = 10
                    range = 4
                    accuracy = 10
                    mobility = 8
                    ammo = 9
                }
                
                // UI effects
                uiEffects = {
                    iconGlow = "fx_ui_fire_glow"
                    selectionEffect = "fx_ui_flame_border"
                    ammoIndicator = "fx_ui_fuel_gauge"
                }
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
