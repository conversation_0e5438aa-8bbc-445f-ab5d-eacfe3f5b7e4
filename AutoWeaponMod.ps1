# Space Marine 2 - Automated Weapon Modification Tool
# One-click solution for overpowered weapons - NO CHEAT ENGINE REQUIRED

param(
    [switch]$AutoRun = $false
)

Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

public class MemoryEditor {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);
    
    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [DllImport("kernel32.dll")]
    public static extern bool VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }
    
    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const uint MEM_COMMIT = 0x1000;
    public const uint PAGE_READWRITE = 0x04;
    public const uint PAGE_EXECUTE_READWRITE = 0x40;
}
"@

Write-Host "🔥 SPACE MARINE 2 - AUTOMATED WEAPON MOD 🔥" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Red
Write-Host ""
Write-Host "This tool automatically creates overpowered weapons!" -ForegroundColor Green
Write-Host "✓ 999 damage flamethrower pistol" -ForegroundColor Yellow
Write-Host "✓ 1500 damage lightning sword" -ForegroundColor Yellow
Write-Host "✓ One-shot kills on most enemies" -ForegroundColor Yellow
Write-Host "✓ No manual work required!" -ForegroundColor Yellow
Write-Host ""

if (-not $AutoRun) {
    Write-Host "INSTRUCTIONS:" -ForegroundColor Cyan
    Write-Host "1. Launch Space Marine 2 first" -ForegroundColor White
    Write-Host "2. Get to a mission with enemies" -ForegroundColor White
    Write-Host "3. Press any key to start the automated mod..." -ForegroundColor White
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

Write-Host ""
Write-Host "🔍 Searching for Space Marine 2 process..." -ForegroundColor Yellow

# Find Space Marine 2 process
$gameProcess = Get-Process | Where-Object { 
    $_.ProcessName -like "*Space Marine*" -or 
    $_.ProcessName -like "*Warhammer*" -or
    $_.MainWindowTitle -like "*Space Marine*"
}

if (-not $gameProcess) {
    Write-Host "❌ Space Marine 2 not found!" -ForegroundColor Red
    Write-Host "Please launch Space Marine 2 first, then run this script again." -ForegroundColor Yellow
    exit 1
}

$processId = $gameProcess[0].Id
$processName = $gameProcess[0].ProcessName
Write-Host "✅ Found: $processName (PID: $processId)" -ForegroundColor Green

# Open process for memory access
$processHandle = [MemoryEditor]::OpenProcess([MemoryEditor]::PROCESS_ALL_ACCESS, $false, $processId)
if ($processHandle -eq [IntPtr]::Zero) {
    Write-Host "❌ Failed to access game process!" -ForegroundColor Red
    Write-Host "Try running this script as Administrator." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Process access granted" -ForegroundColor Green
Write-Host ""

# Function to search for damage values in memory
function Search-DamageValues {
    param(
        [IntPtr]$ProcessHandle,
        [int]$TargetValue,
        [string]$WeaponType
    )
    
    Write-Host "🔍 Searching for $WeaponType damage ($TargetValue)..." -ForegroundColor Yellow
    
    $foundAddresses = @()
    $searchBytes = [BitConverter]::GetBytes([int32]$TargetValue)
    
    # Get process memory regions
    $address = [IntPtr]::Zero
    $memInfo = New-Object MemoryEditor+MEMORY_BASIC_INFORMATION
    
    while ([MemoryEditor]::VirtualQueryEx($ProcessHandle, $address, [ref]$memInfo, [System.Runtime.InteropServices.Marshal]::SizeOf($memInfo))) {
        if ($memInfo.State -eq [MemoryEditor]::MEM_COMMIT -and 
            ($memInfo.Protect -eq [MemoryEditor]::PAGE_READWRITE -or $memInfo.Protect -eq [MemoryEditor]::PAGE_EXECUTE_READWRITE)) {
            
            # Read memory region
            $regionSize = [int]$memInfo.RegionSize
            if ($regionSize -gt 0 -and $regionSize -lt 100MB) {
                $buffer = New-Object byte[] $regionSize
                $bytesRead = 0
                
                if ([MemoryEditor]::ReadProcessMemory($ProcessHandle, $memInfo.BaseAddress, $buffer, $regionSize, [ref]$bytesRead)) {
                    # Search for our target value
                    for ($i = 0; $i -le ($buffer.Length - 4); $i++) {
                        if ($buffer[$i] -eq $searchBytes[0] -and
                            $buffer[$i+1] -eq $searchBytes[1] -and
                            $buffer[$i+2] -eq $searchBytes[2] -and
                            $buffer[$i+3] -eq $searchBytes[3]) {
                            
                            $foundAddress = [IntPtr]::Add($memInfo.BaseAddress, $i)
                            $foundAddresses += $foundAddress
                            
                            if ($foundAddresses.Count -le 5) {
                                Write-Host "  ✓ Found at: 0x$($foundAddress.ToString('X16'))" -ForegroundColor Green
                            }
                        }
                    }
                }
            }
        }
        
        $address = [IntPtr]::Add($memInfo.BaseAddress, [int]$memInfo.RegionSize)
        if ($address -eq [IntPtr]::Zero) { break }
    }
    
    Write-Host "  📊 Total found: $($foundAddresses.Count) locations" -ForegroundColor Cyan
    return $foundAddresses
}

# Function to modify damage values
function Set-DamageValue {
    param(
        [IntPtr]$ProcessHandle,
        [IntPtr[]]$Addresses,
        [int]$NewValue,
        [string]$WeaponType
    )
    
    if ($Addresses.Count -eq 0) {
        Write-Host "  ❌ No addresses to modify for $WeaponType" -ForegroundColor Red
        return $false
    }
    
    Write-Host "🔧 Modifying $WeaponType damage to $NewValue..." -ForegroundColor Yellow
    
    $newBytes = [BitConverter]::GetBytes([int32]$NewValue)
    $successCount = 0
    
    foreach ($address in $Addresses) {
        $bytesWritten = 0
        if ([MemoryEditor]::WriteProcessMemory($ProcessHandle, $address, $newBytes, 4, [ref]$bytesWritten)) {
            $successCount++
            if ($successCount -le 5) {
                Write-Host "  ✅ Modified: 0x$($address.ToString('X16'))" -ForegroundColor Green
            }
        }
    }
    
    Write-Host "  📊 Successfully modified: $successCount/$($Addresses.Count) locations" -ForegroundColor Cyan
    return $successCount -gt 0
}

# Common damage values to search for
$pistolDamageValues = @(25, 30, 35, 28, 32, 40)
$meleeDamageValues = @(60, 75, 80, 90, 100, 120, 150)

$pistolAddresses = @()
$meleeAddresses = @()

Write-Host "🔫 PISTOL MODIFICATION" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

foreach ($damage in $pistolDamageValues) {
    $addresses = Search-DamageValues -ProcessHandle $processHandle -TargetValue $damage -WeaponType "Pistol"
    $pistolAddresses += $addresses
    Start-Sleep -Milliseconds 100
}

if ($pistolAddresses.Count -gt 0) {
    $pistolSuccess = Set-DamageValue -ProcessHandle $processHandle -Addresses $pistolAddresses -NewValue 999 -WeaponType "Pistol"
} else {
    Write-Host "❌ No pistol damage values found" -ForegroundColor Red
    $pistolSuccess = $false
}

Write-Host ""
Write-Host "⚔️ MELEE MODIFICATION" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

foreach ($damage in $meleeDamageValues) {
    $addresses = Search-DamageValues -ProcessHandle $processHandle -TargetValue $damage -WeaponType "Melee"
    $meleeAddresses += $addresses
    Start-Sleep -Milliseconds 100
}

if ($meleeAddresses.Count -gt 0) {
    $meleeSuccess = Set-DamageValue -ProcessHandle $processHandle -Addresses $meleeAddresses -NewValue 1500 -WeaponType "Melee"
} else {
    Write-Host "❌ No melee damage values found" -ForegroundColor Red
    $meleeSuccess = $false
}

# Clean up
[MemoryEditor]::CloseHandle($processHandle)

Write-Host ""
Write-Host "🎯 MODIFICATION COMPLETE!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

if ($pistolSuccess) {
    Write-Host "✅ Pistol: Modified to 999 damage" -ForegroundColor Green
} else {
    Write-Host "❌ Pistol: Modification failed" -ForegroundColor Red
}

if ($meleeSuccess) {
    Write-Host "✅ Melee: Modified to 1500 damage" -ForegroundColor Green
} else {
    Write-Host "❌ Melee: Modification failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎮 TEST YOUR WEAPONS NOW!" -ForegroundColor Yellow
Write-Host "- Fire your pistol at enemies" -ForegroundColor White
Write-Host "- Attack with melee weapons" -ForegroundColor White
Write-Host "- You should see massive damage numbers!" -ForegroundColor White
Write-Host ""

if ($pistolSuccess -or $meleeSuccess) {
    Write-Host "🔥 SUCCESS! You now have overpowered weapons! 🔥" -ForegroundColor Red
} else {
    Write-Host "⚠️ If modifications didn't work:" -ForegroundColor Yellow
    Write-Host "1. Try running as Administrator" -ForegroundColor White
    Write-Host "2. Make sure you're in a combat area" -ForegroundColor White
    Write-Host "3. Try firing weapons first, then run script" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
