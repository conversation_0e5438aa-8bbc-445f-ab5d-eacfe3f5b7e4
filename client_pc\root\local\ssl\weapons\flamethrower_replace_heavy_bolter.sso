// Space Marine 2 - Replace Heavy Bolter with Flamethrower (Temporary)
// This file temporarily replaces the Heavy Bolter with our Flamethrower for testing

FirearmLibraryPve = {
    firearms = {
        // Replace heavy bolter with flamethrower stats
        heavy_bolter = {
            uid = "heavy_bolter"
            name = "Heavy Flamethrower"  // Display as flamethrower
            category = "heavy"
            
            // Use flamethrower mechanics instead of bolter
            weaponType = "heavy_weapon"
            damageType = "fire"          // Changed from ballistic to fire
            fireMode = "continuous"      // Changed from burst to continuous
            
            // Flamethrower damage settings
            damage = {
                base = 45.0              // Flamethrower damage
                armorPenetration = 0.3   // Low penetration
                criticalMultiplier = 1.5
                falloffStart = 10.0      // Short range
                falloffEnd = 20.0
            }
            
            // Continuous fire mechanics
            fireRate = {
                roundsPerMinute = 1200   // High rate for continuous fire
                burstLength = -1         // Infinite burst
                spinUpTime = 0.5         // Spin-up time
                cooldownTime = 2.0       // Cooldown after overheating
            }
            
            // Fuel instead of ammo
            ammo = {
                maxAmmo = 200           // Fuel capacity
                clipSize = 200          // Full tank
                reloadTime = 4.0        // Refuel time
                ammoPerShot = 2         // Fuel consumption per tick
                ammoType = "fuel"
            }
            
            // Short range characteristics
            range = {
                effective = 15.0        // Flamethrower range
                maximum = 20.0
                optimal = 8.0
            }
            
            // High accuracy at close range
            accuracy = {
                hipFire = 0.9           // Very accurate
                aimDownSight = 0.95
                movementPenalty = 0.3
                recoil = {
                    vertical = 0.2      // Low recoil
                    horizontal = 0.1
                    pattern = "minimal"
                }
            }
            
            // Heavy weapon handling
            handling = {
                weight = 8.5
                aimDownSightTime = 1.2
                movementSpeedMultiplier = 0.7
                swapTime = 2.5
            }
            
            // Flame projectile
            projectile = {
                type = "flame_stream"   // Use our custom projectile
                speed = 25.0
                gravity = 0.5
                lifetime = 0.8
                penetration = 0
                areaOfEffect = 3.0
                maxTargets = 8
            }
            
            // Overheating system
            specialMechanics = {
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 1.5
                    coolingRate = 15.0
                    overheatPenalty = 3.0
                }
                
                continuousFire = {
                    enabled = true
                    damagePerTick = 45.0
                    tickRate = 0.05      // 20 ticks per second
                    fuelConsumption = 2.0
                }
            }
            
            // Burning status effects
            statusEffects = {
                burning = {
                    enabled = true
                    applyChance = 0.9
                    duration = 5.0
                    damagePerSecond = 10.0
                    stackable = true
                    maxStacks = 3
                }
                
                fear = {
                    enabled = true
                    applyChance = 0.3
                    duration = 2.0
                    affectedTypes = ["basic_enemy", "cultist"]
                }
            }
            
            // Flame visual effects
            effects = {
                muzzleFlash = "sfx_flamethrower_muzzle"
                projectileTrail = "sfx_flame_stream"
                impactEffect = "sfx_flame_impact"
                scorchMark = "scr_flamethrower"
                
                flameVisuals = {
                    color = [255, 120, 0]
                    intensity = 2.5
                    width = 1.2
                    length = 15.0
                    particleCount = 100
                }
            }
            
            // Flamethrower audio
            audio = {
                fireSound = "wpn_flamethrower_fire"
                reloadSound = "wpn_flamethrower_reload"
                emptySound = "wpn_flamethrower_empty"
                spinUpSound = "wpn_flamethrower_spinup"
                cooldownSound = "wpn_flamethrower_cooldown"
                
                loopingFire = true
                fadeInTime = 0.2
                fadeOutTime = 0.5
            }
            
            // Environmental effects
            environmental = {
                igniteObjects = true
                clearVegetation = true
                meltIce = true
                spreadFire = true
                
                igniteChance = 0.8
                spreadRadius = 2.0
                burnDuration = 10.0
            }
            
            // UI display
            ui = {
                icon = "ui_flamethrower_icon"
                description = "Heavy flamethrower that deals continuous fire damage and applies burning effects to enemies."
                unlockLevel = 1         // Available immediately
                weaponClass = "Heavy"
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
