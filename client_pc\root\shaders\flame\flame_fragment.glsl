#version 330 core
in vec2 TexCoord;
in vec3 Normal;
in vec3 FragPos;
in float FlameTime;

uniform sampler2D flameTexture;
uniform sampler2D noiseTexture;
uniform sampler2D distortionTexture;
uniform vec3 viewPos;
uniform float flameIntensity;
uniform float flameHeight;

out vec4 FragColor;

void main() {
    // Animated texture coordinates
    vec2 animTexCoord = TexCoord;
    animTexCoord.y += FlameTime * 0.4;
    
    // Add noise-based distortion
    vec2 noise = texture(noiseTexture, TexCoord * 2.0 + FlameTime * 0.1).rg;
    animTexCoord += (noise - 0.5) * 0.1;
    
    // Sample flame texture
    vec4 flameColor = texture(flameTexture, animTexCoord);
    
    // Create flame gradient (hotter at bottom, cooler at top)
    float gradient = 1.0 - TexCoord.y;
    vec3 hotColor = vec3(1.0, 1.0, 0.8);  // White-yellow
    vec3 medColor = vec3(1.0, 0.6, 0.2);  // Orange
    vec3 coolColor = vec3(1.0, 0.2, 0.0); // Red
    
    vec3 finalColor;
    if (gradient > 0.7) {
        finalColor = mix(medColor, hotColor, (gradient - 0.7) / 0.3);
    } else {
        finalColor = mix(coolColor, medColor, gradient / 0.7);
    }
    
    // Add flickering
    float flicker = sin(FlameTime * 12.0 + TexCoord.x * 8.0) * 0.1 + 0.9;
    finalColor *= flicker;
    
    // Apply flame intensity
    finalColor *= flameIntensity;
    
    // Calculate alpha based on flame shape and intensity
    float alpha = flameColor.a * gradient * flameIntensity;
    alpha *= smoothstep(0.0, 0.1, TexCoord.y); // Fade in at bottom
    alpha *= smoothstep(1.0, 0.8, TexCoord.y); // Fade out at top
    
    FragColor = vec4(finalColor, alpha);
}