// Space Marine 2 - Clean Pistol Flamethrower Mod
// Replaces Bolt Pistol with flamethrower for instant access

FirearmLibraryPve = {
    firearms = {
        bolt_pistol = {
            uid = "bolt_pistol"
            name = "Bolt Pistol"
            
            // Flamethrower damage
            damage = {
                base = 200.0
                armorPenetration = 0.1
                criticalMultiplier = 1.2
                falloffStart = 5.0
                falloffEnd = 12.0
            }
            
            // High rate of fire
            fireRate = {
                roundsPerMinute = 1500
                burstLength = 20
                spinUpTime = 0.1
                cooldownTime = 0.5
            }
            
            // Lots of ammo
            ammo = {
                maxAmmo = 600
                clipSize = 100
                reloadTime = 1.5
                ammoPerShot = 1
            }
            
            // Short range
            range = {
                effective = 8.0
                maximum = 12.0
                optimal = 4.0
            }
            
            // Perfect accuracy
            accuracy = {
                hipFire = 1.0
                aimDownSight = 1.0
                movementPenalty = 0.0
                recoil = {
                    vertical = 0.0
                    horizontal = 0.0
                    pattern = "none"
                }
            }
            
            // Light handling
            handling = {
                weight = 1.5
                aimDownSightTime = 0.2
                movementSpeedMultiplier = 1.0
                swapTime = 0.5
            }
            
            // Area damage projectile
            projectile = {
                type = "explosive"
                speed = 50.0
                gravity = 0.1
                lifetime = 0.3
                penetration = 5
                areaOfEffect = 3.0
                maxTargets = 10
            }
            
            // Special mechanics
            specialMechanics = {
                overheating = {
                    enabled = true
                    maxHeat = 100.0
                    heatPerShot = 3.0
                    coolingRate = 40.0
                    overheatPenalty = 0.8
                }
                
                areaDamage = {
                    enabled = true
                    radius = 3.0
                    damageMultiplier = 1.0
                    falloffType = "linear"
                    friendlyFire = false
                }
                
                statusEffects = {
                    burning = {
                        enabled = true
                        applyChance = 1.0
                        duration = 6.0
                        damagePerSecond = 30.0
                        stackable = true
                        maxStacks = 5
                    }
                    
                    fear = {
                        enabled = true
                        applyChance = 0.6
                        duration = 4.0
                        affectedTypes = [
                            "basic_enemy",
                            "cultist",
                            "tyranid_basic"
                        ]
                    }
                }
                
                environmental = {
                    igniteObjects = true
                    clearVegetation = true
                    igniteChance = 1.0
                    burnDuration = 15.0
                    spreadRadius = 4.0
                }
            }
            
            // Keep pistol appearance
            ui = {
                icon = "ui_bolt_pistol_icon"
                description = "Compact flamethrower with devastating area damage."
                weaponClass = "Pistol"
                unlockLevel = 1
            }
            
            __type = "FirearmDescription"
        }
    }
    
    __type = "FirearmLibrary"
}
