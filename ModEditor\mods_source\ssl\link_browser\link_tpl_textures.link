__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __where   =   {
                     __name   =   "texture_name"
                  }
                  __format   =   "{value}"
                  __recursive   =   True
                  __selectValue   =   True
                  __uniqueValues   =   True
                  __noEmptyValues   =   True
                  __path   =   ".*"
                  __type   =   "json"
               }
               __baseDir   =   ""
               __baseDirs   =   [
                  "{arg}/../"
               ]
               __searchPattern   =   "obj_list.json"
               __recursive   =   True
               __format   =   "{name no ext}"
               __type   =   "file"
            }
            __treeIndent   =   2
            __argFilter   =   {
               __argFilterFormat   =   "field:tpl"
               __argModification   =   "{arg}.tpl"
            }
            __descTypes   =   [
               "res_desc_tpl"
            ]
            __assetSettings   =   {
               __assetResource   =   ""
            }
            __format   =   ""
            __type   =   "resource"
         }
         __showOnlyChildren   =   True
         __format   =   "{value}"
         __showValue   =   True
         __path   =   "nameTpl"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __where   =   {
         __type   =   "^(geom|lwi_type_base|lwi_type_distr_item)$"
      }
      __onlyChanged   =   False
      __type   =   "modelRecursive"
   }
   __showOnlyChildren   =   True
   __modelType   =   "^(actor|iactor|static_geometry)$"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

