__request   =   {
   __select   =   {
      __id   =   "NAME"
      __select   =   {
         __id   =   "IDX"
         __select   =   {
            __id   =   "VAL"
            __format   =   "{value}"
            __showValue   =   True
            __where   =   {
               __value   =   "^.+$"
            }
            __path   =   "externalVoiceoverEventUid"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __format   =   "{index}"
         __path   =   "voiceoverEvents/*"
         __type   =   "model"
      }
      __path   =   "categories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "VoiceoverLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{VAL}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"


