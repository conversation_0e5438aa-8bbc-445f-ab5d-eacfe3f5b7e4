// Space Marine 2 - Heavy Bolter Flamethrower Replacement
// This file replaces Heavy Bolter with flamethrower behavior
// Place in ModEditor/mods_source/ssl/weapons/ then copy to local

heavy_bolter = {
    uid = "heavy_bolter"
    name = "Heavy Bolter"
    
    // Flamethrower damage - high damage, short range
    damage = {
        base = 120.0                    // Very high damage
        armorPenetration = 0.15         // Low armor penetration
        criticalMultiplier = 1.2        // Low crit multiplier
        falloffStart = 8.0              // Damage starts falling off at 8m
        falloffEnd = 15.0               // No damage beyond 15m
    }
    
    // Flamethrower fire rate - continuous stream
    fireRate = {
        roundsPerMinute = 900           // High rate of fire
        burstLength = 20                // Long bursts
        spinUpTime = 0.2                // Quick spin-up
        cooldownTime = 1.0              // Short cooldown
    }
    
    // Flamethrower ammo - fuel system
    ammo = {
        maxAmmo = 400                   // Lots of fuel
        clipSize = 100                  // Large fuel tank
        reloadTime = 2.5                // Quick refuel
        ammoPerShot = 1                 // Normal consumption
    }
    
    // Short range characteristics
    range = {
        effective = 12.0                // Short effective range
        maximum = 18.0                  // Short max range
        optimal = 6.0                   // Very close optimal range
    }
    
    // High accuracy at close range
    accuracy = {
        hipFire = 0.98                  // Excellent hip fire
        aimDownSight = 0.99             // Perfect when aiming
        movementPenalty = 0.05          // Minimal movement penalty
        recoil = {
            vertical = 0.05             // Very low recoil
            horizontal = 0.02           // Minimal horizontal recoil
            pattern = "tight"           // Tight pattern
        }
    }
    
    // Heavy weapon handling
    handling = {
        weight = 6.5                    // Heavy but manageable
        aimDownSightTime = 0.8          // Quick ADS
        movementSpeedMultiplier = 0.85  // 15% speed reduction
        swapTime = 1.8                  // Quick swap
    }
    
    // Area damage projectile
    projectile = {
        type = "explosive"              // Use explosive for AoE
        speed = 35.0                    // Moderate speed
        gravity = 0.2                   // Slight drop
        lifetime = 0.5                  // Short lifetime
        penetration = 2                 // Can penetrate 2 enemies
        areaOfEffect = 3.0              // 3m AoE radius
        maxTargets = 8                  // Hit up to 8 enemies
    }
    
    // Enhanced visual effects
    effects = {
        muzzleFlash = "sfx_heavy_bolter_muzzle"
        projectileTrail = "sfx_heavy_bolter_trail"
        impactEffect = "sfx_heavy_bolter_impact"
        
        // Additional flame-like effects
        additionalEffects = {
            flameParticles = true
            burnMarks = true
            smokeTrail = true
            emberEffect = true
        }
    }
    
    // Enhanced audio
    audio = {
        fireSound = "wpn_heavy_bolter_fire"
        reloadSound = "wpn_heavy_bolter_reload"
        emptySound = "wpn_heavy_bolter_empty"
        
        // Additional flame audio
        additionalAudio = {
            flameLoop = true
            crackleSound = true
            whooshSound = true
            roarSound = true
        }
    }
    
    // Flamethrower special mechanics
    specialMechanics = {
        // Overheating system
        overheating = {
            enabled = true
            maxHeat = 100.0
            heatPerShot = 1.8
            coolingRate = 25.0
            overheatPenalty = 1.5
        }
        
        // Area damage on impact
        areaDamage = {
            enabled = true
            radius = 3.0
            damageMultiplier = 0.9
            falloffType = "linear"
            friendlyFire = false
        }
        
        // Burning status effect
        statusEffects = {
            burning = {
                enabled = true
                applyChance = 0.85
                duration = 4.0
                damagePerSecond = 20.0
                stackable = true
                maxStacks = 3
            }
            
            // Fear effect on basic enemies
            fear = {
                enabled = true
                applyChance = 0.4
                duration = 2.5
                affectedTypes = [
                    "basic_enemy",
                    "cultist",
                    "tyranid_basic"
                ]
            }
        }
        
        // Environmental effects
        environmental = {
            igniteObjects = true
            clearVegetation = true
            igniteChance = 0.9
            burnDuration = 8.0
            spreadRadius = 2.5
        }
    }
    
    // UI display (keep Heavy Bolter appearance)
    ui = {
        icon = "ui_heavy_bolter_icon"
        description = "Heavy weapon with enhanced area damage and burning effects."
        weaponClass = "Heavy"
        unlockLevel = 1
    }
    
    __type = "FirearmDescription"
}
