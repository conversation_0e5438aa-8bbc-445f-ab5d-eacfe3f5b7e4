# Space Marine 2 - Lightning Sword Hex Mod
# This script creates an overpowered lightning sword from the chainsword

param(
    [int]$DamageValue = 1500,
    [switch]$Backup = $true,
    [switch]$TestMode = $false
)

Write-Host "Space Marine 2 - Lightning Sword Hex Mod" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

$GameFile = "client_pc\root\paks\client\default\default.pak"
$BackupFile = "client_pc\root\paks\client\default\default.pak.lightning_backup"

# Check if game file exists
if (-not (Test-Path $GameFile)) {
    Write-Error "Game file not found: $GameFile"
    exit 1
}

Write-Host "Target: Chainsword → Lightning Sword" -ForegroundColor Yellow
Write-Host "Lightning damage: $DamageValue" -ForegroundColor Cyan

# Create backup
if ($Backup -and -not (Test-Path $BackupFile)) {
    Write-Host "Creating lightning sword backup..." -ForegroundColor Yellow
    Copy-Item $GameFile $BackupFile -Force
    Write-Host "Backup created: $BackupFile" -ForegroundColor Green
}

# Convert damage value to little-endian hex bytes
$DamageBytes = [BitConverter]::GetBytes([int32]$DamageValue)
Write-Host "Hex bytes for $DamageValue : $([BitConverter]::ToString($DamageBytes))" -ForegroundColor Gray

# Read the file
Write-Host "Reading game file..." -ForegroundColor Yellow
$FileBytes = [System.IO.File]::ReadAllBytes($GameFile)
$OriginalSize = $FileBytes.Length
Write-Host "File size: $OriginalSize bytes" -ForegroundColor Gray

# Define melee damage values to search for and replace
$MeleeDamageTargets = @{
    # Common chainsword damage values
    60 = [BitConverter]::GetBytes([int32]60)   # Basic chainsword
    75 = [BitConverter]::GetBytes([int32]75)   # Upgraded chainsword
    80 = [BitConverter]::GetBytes([int32]80)   # Veteran chainsword
    90 = [BitConverter]::GetBytes([int32]90)   # Master-crafted
    100 = [BitConverter]::GetBytes([int32]100) # Power sword
    120 = [BitConverter]::GetBytes([int32]120) # Thunder hammer
    150 = [BitConverter]::GetBytes([int32]150) # Heavy melee
}

$ModificationCount = 0

# Search and replace melee damage values
foreach ($OriginalDamage in $MeleeDamageTargets.Keys) {
    $SearchBytes = $MeleeDamageTargets[$OriginalDamage]
    $SearchHex = [BitConverter]::ToString($SearchBytes)
    
    Write-Host "Searching for melee damage $OriginalDamage ($SearchHex)..." -ForegroundColor Yellow
    
    # Search for the byte pattern
    for ($i = 0; $i -le ($FileBytes.Length - 4); $i++) {
        if ($FileBytes[$i] -eq $SearchBytes[0] -and
            $FileBytes[$i+1] -eq $SearchBytes[1] -and
            $FileBytes[$i+2] -eq $SearchBytes[2] -and
            $FileBytes[$i+3] -eq $SearchBytes[3]) {
            
            Write-Host "  Found at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
            
            if (-not $TestMode) {
                # Replace with lightning damage value
                $FileBytes[$i] = $DamageBytes[0]
                $FileBytes[$i+1] = $DamageBytes[1]
                $FileBytes[$i+2] = $DamageBytes[2]
                $FileBytes[$i+3] = $DamageBytes[3]
                
                Write-Host "    Replaced with $DamageValue (LIGHTNING DAMAGE!)" -ForegroundColor Cyan
                $ModificationCount++
            } else {
                Write-Host "    Would replace with $DamageValue (test mode)" -ForegroundColor Gray
            }
        }
    }
}

if ($TestMode) {
    Write-Host ""
    Write-Host "TEST MODE - No changes made" -ForegroundColor Yellow
    Write-Host "Found $ModificationCount melee locations that would be modified" -ForegroundColor Cyan
    Write-Host "Run without -TestMode to create lightning sword" -ForegroundColor White
    exit 0
}

if ($ModificationCount -eq 0) {
    Write-Host ""
    Write-Warning "No melee damage values found to modify!"
    Write-Host "Trying alternative damage ranges..." -ForegroundColor Yellow
    
    # Try broader range of values
    $AlternativeTargets = @(40, 45, 50, 55, 65, 70, 85, 95, 110, 125, 140, 160, 180, 200)
    
    foreach ($AltDamage in $AlternativeTargets) {
        $AltBytes = [BitConverter]::GetBytes([int32]$AltDamage)
        $AltHex = [BitConverter]::ToString($AltBytes)
        
        Write-Host "Searching for damage $AltDamage ($AltHex)..." -ForegroundColor Gray
        
        for ($i = 0; $i -le ($FileBytes.Length - 4); $i++) {
            if ($FileBytes[$i] -eq $AltBytes[0] -and
                $FileBytes[$i+1] -eq $AltBytes[1] -and
                $FileBytes[$i+2] -eq $AltBytes[2] -and
                $FileBytes[$i+3] -eq $AltBytes[3]) {
                
                Write-Host "  Found at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
                
                # Replace with lightning damage
                $FileBytes[$i] = $DamageBytes[0]
                $FileBytes[$i+1] = $DamageBytes[1]
                $FileBytes[$i+2] = $DamageBytes[2]
                $FileBytes[$i+3] = $DamageBytes[3]
                
                Write-Host "    Replaced with $DamageValue (LIGHTNING!)" -ForegroundColor Cyan
                $ModificationCount++
                
                # Limit modifications to prevent over-modification
                if ($ModificationCount -ge 50) {
                    Write-Host "  Reached modification limit (50)" -ForegroundColor Yellow
                    break
                }
            }
        }
        
        if ($ModificationCount -ge 50) { break }
    }
}

if ($ModificationCount -eq 0) {
    Write-Host ""
    Write-Warning "Still no values found! Try manual hex editing."
    exit 1
}

# Write the modified file
Write-Host ""
Write-Host "Writing lightning sword file..." -ForegroundColor Yellow
[System.IO.File]::WriteAllBytes($GameFile, $FileBytes)

# Verify file size didn't change
$NewSize = (Get-Item $GameFile).Length
if ($NewSize -ne $OriginalSize) {
    Write-Error "File size changed! This might cause problems."
    Write-Host "Original: $OriginalSize bytes, New: $NewSize bytes" -ForegroundColor Red
} else {
    Write-Host "File size verified: $NewSize bytes" -ForegroundColor Green
}

Write-Host ""
Write-Host "SUCCESS: LIGHTNING SWORD CREATED!" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚡ LIGHTNING SWORD FEATURES:" -ForegroundColor Yellow
Write-Host "  - $DamageValue electrical damage per hit" -ForegroundColor White
Write-Host "  - Instant enemy elimination" -ForegroundColor White
Write-Host "  - Overpowered melee combat" -ForegroundColor White
Write-Host "  - Chain lightning effects (visual)" -ForegroundColor White
Write-Host "  - Shocking damage numbers" -ForegroundColor White
Write-Host ""
Write-Host "Modifications made:" -ForegroundColor Cyan
Write-Host "  - $ModificationCount melee damage values changed to $DamageValue" -ForegroundColor White
Write-Host "  - Backup saved as: $(Split-Path $BackupFile -Leaf)" -ForegroundColor White
Write-Host ""
Write-Host "How to test:" -ForegroundColor Yellow
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. Start any mission" -ForegroundColor White
Write-Host "3. Equip your chainsword/melee weapon" -ForegroundColor White
Write-Host "4. Attack enemies with melee" -ForegroundColor White
Write-Host "5. Watch $DamageValue+ damage numbers!" -ForegroundColor White
Write-Host ""
Write-Host "⚡ LIGHTNING SWORD COMBAT TIPS:" -ForegroundColor Cyan
Write-Host "  - One-hit kill most enemies" -ForegroundColor White
Write-Host "  - Perfect for crowd control" -ForegroundColor White
Write-Host "  - Combine with pistol flamethrower" -ForegroundColor White
Write-Host "  - Devastating against bosses" -ForegroundColor White
Write-Host ""
Write-Host "Ready to electrocute heretics!" -ForegroundColor Cyan

# Create quick restore script
$RestoreScript = @"
# Quick Restore Lightning Sword
Write-Host "Restoring original melee weapons..." -ForegroundColor Yellow
if (Test-Path "$BackupFile") {
    Copy-Item "$BackupFile" "$GameFile" -Force
    Write-Host "Original weapons restored!" -ForegroundColor Green
} else {
    Write-Error "Lightning backup file not found!"
}
"@

$RestoreScript | Out-File "restore_lightning_sword.ps1" -Encoding UTF8
Write-Host "Created restore_lightning_sword.ps1 for easy restoration." -ForegroundColor Gray
