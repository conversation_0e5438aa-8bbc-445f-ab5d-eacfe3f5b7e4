water {
   __caption = "water"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   globalMask = {
      __type = "texture"
      __callback = ""
   }
   
   globalFlowScale {
      __type = float
   }
   
   waterSurface {
      __type = "section"
      tint {
         __type = "color"
      }
      
      tintHeightGradient {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         lowLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         highLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
      }
      
      albedoTex = {
         __type = "texture"
         __callback = ""
      }
      
      roughness {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      metalness {
         __type = "slider"
         __min = 0.000000
         __max = 1.000000
         __step = 0.010000
      }
      
      metalnessTint {
         __type = "color"
      }
      
      metalnessColorFromAlbedo {
         __type = "bool"
      }
      
      roughnessHeightParams {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         scaleLow {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         biasLow {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         scaleHigh {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         biasHigh {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         lowLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         highLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
      }
      
      metalnessHeightParams {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         scaleLow {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         biasLow {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         scaleHigh {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         biasHigh {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         lowLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         highLevel {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
      }
      
      specularTexture {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = "spec"
         }
         
         tiling {
            __type = float
            __min = 0.000000
         }
         
         roughnessScale {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         roughnessBias {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         metalnessScale {
            __type = "slider"
            __min = 0.000000
            __max = 2.000000
            __step = 0.010000
         }
         
         metalnessBias {
            __type = "slider"
            __min = -1.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         first {
            __type = "section"
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         second {
            __type = "section"
            weight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
      }
      
      thinFilm {
         __type = "section"
         amount {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         thicknessMin {
            __type = "slider"
            __min = 10.000000
            __max = 1000.000000
            __step = 1.000000
         }
         
         thicknessMax {
            __type = "slider"
            __min = 10.000000
            __max = 1000.000000
            __step = 1.000000
         }
         
         refractiveIndex {
            __type = "slider"
            __min = 1.000000
            __max = 5.000000
            __step = 0.010000
         }
         
         thicknessTexture = {
            __type = "texture"
            __callback = "hdetm"
         }
         
         first {
            __type = "section"
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         second {
            __type = "section"
            weight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
      }
      
      emissive {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         intensity {
            __type = float
         }
         
         tint {
            __type = "color"
         }
         
         staticLightIntFactor {
            __type = "slider"
            __min = 0.000000
            __max = 1.000000
            __step = 0.010000
         }
         
         disturbersGradientTint {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = ""
            }
            
            lowLevel {
               __type = "slider"
               __min = 0.000000
               __max = 1.000000
               __step = 0.010000
            }
            
            highLevel {
               __type = "slider"
               __min = 0.000000
               __max = 1.000000
               __step = 0.010000
            }
            
         }
         
         heightGradientTint {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = ""
            }
            
            lowLevel {
               __type = "slider"
               __min = -1.000000
               __max = 1.000000
               __step = 0.010000
            }
            
            highLevel {
               __type = "slider"
               __min = -1.000000
               __max = 1.000000
               __step = 0.010000
            }
            
         }
         
         firstOctave {
            __type = "section"
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         secondOctave {
            __type = "section"
            weight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
      }
      
   }
   
   volumeProperties {
      __type = "section"
      refractionFilter {
         __type = "section"
         color {
            __type = "color"
         }
         
         density {
            __type = float
            __min = 0.000000
            __max = 100.000000
         }
         
      }
      
      fog {
         __type = "section"
         color {
            __type = "color"
         }
         
         intensity {
            __type = float
            __min = 0.000000
         }
         
         density {
            __type = float
            __min = 0.000000
            __max = 100.000000
         }
         
      }
      
      blurriness {
         __type = float
         __min = 0.000000
         __max = 100.000000
      }
      
   }
   
   foam {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      tex = {
         __type = "texture"
         __callback = ""
      }
      
      noiseTex = {
         __type = "texture"
         __callback = ""
      }
      
      noiseIntensity {
         __type = float
      }
      
      noiseTextureScale {
         __type = float
      }
      
      underwaterFoam {
         __type = "section"
         color {
            __type = "color"
         }
         
         opacity {
            __type = float
         }
         
         velocity {
            __type = float
         }
         
         intensityBias {
            __type = float
         }
         
      }
      
      overwaterFoam {
         __type = "section"
         color {
            __type = "color"
         }
         
         opacity {
            __type = float
         }
         
         intensity {
            __type = float
         }
         
         spread {
            __type = float
         }
         
         foamHardMaxAddedValue {
            __type = float
         }
         
         foamHardStartThreshold {
            __type = float
         }
         
         foamHardMaxThreshold {
            __type = float
         }
         
         velocity {
            __type = float
         }
         
      }
      
      normalBlend {
         __type = float
      }
      
      shallowOffset {
         __type = float
      }
      
      shallowScale {
         __type = float
      }
      
      textureScale {
         __type = float
         __min = 0.000000
      }
      
      normalScale {
         __type = float
      }
      
      roughness {
         __type = float
      }
      
      wavesPeakThreshold {
         __type = float
      }
      
      wavesPeakIntensity {
         __type = float
      }
      
      wavesPeakContrast {
         __type = float
      }
      
      textureScrollFlowScale {
         __type = float
      }
      
      textureScrollTimeScale {
         __type = float
      }
      
      simulation {
         __type = "section"
         simulationTexThreshold {
            __type = float
         }
         
         simulationTexIntensity {
            __type = float
         }
         
         simulationTexContrast {
            __type = float
         }
         
         heightThreshold {
            __type = float
         }
         
         heightIntensity {
            __type = float
         }
         
         heightContrast {
            __type = float
         }
         
         velocityThreshold {
            __type = float
         }
         
         velocityIntensity {
            __type = float
         }
         
         velocityContrast {
            __type = float
         }
         
      }
      
   }
   
   mud {
      __type = "section"
      color {
         __type = "color"
      }
      
      foamColor {
         __type = "color"
      }
      
      underwaterDensity {
         __type = float
      }
      
      compactness {
         __type = float
      }
      
   }
   
   caustic {
      __type = "section"
      tex = {
         __type = "texture"
         __callback = ""
      }
      
      animation {
         __type = "section"
         disturbance {
            __type = "section"
            tiling {
               __type = float
            }
            
            timeScale {
               __type = float
            }
            
         }
         
         octave {
            __type = "section"
            tiling {
               __type = float
            }
            
            disturbanceScale {
               __type = float
            }
            
            timeScale {
               __type = float
            }
            
         }
         
         contrast {
            __type = float
         }
         
      }
      
      simScale {
         __type = float
      }
      
      simFlowScale {
         __type = float
      }
      
      underwater {
         __type = "section"
         enable {
            __type = "bool"
         }
         
         intensity {
            __type = float
         }
         
         depth {
            __type = float
         }
         
      }
      
   }
   
   refraction {
      __type = "section"
      depthCorrection {
         __type = "section"
         enabled {
            __type = "bool"
         }
         
         shallowWaterDecay {
            __type = float
         }
         
         maxDepthDelta {
            __type = float
         }
         
      }
      
      scrDistortScale {
         __type = float
      }
      
   }
   
   softZ {
      __type = float
      __min = 0.000000
      __max = 100.000000
   }
   
   disableSSR {
      __type = "bool"
   }
   
   slimeShading {
      __type = "bool"
   }
   
   reflectance {
      __type = float
   }
   
   scattering {
      __type = "section"
      tint {
         __type = "color"
      }
      
      ambient {
         __type = float
      }
      
      throughWavesContrast {
         __type = float
      }
      
      foamIntensity {
         __type = float
      }
      
   }
   
   waves {
      __type = "section"
      texWaves = {
         __type = "texture"
         __callback = ""
      }
      
      texWavesBias {
         __type = float
      }
      
      texWaveNMScale {
         __type = float
      }
      
      tiling {
         __type = "section"
         u {
            __type = float
            __min = 0.000000
         }
         
         v {
            __type = float
            __min = 0.000000
         }
         
      }
      
      speedScale {
         __type = float
      }
      
      speedAngle {
         __type = float
      }
      
      wavesMaxDistance {
         __type = float
         __min = 0.000000
         __max = 200000.000000
      }
      
      bumpiness {
         __type = float
         __min = 0.000000
      }
      
      waveAmplitude {
         __type = float
      }
      
      octaves {
         __type = "section"
         one {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         two {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         three {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         four {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         five {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
         six {
            __type = "section"
            farDistance {
               __type = float
               __min = 0.000000
               __max = 200000.000000
            }
            
            nearFarBlendRange {
               __type = float
               __min = 0.001000
               __max = 200000.000000
            }
            
            weight {
               __type = float
            }
            
            farWeight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
         }
         
      }
      
      wind {
         __type = "section"
         texMaxWeight {
            __type = float
         }
         
         staticWeight {
            __type = float
         }
         
         dynamicWeight {
            __type = float
         }
         
         maxSpeed {
            __type = float
         }
         
      }
      
      distortWaves {
         __type = "section"
         layer0 {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = "nm"
            }
            
            weight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
            microdetailWeight {
               __type = float
            }
            
         }
         
         layer1 {
            __type = "section"
            tex = {
               __type = "texture"
               __callback = "nm"
            }
            
            weight {
               __type = float
            }
            
            tilingScale {
               __type = float
            }
            
            tilingAngle {
               __type = float
            }
            
            speedScale {
               __type = float
            }
            
            speedAngle {
               __type = float
            }
            
            microdetailWeight {
               __type = float
            }
            
         }
         
      }
      
      disturbanceWaves {
         __type = "section"
         scale {
            __type = float
         }
         
         flowScale {
            __type = float
         }
         
         heightScale {
            __type = float
         }
         
      }
      
      seaLevelBias {
         __type = float
      }
      
      displacementMapping {
         __type = "section"
         simTessFactor {
            __type = float
         }
         
         distTessFactor {
            __type = float
         }
         
         simTessFactorFadeDist {
            __type = float
         }
         
         distTessFactorFadeDist {
            __type = float
         }
         
         choppyScale {
            __type = float
            __min = 0.000000
         }
         
         microdetail {
            __type = "section"
            first {
               __type = "section"
               weight {
                  __type = float
               }
               
               tilingScale {
                  __type = float
               }
               
               tilingAngle {
                  __type = float
               }
               
               speedScale {
                  __type = float
               }
               
               speedAngle {
                  __type = float
               }
               
            }
            
            second {
               __type = "section"
               weight {
                  __type = float
               }
               
               tilingScale {
                  __type = float
               }
               
               tilingAngle {
                  __type = float
               }
               
               speedScale {
                  __type = float
               }
               
               speedAngle {
                  __type = float
               }
               
            }
            
         }
         
      }
      
      interactiveRipples {
         __type = "section"
         simulation {
            __type = "enum"
            __list = ["off", "water", "slime"]
         }
         
         heightAboveGround {
            __type = float
         }
         
      }
      
   }
   
   slimeWaves {
      __type = "section"
      mixAndSumBlendFactor {
         __type = float
         __min = 0.000000
         __max = 1.000000
      }
      
      offset {
         __type = float
      }
      
      displNormalHeightScale {
         __type = float
      }
      
      attraction {
         __type = float
      }
      
      useMathInsteadOfTextures {
         __type = "bool"
      }
      
      flowLayer {
         __type = "section"
         texWave = {
            __type = "texture"
            __callback = ""
         }
         
         texWaveNMScale {
            __type = float
         }
         
         weight {
            __type = float
         }
         
         tilingScale {
            __type = float
         }
         
         tilingAngle {
            __type = float
         }
         
         flowSpeed {
            __type = float
         }
         
      }
      
      additiveLayer0 {
         __type = "section"
         texWave = {
            __type = "texture"
            __callback = ""
         }
         
         texWaveNMScale {
            __type = float
         }
         
         weight {
            __type = float
         }
         
         tilingScale {
            __type = float
         }
         
         tilingAngle {
            __type = float
         }
         
         speedScale {
            __type = float
         }
         
         speedAngle {
            __type = float
         }
         
      }
      
      additiveLayer1 {
         __type = "section"
         texWave = {
            __type = "texture"
            __callback = ""
         }
         
         texWaveNMScale {
            __type = float
         }
         
         weight {
            __type = float
         }
         
         tilingScale {
            __type = float
         }
         
         tilingAngle {
            __type = float
         }
         
         speedScale {
            __type = float
         }
         
         speedAngle {
            __type = float
         }
         
      }
      
      multiplyLayer0 {
         __type = "section"
         texMask = {
            __type = "texture"
            __callback = ""
         }
         
         weight {
            __type = float
         }
         
         tilingScale {
            __type = float
         }
         
         tilingAngle {
            __type = float
         }
         
         speedScale {
            __type = float
         }
         
         speedAngle {
            __type = float
         }
         
      }
      
      multiplyLayer1 {
         __type = "section"
         texMask = {
            __type = "texture"
            __callback = ""
         }
         
         weight {
            __type = float
         }
         
         tilingScale {
            __type = float
         }
         
         tilingAngle {
            __type = float
         }
         
         speedScale {
            __type = float
         }
         
         speedAngle {
            __type = float
         }
         
      }
      
   }
   
   isUseHeightBoundTex {
      __type = "bool"
   }
   
   useDecals {
      __type = "bool"
   }
   
   newSlime {
      __type = "bool"
   }
   
   useSimulation {
      __type = "bool"
   }
   
   visualizationState {
      __type = "int"
   }
   
   dbgVisualizeMask = {
      __type = "texture"
      __callback = ""
   }
   
   sunLightDirection {
      __type = "float4"
   }
   
}