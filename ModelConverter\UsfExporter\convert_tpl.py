import os
import sys
import shutil
from dataclasses import dataclass
import subprocess
import yaml

CWD = os.path.dirname(os.path.realpath(__file__))

PROJECT_DIR = os.path.join(CWD, 'project')
RESULT_DIR = os.path.join(PROJECT_DIR, 'models')
TMP_TPL_DIR = os.path.join(PROJECT_DIR, 'tpl')
DEFAULT_OPTIONS = os.path.join(PROJECT_DIR, 'default_options.ps')

BINARIES_DIR = os.path.join(PROJECT_DIR, 'bin')
USF_EXPORT_EXE = os.path.join(BINARIES_DIR, 'UsfExport.exe')
CDT_CNV_EXE = os.path.join(BINARIES_DIR, 'CDTConverterExe.exe')

RESOURCES_DIR = os.path.join(PROJECT_DIR, 'resources', 'tpl')


def _execute_subprocess(cmd_args):
    startupinfo = subprocess.STARTUPINFO()
    # this bit is needed if process creates window
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE

    print(' '.join(cmd_args))

    cmd_process = subprocess.Popen(
        cmd_args,
        startupinfo=startupinfo,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd=PROJECT_DIR)
        
    stdout, stderr = cmd_process.communicate()
    print(stdout.decode())
    if stderr:
        print(stderr.decode())
    
    return cmd_process.wait()


@dataclass(frozen=True)
class TplExportContext:
    dst_dir: str
    obj_name: str
    src_file: str


def usf_export_tpl(ctx: TplExportContext):
    cmd = [
        USF_EXPORT_EXE,
        '-e' + ctx.dst_dir,
        '-n' + PROJECT_DIR,
        '-a',
        '-u',
        '-o' + DEFAULT_OPTIONS,
        ctx.src_file,
    ]

    return _execute_subprocess(cmd)



def cdt_converter_tpl(ctx: TplExportContext):
    source_file = ctx.obj_name + '.tpl'
    source_data_file = source_file + '_data'
    geom_dbg_file = os.path.splitext(source_file)[0] + '.geom_dbg'

    cmd = [
        CDT_CNV_EXE,
        '-p', PROJECT_DIR,
        '-o', ctx.dst_dir,
        '--silent',
        '--source', os.path.join(ctx.dst_dir, source_file),
        '--geom-dbg', os.path.join(ctx.dst_dir, geom_dbg_file),
    ]
    source_data = os.path.join(ctx.dst_dir, source_data_file)
    if os.path.exists(source_data):
        cmd.extend(('--source-data', source_data))

    return _execute_subprocess(cmd)


def create_tpl_resource(ctx: TplExportContext):
    patch_local_path = lambda p: p.replace('..\\', '')

    usf_output = None
    cdt_output =  None
    with open(os.path.join(ctx.dst_dir, 'intermediate', '.usf_export_tpl_output'), 'r') as f:
        usf_output = yaml.load(f, Loader=yaml.FullLoader)
    with open(os.path.join(ctx.dst_dir, 'intermediate', '.cdt_conv_output'), 'r') as f:
        cdt_output = yaml.load(f, Loader=yaml.FullLoader)
    
    resource = usf_output
    resource['tpl'] = patch_local_path(resource['tpl'])
    resource['tplData'] = patch_local_path(resource['tplData'])
    resource['geomDbg'] = patch_local_path(resource['geomDbg'])
    resource['tplLodsBase'] = patch_local_path(resource['tplLodsBase'])
    resource['tplMarkup'] = patch_local_path(resource['tplMarkup'])
    resource['cdt'] = patch_local_path(cdt_output['cdt'])
    resource['__type'] = 'res_desc_tpl'

    with open(ctx.obj_name + '.tpl.resource', 'w') as f:
        yaml.dump(resource, f)


def move_to_resources(ctx: TplExportContext):
    dir_name = os.path.basename(ctx.dst_dir)
    shutil.rmtree(os.path.join(RESOURCES_DIR, dir_name), ignore_errors=True)

    os.makedirs(RESOURCES_DIR, exist_ok=True)
    shutil.move(ctx.dst_dir, RESOURCES_DIR)

    shutil.rmtree(RESULT_DIR, ignore_errors=True)
    shutil.rmtree(TMP_TPL_DIR, ignore_errors=True)


def convert_main(src_file):
    filename, ext = os.path.splitext(os.path.basename(src_file))
    filename = "refrt_" + filename if ext == '.s3dref' else filename
    subpath = "ref_realtime" if ext == '.s3dref' else "database"
    dst_dir = os.path.join(RESULT_DIR, subpath, filename + '.tpl')
    obj_name = os.path.join(dst_dir, filename)

    ctx = TplExportContext(src_file=src_file, dst_dir=dst_dir, obj_name=obj_name)

    ret_code = usf_export_tpl(ctx)
    if ret_code != 0:
        print('Failed to export USF')
        return ret_code
    
    ret_code = cdt_converter_tpl(ctx)
    if ret_code != 0:
        print('Failed to convert CDT')
        return ret_code
    
    create_tpl_resource(ctx)
    move_to_resources(ctx)
    return


def test_main():
    convert_main(
        src_file=os.path.join(CWD, 'examples\\models\\pawn_cadian.s3dusf')
    )
    convert_main(
        src_file=os.path.join(CWD, 'examples\\models\\rock_desert_04.s3dref')
    )


if __name__ == '__main__':
    result = convert_main(sys.argv[1])
    #result = test_main()
    sys.exit(result)
