// Space Marine 2 - Flamethrower Weapon Spawning Integration
// This file adds the flamethrower to weapon spawn systems

// Add to Equipment Library for loadouts
EquipmentLibrary = {
    presets = {
        // Heavy weapon loadout with flamethrower
        heavy_flamethrower = {
            uid = "heavy_flamethrower"
            name = "Heavy Flamethrower Loadout"
            category = "heavy"
            
            weapons = {
                primary = {
                    weaponUid = "flamethrower_heavy"
                    ammo = 200
                    modifications = []
                }
                
                secondary = {
                    weaponUid = "bolt_pistol"
                    ammo = 60
                    modifications = []
                }
                
                melee = {
                    weaponUid = "chainsword"
                    modifications = []
                }
                
                throwable = {
                    weaponUid = "frag_grenade"
                    count = 2
                }
            }
            
            equipment = {
                armor = "heavy_armor"
                helmet = "heavy_helmet"
                backpack = "fuel_tank"
            }
            
            perks = [
                "heavy_weapon_specialist",
                "fire_resistance",
                "area_damage_boost"
            ]
            
            __type = "LoadoutPreset"
        }
    }
    
    __type = "EquipmentLibrary"
}

// Add to Weapon Creator Server for spawning
FirearmCreatorServer = {
    firearms = {
        flamethrower_heavy = {
            uid = "flamethrower_heavy"
            spawnWeight = 1.0           // Equal chance to spawn as other heavy weapons
            spawnLocations = [
                "heavy_weapon_spawn",
                "special_weapon_spawn",
                "armory_spawn"
            ]
            
            // Spawn conditions
            spawnConditions = {
                minPlayerLevel = 15     // Only spawn for level 15+ players
                gameMode = [
                    "operations",
                    "eternal_war",
                    "private_match"
                ]
                excludeOnline = true    // Don't spawn in online matchmaking
            }
            
            // Spawn configuration
            spawnConfig = {
                ammoCount = 200         // Full fuel tank when spawned
                condition = 1.0         // Perfect condition
                modifications = []      // No modifications by default
                
                // Visual spawn effects
                spawnEffect = "sfx_weapon_spawn_heavy"
                spawnSound = "sfx_flamethrower_ready"
            }
            
            __type = "WeaponSpawnConfig"
        }
    }
    
    __type = "FirearmCreatorServer"
}

// Add to Weapon Mastery Progression
WeaponMasteryProgressionLibrary = {
    weapons = {
        flamethrower_heavy = {
            uid = "flamethrower_heavy"
            name = "Heavy Flamethrower"
            category = "heavy"
            
            // XP requirements for each tier
            tiers = {
                tier1 = {
                    xpRequired = 500
                    perkLayers = {
                        layer1 = {
                            perkUid = "flamethrower_range_boost"
                        }
                    }
                }
                
                tier2 = {
                    xpRequired = 1200
                    perkLayers = {
                        layer1 = {
                            perkUid = "flamethrower_fuel_efficiency"
                        }
                    }
                }
                
                tier3 = {
                    xpRequired = 2500
                    perkLayers = {
                        layer1 = {
                            perkUid = "flamethrower_enhanced_burning"
                        }
                    }
                }
                
                tier4 = {
                    xpRequired = 5000
                    perkLayers = {
                        layer1 = {
                            perkUid = "flamethrower_rapid_cooling"
                        }
                    }
                }
                
                tier5 = {
                    xpRequired = 10000
                    perkLayers = {
                        layer1 = {
                            perkUid = "flamethrower_inferno_blast"
                        }
                    }
                }
            }
            
            __type = "WeaponProgression"
        }
    }
    
    __type = "WeaponMasteryProgressionLibrary"
}

// Add to UI Firearms Library for display
UiFirearmsLibrary = {
    uiDataList = {
        flamethrower_heavy = {
            uid = "flamethrower_heavy"
            name = "Heavy Flamethrower"
            description = "Purge the heretic with righteous flame. This heavy flamethrower delivers continuous fire damage with area-of-effect burning."
            
            // UI display data
            ui = {
                icon = "ui_weapon_flamethrower"
                category = "Heavy"
                rarity = "common"
                
                // Stats display
                stats = {
                    damage = 9          // Out of 10
                    range = 4           // Out of 10
                    accuracy = 8        // Out of 10
                    mobility = 3        // Out of 10
                    ammo = 7           // Out of 10
                }
                
                // Weapon tags
                tags = [
                    "area_damage",
                    "continuous_fire",
                    "status_effects",
                    "environmental"
                ]
            }
            
            // Unlock requirements
            unlock = {
                level = 15
                challenges = []
                cost = 0
            }
            
            __type = "UiWeaponData"
        }
    }
    
    __type = "UiFirearmsLibrary"
}
