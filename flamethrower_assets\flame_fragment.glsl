
        #version 330 core
        
        in vec2 TexCoord;
        in vec3 Normal;
        in vec3 FragPos;
        in float Time;
        
        uniform sampler2D flameTexture;
        uniform sampler2D distortionTexture;
        uniform vec3 viewPos;
        
        out vec4 FragColor;
        
        void main() {
            // Animated distortion
            vec2 distortion = texture(distortionTexture, TexCoord + Time * 0.1).rg * 0.1;
            vec2 animatedTexCoord = TexCoord + distortion;
            animatedTexCoord.y += Time * 0.3;
            
            // Sample flame texture
            vec4 flameColor = texture(flameTexture, animatedTexCoord);
            
            // Add flame gradient
            float gradient = 1.0 - TexCoord.y;
            flameColor.rgb *= gradient;
            
            // Add flickering
            float flicker = sin(Time * 10.0) * 0.1 + 0.9;
            flameColor.rgb *= flicker;
            
            // Add heat distortion effect
            float heat = gradient * 0.5;
            flameColor.a *= heat;
            
            FragColor = flameColor;
        }
        