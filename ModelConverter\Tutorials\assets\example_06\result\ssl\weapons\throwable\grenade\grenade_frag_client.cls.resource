__type: res_desc_cls
clientDynamicLinks: []
cls: grenade_frag_client.cls
dynamicLinks: []
links: []
linksDedicated: []
linksDesc:
- res://ssl/end_effect/ee_gren_frag.ee.resource
- res://ssl/scorchmarks/scr_grenade_sand.scr.resource
- res://ssl/scorchmarks/scr_molotov.scr.resource
- res://ssl/sfx/classes/sfx_mg_frag_grenade_expl.cls.resource
- res://ssl/sfx/classes/sfx_mg_frag_grenade_expl_sand.cls.resource
linksDescDedicated: []
linksDescVisual: []
linksDescWeak: []
linksFgaMeta: []
linksFont: []
linksMr: []
linksPct: []
linksPreset: []
linksSound:
- res://sounds/events/phys_grenade_collision.sound_event.resource
- res://sounds/events/wpn_expl_grnd.sound_event.resource
linksSsl:
- res://ssl/weapons/throwable/grenade/grenade_frag_client.ssc.resource
linksSslDedicated: []
linksSslVisual: []
linksTpl: 
- res://tpl/wpn_fumo_cirno.tpl/wpn_fumo_cirno.tpl.resource
linksVideo: []
linksVisual: []
parents:
- res://ssl/weapons/throwable/simple_grenade_base/simple_grenade_base_client.cls.resource
serverDynamicLinks: []
tags: []
typemask:
- client
