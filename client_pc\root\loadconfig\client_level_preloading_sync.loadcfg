steps:
  - RES3_SCENE_SSL
  - RES3_SCENE_SSL_DESC

functions:
  - name: finalize_ssl_loading
    on:
      - step: RES3_SCENE_SSL

rules:
  #
  # ssl descs
  #
  - name: process scene ssl
    res_type: res_desc_ssl
    step: RES3_SCENE_SSL

  - name: process scene ssolib
    res_type: res_desc_ssolib
    step: RES3_SCENE_SSL_DESC

  - name: process scene sso
    res_type: res_desc_sso
    step: RES3_SCENE_SSL_DESC

  - name: process scene cls
    res_type: res_desc_cls
    step: RES3_SCENE_SSL_DESC

  - name: process scene prop
    res_type: res_desc_prop
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab
    res_type: res_prefab_desc
    step: RES3_SCENE_SSL_DESC

  - name: process scene prefab asset
    res_type: res_desc_prefab_asset
    step: RES3_SCENE_SSL_DESC

  #
  # basic descs
  #
  - name: process scene basic descs
    res_type: res_basic_ssl_desc
    step: RES3_SCENE_SSL_DESC

  #
  # other
  #
  - name: disable all other RES3_SCENE resources
    use_default_loader: true
    exclude: true
