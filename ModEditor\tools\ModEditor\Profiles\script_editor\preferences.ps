EngineConnector   =   {
   SwitchToDebugLayoutOnConnect   =   False
   AutoConnection   =   True
   DefaultChannel   =   -1
   AutoStartConsoleTre   =   Host
}
SSL   =   {
   AutoOpenRootSsl   =   False
   BreakOnRuntimeError   =   False
   SslVariableGeneratorType   =   "Dummy"
}
ProjectBrowser   =   {
   FindOptions   =   {
      FindInProperties   =   {
         SearchInTextMode   =   True
      }
      FastSearch   =   True
   }
   ViewOptions   =   {
      ShowFoldersPanel   =   True
      Layout   =   "OnePanel"
      ThumbnailsBackgroundColor   =   [
         0,
         255,
         0,
         0
      ]
   }
   FileOperations   =   {
   }
   TrackActiveDocument   =   False
}
LogViewer   =   {
   "Clear On Connect"   =   True
}
Diagramming   =   {
   "Show property control by default"   =   False
   SnapToGrid   =   True
}
HotKeys   =   {
   Standard_Close   =   {
      Command   =   "Standard.Close"
      Key   =   "W"
      Modifiers   =   "Control"
   }
   Editing_Comment   =   {
      Command   =   "Editing.Comment"
      Key   =   "Add"
      Modifiers   =   "Control"
   }
   Editing_Uncomment   =   {
      Command   =   "Editing.Uncomment"
      Key   =   "Subtract"
      Modifiers   =   "Control"
   }
   Editing_ExpandFolds   =   {
      Command   =   "Editing.ExpandFolds"
      Key   =   "Subtract"
      Modifiers   =   "Alt"
   }
   Editing_CollapseFolds   =   {
      Command   =   "Editing.CollapseFolds"
      Key   =   "Add"
      Modifiers   =   "Alt"
   }
   "SSL_Add Watch"   =   {
      Command   =   "SSL.Add Watch"
      Key   =   "Q"
      Modifiers   =   "Alt"
   }
   Standard_CloseAllDocuments   =   {
      Command   =   "Standard.CloseAllDocuments"
      Key   =   "W"
      Modifiers   =   "Control, Shift"
   }
   "SSL_Insert Snippet"   =   {
      Command   =   "SSL.Insert Snippet"
      Key   =   "W"
      Modifiers   =   "Alt"
   }
   "SSL_Add Import"   =   {
      Command   =   "SSL.Add Import"
      Key   =   "E"
      Modifiers   =   "Alt"
   }
   Standard_OpenPreferences   =   {
      Command   =   "Standard.OpenPreferences"
      Key   =   "F12"
      Modifiers   =   "None"
   }
   View_OpenPreferences   =   {
      Command   =   "View.OpenPreferences"
      Key   =   "F12"
      Modifiers   =   "None"
   }
   SSL_CopyDocumentModuleName   =   {
      Command   =   "SSL.CopyDocumentModuleName"
      Key   =   "I"
      Modifiers   =   "Alt, Shift"
   }
   "SSL To Class_CopyFullSscPath"   =   {
      Command   =   "SSL To Class.CopyFullSscPath"
      Key   =   "C"
      Modifiers   =   "Alt, Shift"
   }
   "SSL To Class_CopyDocFullSscPath"   =   {
      Command   =   "SSL To Class.CopyDocFullSscPath"
      Key   =   "C"
      Modifiers   =   "Alt, Shift"
   }
   "TextEditor_Insert Snippet"   =   {
      Command   =   "TextEditor.Insert Snippet"
      Key   =   "W"
      Modifiers   =   "Alt"
   }
   "Project Browser_CopyFullPath"   =   {
      Command   =   "Project Browser.CopyFullPath"
      Key   =   "C"
      Modifiers   =   "Alt, Shift"
   }
   Standard_CopyFullPath   =   {
      Command   =   "Standard.CopyFullPath"
      Key   =   "C"
      Modifiers   =   "Alt, Shift"
   }
   SSL_CopyFileModuleName   =   {
      Command   =   "SSL.CopyFileModuleName"
      Key   =   "I"
      Modifiers   =   "Alt, Shift"
   }
   TextEditor_ExpandFolds   =   {
      Command   =   "TextEditor.ExpandFolds"
      Key   =   "Subtract"
      Modifiers   =   "Alt"
   }
   TextEditor_CollapseFolds   =   {
      Command   =   "TextEditor.CollapseFolds"
      Key   =   "Add"
      Modifiers   =   "Alt"
   }
   TextEditor_Comment   =   {
      Command   =   "TextEditor.Comment"
      Key   =   "Add"
      Modifiers   =   "Control"
   }
   TextEditor_Uncomment   =   {
      Command   =   "TextEditor.Uncomment"
      Key   =   "Subtract"
      Modifiers   =   "Control"
   }
   Xgine_Play   =   {
      Command   =   "Xgine.Play"
      Key   =   "P"
      Modifiers   =   "Alt"
   }
   Xgine_TogglePause   =   {
      Command   =   "Xgine.TogglePause"
      Key   =   "Q"
      Modifiers   =   "Control"
   }
   "Xgine_Open Instector"   =   {
      Command   =   "Xgine.Open Instector"
      Key   =   "F4"
      Modifiers   =   "None"
   }
   "Xgine_Open Outline"   =   {
      Command   =   "Xgine.Open Outline"
      Key   =   "F3"
      Modifiers   =   "None"
   }
}
Xgine   =   {
   Snapping   =   {
      SnapScaleSize   =   0.05
      SnapScaleEnable   =   False
      SnapLocationEnable   =   False
      SnapLocationSize   =   0.05
      SnapRotationEnable   =   False
   }
   Brushes   =   {
      PreviewSize   =   78
      AutoCheckout   =   True
   }
   UseLessCpuWhenInBackground   =   False
}
EnginePreview   =   {
   EnableThumbnailsGeneration   =   True
   EngineGraphicsAPI   =   "Direct3D12"
	 PreviewScene   =   ".\\client_pc\\root\\mods_source\\xscenes\\ge_viewer_xgine\\ge_viewer_xgine.scn"
	 ThumbnailsScene   =   ".\\client_pc\\root\\mods_source\\xscenes\\ge_viewer_xgine\\ge_viewer_xgine.scn"
}
Class   =   {
   ExtendableSplitterDistance   =   55.28935
}
Environment   =   {
	AutoSaveInterval   =   5
}
