trail {
   __caption = "trail"
   __type = "section"
   __erase = true

   _presets_ {
      __isDefault = true
      //data will be loaded from disk
   }

   preset {
      __type = "string"
      __disable = true
      __canCopyPaste = false
   }

   rgbSrc {
      __type = "enum"
      __list = ["tex", "tex_mul_vert"]
   }
   
   alphaSrc {
      __type = "enum"
      __list = ["tex", "tex_mul_vert", "tex_add_vert"]
   }
   
   blendMode {
      __type = "enum"
      __list = ["blend", "add"]
   }
   
   writeToTransparencyMask {
      __type = "bool"
   }
   
   writeToReactiveMask {
      __type = "bool"
   }
   
   sortingTrails {
      __type = "bool"
   }
   
   applyNoiseDithering {
      __type = "bool"
   }
   
   emissive {
      __type = "section"
      bloomIntensity {
         __type = float
      }
      
      alphaMult {
         __type = float
      }
      
      fresnel {
         __type = float
      }
      
      colorIntensityMultiplier {
         __type = float
      }
      
      lightingMultiplier {
         __type = float
      }
      
      softDepth {
         __type = float
      }
      
   }
   
   instancing {
      __type = "section"
      nVertPerInstance {
         __type = "int"
      }
      
   }
   
   transform {
      __type = "section"
      offset {
         __type = "float4"
      }
      
      speed {
         __type = "float4"
      }
      
      scale {
         __type = "float4"
      }
      
      rotation {
         __type = float
      }
      
      rotationSpeed {
         __type = float
      }
      
   }
   
   localOffset {
      __type = float
   }
   
   forcedDepth {
      __type = float
   }
   
   disableZTest {
      __type = "bool"
   }
   
   degNear {
      __type = "section"
      radStart {
         __type = float
      }
      
      radEnd {
         __type = float
      }
      
      scaleSize {
         __type = float
      }
      
      alpha {
         __type = float
      }
      
   }
   
   degFar {
      __type = "section"
      radStart {
         __type = float
      }
      
      radEnd {
         __type = float
      }
      
      scaleSize {
         __type = float
      }
      
      alpha {
         __type = float
      }
      
   }
   
   colorFadeoff {
      __type = "section"
      degradationRadius_2 {
         __type = float
      }
      
   }
   
   tint {
      __type = "color"
   }
   
   sdr {
      __type = "section"
      transform {
         __type = "section"
         offset {
            __type = "float4"
         }
         
         speed {
            __type = "float4"
         }
         
         scale {
            __type = "float4"
         }
         
         rotation {
            __type = float
         }
         
         rotationSpeed {
            __type = float
         }
         
      }
      
      tex = {
         __type = "texture"
         __callback = ""
      }
      
   }
   
   displacement {
      __type = "section"
      formationTime {
         __type = float
      }
      
   }
   
   lighting {
      __type = "enum"
      __list = ["none", "type_vertex", "type_pixel"]
   }
   
   zTest {
      __type = "bool"
   }
   
   shaderType {
      __type = "enum"
      __list = ["base", "sfx", "distort", "alpha_kill", "gbuffer", "deferred"]
   }
   
   isThinTrail {
      __type = "bool"
   }
   
   isTrailStatic {
      __type = "bool"
   }
   
   isTrailLightning {
      __type = "bool"
   }
   
   geometryType {
      __type = "enum"
      __list = ["tape", "flat", "volumetric", "billboards", "triangle", "projection"]
   }
   
   trailTapeNormal {
      __type = "float4"
   }
   
   trailProjParams {
      __type = "section"
      height {
         __type = float
      }
      
      yOffset {
         __type = float
      }
      
      parallaxScale {
         __type = float
      }
      
      normalMapScale {
         __type = float
      }
      
      useOrientation {
         __type = "bool"
      }
      
      placeOnDynamic {
         __type = "bool"
      }
      
      zFadeScale {
         __type = float
      }
      
      angleFadeScale {
         __type = float
      }
      
      angleFadeBias {
         __type = float
      }
      
      blendMaskScale {
         __type = "float2"
      }
      
      blendMaskBias {
         __type = "float2"
      }
      
   }
   
   tex = {
      __type = "texture"
      __callback = ""
   }
   
   normal = {
      __type = "texture"
      __callback = ""
   }
   
   height = {
      __type = "texture"
      __callback = ""
   }
   
   backbuf = {
      __type = "texture"
      __callback = ""
   }
   
   alphaMask {
      __type = "section"
      tex = {
         __type = "texture"
         __callback = ""
      }
      
      scroll {
         __type = "float4"
      }
      
      scale {
         __type = "float4"
      }
      
      generateSoftBorderNormal {
         __type = "bool"
      }
      
      softBorderNormalMultiplier {
         __type = float
      }
      
      dynamicMask {
         __type = "section"
         tex = {
            __type = "texture"
            __callback = ""
         }
         
         maskMin {
            __type = float
         }
         
         maskMax {
            __type = float
         }
         
         softBorder {
            __type = float
         }
         
      }
      
   }
   
   flatLighting {
      __type = "section"
      enabled {
         __type = "bool"
      }
      
      roughness {
         __type = float
      }
      
      metalness {
         __type = float
      }
      
      transparency {
         __type = float
      }
      
      reflectionIntencity {
         __type = float
      }
      
      specColor {
         __type = "color"
      }
      
      blurReflection {
         __type = float
      }
      
      refractionFilterColor {
         __type = "color"
      }
      
      refractionFilterDensity {
         __type = float
      }
      
      backgroundDistort {
         __type = float
      }
      
   }
   
   temporalAA {
      __type = "section"
      transparencyMaskScale {
         __type = float
         __min = 0.000000
      }
      
      reactiveMaskScale {
         __type = float
         __min = 0.000000
      }
      
   }
   
}