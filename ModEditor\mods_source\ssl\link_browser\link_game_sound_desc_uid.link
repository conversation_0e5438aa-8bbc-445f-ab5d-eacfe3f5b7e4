__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "sound"
         __path   =   "/*"
         __type   =   "model"
      }
      __path   =   "categories/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "GameSoundDescsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{sound}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

