__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __select   =   {
                     __showOnlyChildren   =   False
                     __format   =   "{value}"
                     __showValue   =   True
                     __path   =   "eventName"
                     __type   =   "model"
                  }
                  __showOnlyChildren   =   True
                  __path   =   "spline/points/*"
                  __type   =   "model"
               }
               __showOnlyChildren   =   True
               __where   =   {
                  __type   =   "xcSECTION_SSL_EVENT"
               }
               __path   =   "sections/*"
               __type   =   "model"
            }
            __showOnlyChildren   =   True
            __format   =   "{type}"
            __showValue   =   False
            __path   =   "tracks/*"
            __type   =   "model"
         }
         __showOnlyChildren   =   True
         __format   =   "{path}"
         __showValue   =   False
         __path   =   "desc/desc/type/tracks/*"
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __format   =   ""
      __where   =   {
         __type   =   "level_sequence"
      }
      __path   =   "elements/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __showValue   =   False
   __modelType   =   "scene_object"
   __type   =   "modelParent"
}
__type   =   "linkBrowser"

