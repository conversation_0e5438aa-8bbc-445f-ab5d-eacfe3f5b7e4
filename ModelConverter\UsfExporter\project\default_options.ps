geom = {
   optimize_pc = true
   share_geometry = true
   build_skin_compound = true
   weld_vtx = 0.001000
   weld_uv = 0.001000
   export_decals = false
   subdivide_vis = false
   subdivide_edge = false
   subdivide_merge = false
   subdivide_merge_count = 200
   disable_tpl_edge = true
   dyn_cont = false
}
advanced = {
   disable_bulldozer = true
   disable_export = false
   disable_resource_manager = false
   disable_shadow_map_converter = false
   memory_file_size = 96
   optim_tpl = true
   dont_run_lsagen = true
   show_lsagen_wnd = false
   disable_scene_optimization = false
   disable_scene_group_optimization = false
   remove_degenerated_faces_havok = true
   export_mtl_in_text_format = false
   optimize_indices_for_tl_cache = true
   export_tpl_data = true
}
compression = {
   compress_verts_tpl = true
   compress_stat_verts = {
      precision = 0.002000
      __type_id = "yes"
   }
   compress_normals = true
   compress_norm_to_4verts = true
   compress_texture = true
}
