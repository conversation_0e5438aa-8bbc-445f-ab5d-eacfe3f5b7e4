# Space Marine 2 - Flamethrower Installation Script
# This script installs the custom flamethrower mod

param(
    [switch]$Test = $false,
    [switch]$Uninstall = $false,
    [switch]$Backup = $true
)

Write-Host "Space Marine 2 - Flamethrower Mod Installer" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

$ModName = "Flamethrower Heavy Weapon"
$ModVersion = "1.0.0"
$LocalPath = "client_pc\root\local"
$BackupPath = "flamethrower_backup"

# Check if Space Marine 2 directory exists
if (-not (Test-Path "client_pc\root")) {
    Write-Error "Space Marine 2 directory structure not found! Make sure you're in the game directory."
    exit 1
}

Write-Host "Installing: $ModName v$ModVersion" -ForegroundColor Cyan

# Create backup if requested
if ($Backup -and -not $Uninstall) {
    Write-Host "Creating backup..." -ForegroundColor Yellow
    
    if (Test-Path $BackupPath) {
        Remove-Item $BackupPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    
    # Backup existing files that might be overwritten
    $FilesToBackup = @(
        "client_pc\root\local\ssl\weapons\*",
        "client_pc\root\local\ssl\status_effects\*"
    )
    
    foreach ($Pattern in $FilesToBackup) {
        $Files = Get-ChildItem -Path $Pattern -ErrorAction SilentlyContinue
        if ($Files) {
            foreach ($File in $Files) {
                $RelativePath = $File.FullName.Replace((Get-Location).Path + "\", "")
                $BackupFile = Join-Path $BackupPath $RelativePath
                $BackupDir = Split-Path $BackupFile -Parent
                
                if (-not (Test-Path $BackupDir)) {
                    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
                }
                
                Copy-Item $File.FullName $BackupFile -Force
                Write-Host "  Backed up: $RelativePath" -ForegroundColor Gray
            }
        }
    }
}

# Uninstall mode
if ($Uninstall) {
    Write-Host "Uninstalling flamethrower mod..." -ForegroundColor Yellow
    
    $FilesToRemove = @(
        "client_pc\root\local\ssl\weapons\flamethrower_library.sso",
        "client_pc\root\local\ssl\weapons\flamethrower_projectile.sso",
        "client_pc\root\local\ssl\status_effects\flamethrower_effects.sso"
    )
    
    foreach ($File in $FilesToRemove) {
        if (Test-Path $File) {
            Remove-Item $File -Force
            Write-Host "  Removed: $File" -ForegroundColor Red
        }
    }
    
    # Restore backup if available
    if (Test-Path $BackupPath) {
        Write-Host "Restoring backup..." -ForegroundColor Yellow
        Copy-Item "$BackupPath\*" . -Recurse -Force
        Remove-Item $BackupPath -Recurse -Force
        Write-Host "Backup restored successfully!" -ForegroundColor Green
    }
    
    Write-Host "Flamethrower mod uninstalled successfully!" -ForegroundColor Green
    exit 0
}

# Installation mode
Write-Host "Installing flamethrower files..." -ForegroundColor Yellow

# Ensure directory structure exists
$RequiredDirs = @(
    "client_pc\root\local\ssl\weapons",
    "client_pc\root\local\ssl\status_effects"
)

foreach ($Dir in $RequiredDirs) {
    if (-not (Test-Path $Dir)) {
        New-Item -ItemType Directory -Path $Dir -Force | Out-Null
        Write-Host "  Created directory: $Dir" -ForegroundColor Green
    }
}

# Verify mod files exist
$ModFiles = @(
    "client_pc\root\local\ssl\weapons\flamethrower_library.sso",
    "client_pc\root\local\ssl\weapons\flamethrower_projectile.sso",
    "client_pc\root\local\ssl\status_effects\flamethrower_effects.sso"
)

$MissingFiles = @()
foreach ($File in $ModFiles) {
    if (-not (Test-Path $File)) {
        $MissingFiles += $File
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Error "Missing mod files:"
    $MissingFiles | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
    Write-Host "Please ensure all flamethrower mod files are present." -ForegroundColor Yellow
    exit 1
}

# Verify file integrity
Write-Host "Verifying mod files..." -ForegroundColor Yellow

foreach ($File in $ModFiles) {
    $Content = Get-Content $File -Raw
    
    # Basic validation
    if ($Content.Length -lt 100) {
        Write-Warning "File $File seems too small, may be corrupted"
    }
    
    if (-not $Content.Contains("__type")) {
        Write-Warning "File $File may not be a valid SSL file"
    }
    
    Write-Host "  Verified: $(Split-Path $File -Leaf)" -ForegroundColor Green
}

# Test mode - just verify installation
if ($Test) {
    Write-Host "Test mode - Installation verified successfully!" -ForegroundColor Green
    Write-Host "Files are ready for use." -ForegroundColor Cyan
    exit 0
}

# Final installation steps
Write-Host "Finalizing installation..." -ForegroundColor Yellow

# Create mod info file
$ModInfo = @{
    Name = $ModName
    Version = $ModVersion
    InstallDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Files = $ModFiles
    Description = "Heavy flamethrower weapon with continuous fire, area damage, and burning effects"
    Author = "Space Marine 2 Modding Community"
}

$ModInfo | ConvertTo-Json | Out-File "flamethrower_mod_info.json" -Encoding UTF8

Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Flamethrower Mod Features:" -ForegroundColor Cyan
Write-Host "  • Continuous flame stream with area damage" -ForegroundColor White
Write-Host "  • Burning status effects that stack and spread" -ForegroundColor White
Write-Host "  • Fear effects on basic enemies" -ForegroundColor White
Write-Host "  • Armor melting on heavily armored foes" -ForegroundColor White
Write-Host "  • Environmental interactions (ignite objects, clear vegetation)" -ForegroundColor White
Write-Host "  • Overheating mechanics with cooling system" -ForegroundColor White
Write-Host "  • 5-tier mastery progression system" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Magenta
Write-Host "1. Launch Space Marine 2" -ForegroundColor White
Write-Host "2. The flamethrower should appear in heavy weapon spawns" -ForegroundColor White
Write-Host "3. Test in Operations or Eternal War modes" -ForegroundColor White
Write-Host "4. Report any issues or bugs" -ForegroundColor White
Write-Host ""
Write-Host "Note: This mod works in offline/PvE modes only!" -ForegroundColor Yellow
Write-Host "Using mods in online play may result in bans." -ForegroundColor Red

# Create quick uninstall script
$UninstallScript = @"
# Quick Uninstall Script for Flamethrower Mod
Write-Host "Uninstalling Flamethrower Mod..." -ForegroundColor Yellow
& ".\install_flamethrower.ps1" -Uninstall
"@

$UninstallScript | Out-File "uninstall_flamethrower.ps1" -Encoding UTF8

Write-Host ""
Write-Host "Installation complete! Use 'uninstall_flamethrower.ps1' to remove the mod." -ForegroundColor Green
