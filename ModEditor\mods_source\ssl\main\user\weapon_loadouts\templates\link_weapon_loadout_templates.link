__request   =   {
   __select   =   {
      __id   =   "mastery"
      __select   =   {
         __id   =   "key"
         __showOnlyChildren   =   False
         __where   =   {
            __name   =   "^(?!.*?_).*$"
            __type   =   "WeaponLoadoutTemplate"
         }
         __onlyChanged   =   False
         __type   =   "modelRecursive"
      }
      __showOnlyChildren   =   False
      __format   =   "{name}"
      __showValue   =   False
      __path   =   "templates/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __family   =   ".ssl"
   __groupByParents   =   False
   __where   =   {
      __name   =   "WeaponLoadoutTemplatesLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{mastery}__{key}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

