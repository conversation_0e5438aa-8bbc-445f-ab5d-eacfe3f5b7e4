__request   =   {
   __select   =   {
      __id   =   "group"
      __select   =   {
         __id   =   "command"
         __path   =   "commands/*"
         __type   =   "model"
      }
      __path   =   "landGroups/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "SwarmCommandsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{group}.{command}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

