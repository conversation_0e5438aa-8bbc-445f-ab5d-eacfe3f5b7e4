__request   =   {
   __select   =   {
      __select   =   {
         __select   =   {
            __select   =   {
               __select   =   {
                  __id   =   "versionUid"
                  __path   =   "versionName"
                  __format   =   "{value}"
                  __showValue   =   True
                  __type   =   "model"
               }
               __showOnlyChildren   =   True
               __path   =   "versions/*"
               __type   =   "model"
            }
            __showOnlyChildren   =   True
            __type   =   "modelParent"
         }
         __path   =   "firearmUid"
         __format   =   "{value}"
         __showValue   =   True
         __type   =   "model"
      }
      __showOnlyChildren   =   True
      __path   =   "firearmsVersions/*"
      __type   =   "model"
   }
   __family   =   ".ssl"
   __where   =   {
      __name   =   "FirearmVersionsLibrary"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{versionUid}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"

