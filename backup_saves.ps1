﻿# Space Marine 2 Save Backup Script
Write-Host "Creating save file backup..." -ForegroundColor Yellow

$BackupDir = "space_marine_2_save_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null

# Backup Steam saves
$SteamSaves = Get-ChildItem -Path "C:\Program Files (x86)\Steam\userdata" -Recurse -Directory -Name "*2183900*" -ErrorAction SilentlyContinue
foreach ($SavePath in $SteamSaves) {
    $Source = "C:\Program Files (x86)\Steam\userdata\$SavePath"
    $Dest = "$BackupDir\steam_$SavePath"
    Copy-Item $Source $Dest -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Backup created: $BackupDir" -ForegroundColor Green
Write-Host "Always backup before modifying saves!" -ForegroundColor Red
