objectInfos   =   [
   {
      properties   =   [
         {
            __type   =   "tpl_markup_object_shape_property_physics"
         }
      ]
      objectId   =   {
         name   =   "rb_wpn"
      }
   },
   {
      properties   =   [
         {
            massFromDensity   =   False
            mass   =   200
            friction   =   0.99
            __type   =   "tpl_markup_object_property_physics"
         }
      ]
      objectId   =   {
         name   =   "body"
      }
   }
]
version   =   1
lodInfo   =   {
   hideDistance   =   150
   maxLodDist   =   [
      5,
      10,
      15,
      20
   ]
}
rendParams   =   {
   allowHashedTransparent   =   True
   allowDissolve   =   True
   allowFresnelHighlight   =   True
   allowCovering   =   True
}
mtlMutators   =   [
   "camo"
]
__type   =   "tpl_markup"
