lodInfo = {
   maxLodDist = [

   ]
}
__type = "tpl_markup"
objSettings = {
   exportedVersion = "OBJ_SETTINGS_EXPORT_VER_VISIBILITY"
   settings = {
      sfx_source_area1 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      sfx_source_area2 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
      sfx_source_area3 = {
         collision = {
            plrMove = false
            abilityProjectile = false
            aiMove = false
            aiExtMove = false
            aiFly = false
            physMove = false
            vhcMove = false
            light = false
            aiLook = false
            wpnBullet = false
            wpnRocket = false
            wpnSfx = false
            camera = false
            __type = "tpl_markup_cdt_flags"
         }
         visibility = {
            visibleDefault = false
            visibleInReflectionCubemap = false
            __type = "tpl_markup_visibility_flags"
         }
         __type = "TplObjSettingsGeom"
      }
   }
   all = {
      visibility = {
         __type = "tpl_markup_visibility_flags"
      }
   }
}
