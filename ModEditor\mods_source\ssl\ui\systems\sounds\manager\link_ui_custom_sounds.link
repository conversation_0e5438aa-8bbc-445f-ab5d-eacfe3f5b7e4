__request   =   {
   __select   =   {
      __id   =   "category"
      __select   =   {
         __id   =   "asset"
         __path   =   "/*"
         __type   =   "model"
      }
      __path   =   "customSounds/*"
      __type   =   "model"
   }
   __showOnlyChildren   =   True
   __groupByParents   =   False
   __where   =   {
      __name   =   "UiProjectSoundManager"
   }
   __type   =   "brand"
}
__resultFormatter   =   {
   __format   =   "{category}.{asset}"
   __type   =   "rf_text"
}
__type   =   "linkBrowser"


